{"version": 3, "file": "stable_metrics.js", "sourceRoot": "", "sources": ["../../src/stable_metrics.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,4GAA4G;AAC5G,2GAA2G;AAC3G,4GAA4G;AAE5G;;;;GAIG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;GAIG;AACH,MAAM,CAAC,MAAM,qDAAqD,GAAG,gDAAyD,CAAC;AAE/H;;;;GAIG;AACH,MAAM,CAAC,MAAM,+CAA+C,GAAG,0CAAmD,CAAC;AAEnH;;;;GAIG;AACH,MAAM,CAAC,MAAM,qDAAqD,GAAG,gDAAyD,CAAC;AAE/H;;;;GAIG;AACH,MAAM,CAAC,MAAM,sDAAsD,GAAG,iDAA0D,CAAC;AAEjI;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;GAIG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;GAIG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;;GAKG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,mBAA4B,CAAC;AAErE;;;;;GAKG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,uBAAgC,CAAC;AAE7E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;GAKG;AACH,MAAM,CAAC,MAAM,wDAAwD,GAAG,mDAA4D,CAAC;AAErI;;;;;GAKG;AACH,MAAM,CAAC,MAAM,0CAA0C,GAAG,qCAA8C,CAAC;AAEzG;;;;;GAKG;AACH,MAAM,CAAC,MAAM,sDAAsD,GAAG,iDAA0D,CAAC;AAEjI;;;;;GAKG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;GAKG;AACH,MAAM,CAAC,MAAM,+BAA+B,GAAG,0BAAmC,CAAC;AAEnF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;;GAKG;AACH,MAAM,CAAC,MAAM,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;GAKG;AACH,MAAM,CAAC,MAAM,sCAAsC,GAAG,iCAA0C,CAAC;AAEjG;;;;;GAKG;AACH,MAAM,CAAC,MAAM,yCAAyC,GAAG,oCAA6C,CAAC;AAEvG;;;;;GAKG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;GAEG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,oBAA6B,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,eAAwB,CAAC;AAE7D;;;;GAIG;AACH,MAAM,CAAC,MAAM,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,cAAuB,CAAC;AAE3D;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAG,sBAA+B,CAAC;AAE3E;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,iBAA0B,CAAC;AAEjE;;GAEG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;GAEG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,kBAA2B,CAAC;AAEnE;;;;GAIG;AACH,MAAM,CAAC,MAAM,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;GAIG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAAG,+BAAwC,CAAC;AAE7F;;;;GAIG;AACH,MAAM,CAAC,MAAM,kCAAkC,GAAG,6BAAsC,CAAC;AAEzF;;;;GAIG;AACH,MAAM,CAAC,MAAM,iCAAiC,GAAG,4BAAqC,CAAC;AAEvF;;;;GAIG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,yBAAkC,CAAC;AAEjF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;GAIG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,gCAAyC,CAAC;AAE/F;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,mCAAmC,GAAG,8BAAuC,CAAC;AAE3F;;;;GAIG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAG,mCAA4C,CAAC;AAErG;;;;GAIG;AACH,MAAM,CAAC,MAAM,yCAAyC,GAAG,oCAA6C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/register/stable/metrics.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n/**\n * Number of exceptions caught by exception handling middleware.\n *\n * @note Meter name: `Microsoft.AspNetCore.Diagnostics`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS = 'aspnetcore.diagnostics.exceptions' as const;\n\n/**\n * Number of requests that are currently active on the server that hold a rate limiting lease.\n *\n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES = 'aspnetcore.rate_limiting.active_request_leases' as const;\n\n/**\n * Number of requests that are currently queued, waiting to acquire a rate limiting lease.\n *\n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS = 'aspnetcore.rate_limiting.queued_requests' as const;\n\n/**\n * The time the request spent in a queue waiting to acquire a rate limiting lease.\n *\n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE = 'aspnetcore.rate_limiting.request.time_in_queue' as const;\n\n/**\n * The duration of rate limiting lease held by requests on the server.\n *\n * @note Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION = 'aspnetcore.rate_limiting.request_lease.duration' as const;\n\n/**\n * Number of requests that tried to acquire a rate limiting lease.\n *\n * @note Requests could be:\n *\n *   - Rejected by global or endpoint rate limiting policies\n *   - Canceled while waiting for the lease.\n *\n * Meter name: `Microsoft.AspNetCore.RateLimiting`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS = 'aspnetcore.rate_limiting.requests' as const;\n\n/**\n * Number of requests that were attempted to be matched to an endpoint.\n *\n * @note Meter name: `Microsoft.AspNetCore.Routing`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS = 'aspnetcore.routing.match_attempts' as const;\n\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n */\nexport const METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration' as const;\n\n/**\n * The number of .NET assemblies that are currently loaded.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`AppDomain.CurrentDomain.GetAssemblies().Length`](https://learn.microsoft.com/dotnet/api/system.appdomain.getassemblies).\n */\nexport const METRIC_DOTNET_ASSEMBLY_COUNT = 'dotnet.assembly.count' as const;\n\n/**\n * The number of exceptions that have been thrown in managed code.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as counting calls to [`AppDomain.CurrentDomain.FirstChanceException`](https://learn.microsoft.com/dotnet/api/system.appdomain.firstchanceexception).\n */\nexport const METRIC_DOTNET_EXCEPTIONS = 'dotnet.exceptions' as const;\n\n/**\n * The number of garbage collections that have occurred since the process has started.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric uses the [`GC.CollectionCount(int generation)`](https://learn.microsoft.com/dotnet/api/system.gc.collectioncount) API to calculate exclusive collections per generation.\n */\nexport const METRIC_DOTNET_GC_COLLECTIONS = 'dotnet.gc.collections' as const;\n\n/**\n * The *approximate* number of bytes allocated on the managed GC heap since the process has started. The returned value does not include any native allocations.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetTotalAllocatedBytes()`](https://learn.microsoft.com/dotnet/api/system.gc.gettotalallocatedbytes).\n */\nexport const METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED = 'dotnet.gc.heap.total_allocated' as const;\n\n/**\n * The heap fragmentation, as observed during the latest garbage collection.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetGCMemoryInfo().GenerationInfo.FragmentationAfterBytes`](https://learn.microsoft.com/dotnet/api/system.gcgenerationinfo.fragmentationafterbytes).\n */\nexport const METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE = 'dotnet.gc.last_collection.heap.fragmentation.size' as const;\n\n/**\n * The managed GC heap size (including fragmentation), as observed during the latest garbage collection.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetGCMemoryInfo().GenerationInfo.SizeAfterBytes`](https://learn.microsoft.com/dotnet/api/system.gcgenerationinfo.sizeafterbytes).\n */\nexport const METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE = 'dotnet.gc.last_collection.heap.size' as const;\n\n/**\n * The amount of committed virtual memory in use by the .NET GC, as observed during the latest garbage collection.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetGCMemoryInfo().TotalCommittedBytes`](https://learn.microsoft.com/dotnet/api/system.gcmemoryinfo.totalcommittedbytes). Committed virtual memory may be larger than the heap size because it includes both memory for storing existing objects (the heap size) and some extra memory that is ready to handle newly allocated objects in the future.\n */\nexport const METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE = 'dotnet.gc.last_collection.memory.committed_size' as const;\n\n/**\n * The total amount of time paused in GC since the process has started.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`GC.GetTotalPauseDuration()`](https://learn.microsoft.com/dotnet/api/system.gc.gettotalpauseduration).\n */\nexport const METRIC_DOTNET_GC_PAUSE_TIME = 'dotnet.gc.pause.time' as const;\n\n/**\n * The amount of time the JIT compiler has spent compiling methods since the process has started.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`JitInfo.GetCompilationTime()`](https://learn.microsoft.com/dotnet/api/system.runtime.jitinfo.getcompilationtime).\n */\nexport const METRIC_DOTNET_JIT_COMPILATION_TIME = 'dotnet.jit.compilation.time' as const;\n\n/**\n * Count of bytes of intermediate language that have been compiled since the process has started.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`JitInfo.GetCompiledILBytes()`](https://learn.microsoft.com/dotnet/api/system.runtime.jitinfo.getcompiledilbytes).\n */\nexport const METRIC_DOTNET_JIT_COMPILED_IL_SIZE = 'dotnet.jit.compiled_il.size' as const;\n\n/**\n * The number of times the JIT compiler (re)compiled methods since the process has started.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`JitInfo.GetCompiledMethodCount()`](https://learn.microsoft.com/dotnet/api/system.runtime.jitinfo.getcompiledmethodcount).\n */\nexport const METRIC_DOTNET_JIT_COMPILED_METHODS = 'dotnet.jit.compiled_methods' as const;\n\n/**\n * The number of times there was contention when trying to acquire a monitor lock since the process has started.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`Monitor.LockContentionCount`](https://learn.microsoft.com/dotnet/api/system.threading.monitor.lockcontentioncount).\n */\nexport const METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS = 'dotnet.monitor.lock_contentions' as const;\n\n/**\n * The number of processors available to the process.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as accessing [`Environment.ProcessorCount`](https://learn.microsoft.com/dotnet/api/system.environment.processorcount).\n */\nexport const METRIC_DOTNET_PROCESS_CPU_COUNT = 'dotnet.process.cpu.count' as const;\n\n/**\n * CPU time used by the process.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as accessing the corresponding processor time properties on [`System.Diagnostics.Process`](https://learn.microsoft.com/dotnet/api/system.diagnostics.process).\n */\nexport const METRIC_DOTNET_PROCESS_CPU_TIME = 'dotnet.process.cpu.time' as const;\n\n/**\n * The number of bytes of physical memory mapped to the process context.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`Environment.WorkingSet`](https://learn.microsoft.com/dotnet/api/system.environment.workingset).\n */\nexport const METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET = 'dotnet.process.memory.working_set' as const;\n\n/**\n * The number of work items that are currently queued to be processed by the thread pool.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`ThreadPool.PendingWorkItemCount`](https://learn.microsoft.com/dotnet/api/system.threading.threadpool.pendingworkitemcount).\n */\nexport const METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH = 'dotnet.thread_pool.queue.length' as const;\n\n/**\n * The number of thread pool threads that currently exist.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`ThreadPool.ThreadCount`](https://learn.microsoft.com/dotnet/api/system.threading.threadpool.threadcount).\n */\nexport const METRIC_DOTNET_THREAD_POOL_THREAD_COUNT = 'dotnet.thread_pool.thread.count' as const;\n\n/**\n * The number of work items that the thread pool has completed since the process has started.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`ThreadPool.CompletedWorkItemCount`](https://learn.microsoft.com/dotnet/api/system.threading.threadpool.completedworkitemcount).\n */\nexport const METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT = 'dotnet.thread_pool.work_item.count' as const;\n\n/**\n * The number of timer instances that are currently active.\n *\n * @note Meter name: `System.Runtime`; Added in: .NET 9.0.\n * This metric reports the same values as calling [`Timer.ActiveCount`](https://learn.microsoft.com/dotnet/api/system.threading.timer.activecount).\n */\nexport const METRIC_DOTNET_TIMER_COUNT = 'dotnet.timer.count' as const;\n\n/**\n * Duration of HTTP client requests.\n */\nexport const METRIC_HTTP_CLIENT_REQUEST_DURATION = 'http.client.request.duration' as const;\n\n/**\n * Duration of HTTP server requests.\n */\nexport const METRIC_HTTP_SERVER_REQUEST_DURATION = 'http.server.request.duration' as const;\n\n/**\n * Number of classes currently loaded.\n */\nexport const METRIC_JVM_CLASS_COUNT = 'jvm.class.count' as const;\n\n/**\n * Number of classes loaded since JVM start.\n */\nexport const METRIC_JVM_CLASS_LOADED = 'jvm.class.loaded' as const;\n\n/**\n * Number of classes unloaded since JVM start.\n */\nexport const METRIC_JVM_CLASS_UNLOADED = 'jvm.class.unloaded' as const;\n\n/**\n * Number of processors available to the Java virtual machine.\n */\nexport const METRIC_JVM_CPU_COUNT = 'jvm.cpu.count' as const;\n\n/**\n * Recent CPU utilization for the process as reported by the JVM.\n *\n * @note The value range is [0.0,1.0]. This utilization is not defined as being for the specific interval since last measurement (unlike `system.cpu.utilization`). [Reference](https://docs.oracle.com/en/java/javase/17/docs/api/jdk.management/com/sun/management/OperatingSystemMXBean.html#getProcessCpuLoad()).\n */\nexport const METRIC_JVM_CPU_RECENT_UTILIZATION = 'jvm.cpu.recent_utilization' as const;\n\n/**\n * CPU time used by the process as reported by the JVM.\n */\nexport const METRIC_JVM_CPU_TIME = 'jvm.cpu.time' as const;\n\n/**\n * Duration of JVM garbage collection actions.\n */\nexport const METRIC_JVM_GC_DURATION = 'jvm.gc.duration' as const;\n\n/**\n * Measure of memory committed.\n */\nexport const METRIC_JVM_MEMORY_COMMITTED = 'jvm.memory.committed' as const;\n\n/**\n * Measure of max obtainable memory.\n */\nexport const METRIC_JVM_MEMORY_LIMIT = 'jvm.memory.limit' as const;\n\n/**\n * Measure of memory used.\n */\nexport const METRIC_JVM_MEMORY_USED = 'jvm.memory.used' as const;\n\n/**\n * Measure of memory used, as measured after the most recent garbage collection event on this pool.\n */\nexport const METRIC_JVM_MEMORY_USED_AFTER_LAST_GC = 'jvm.memory.used_after_last_gc' as const;\n\n/**\n * Number of executing platform threads.\n */\nexport const METRIC_JVM_THREAD_COUNT = 'jvm.thread.count' as const;\n\n/**\n * Number of connections that are currently active on the server.\n *\n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_ACTIVE_CONNECTIONS = 'kestrel.active_connections' as const;\n\n/**\n * Number of TLS handshakes that are currently in progress on the server.\n *\n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES = 'kestrel.active_tls_handshakes' as const;\n\n/**\n * The duration of connections on the server.\n *\n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_CONNECTION_DURATION = 'kestrel.connection.duration' as const;\n\n/**\n * Number of connections that are currently queued and are waiting to start.\n *\n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_QUEUED_CONNECTIONS = 'kestrel.queued_connections' as const;\n\n/**\n * Number of HTTP requests on multiplexed connections (HTTP/2 and HTTP/3) that are currently queued and are waiting to start.\n *\n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_QUEUED_REQUESTS = 'kestrel.queued_requests' as const;\n\n/**\n * Number of connections rejected by the server.\n *\n * @note Connections are rejected when the currently active count exceeds the value configured with `MaxConcurrentConnections`.\n * Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_REJECTED_CONNECTIONS = 'kestrel.rejected_connections' as const;\n\n/**\n * The duration of TLS handshakes on the server.\n *\n * @note Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_TLS_HANDSHAKE_DURATION = 'kestrel.tls_handshake.duration' as const;\n\n/**\n * Number of connections that are currently upgraded (WebSockets). .\n *\n * @note The counter only tracks HTTP/1.1 connections.\n *\n * Meter name: `Microsoft.AspNetCore.Server.Kestrel`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_KESTREL_UPGRADED_CONNECTIONS = 'kestrel.upgraded_connections' as const;\n\n/**\n * Number of connections that are currently active on the server.\n *\n * @note Meter name: `Microsoft.AspNetCore.Http.Connections`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS = 'signalr.server.active_connections' as const;\n\n/**\n * The duration of connections on the server.\n *\n * @note Meter name: `Microsoft.AspNetCore.Http.Connections`; Added in: ASP.NET Core 8.0\n */\nexport const METRIC_SIGNALR_SERVER_CONNECTION_DURATION = 'signalr.server.connection.duration' as const;\n\n"]}