{"version": 3, "sources": ["../../../../src/ui/contexts/OverflowContext.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\n\nexport interface OverflowState {\n  isOverflowing: boolean;\n  maxLines: number;\n  currentLines: number;\n  showMoreAvailable: boolean;\n}\n\nexport interface OverflowContextValue {\n  overflow: OverflowState;\n  setOverflow: (state: OverflowState) => void;\n  setMaxLines: (maxLines: number) => void;\n  setCurrentLines: (currentLines: number) => void;\n  toggleShowMore: () => void;\n  resetOverflow: () => void;\n}\n\nexport interface OverflowActionsValue {\n  addOverflowingId: (id: string) => void;\n  removeOverflowingId: (id: string) => void;\n  getOverflowingIds: () => string[];\n}\n\nconst OverflowContext = createContext<OverflowContextValue | undefined>(undefined);\nconst OverflowActionsContext = createContext<OverflowActionsValue | undefined>(undefined);\n\ninterface OverflowProviderProps {\n  children: ReactNode;\n  defaultMaxLines?: number;\n}\n\nexport const OverflowProvider: React.FC<OverflowProviderProps> = ({\n  children,\n  defaultMaxLines = 20\n}) => {\n  const [overflow, setOverflowState] = useState<OverflowState>({\n    isOverflowing: false,\n    maxLines: defaultMaxLines,\n    currentLines: 0,\n    showMoreAvailable: false,\n  });\n\n  const [overflowingIds, setOverflowingIds] = useState<Set<string>>(new Set());\n\n  const setOverflow = (state: OverflowState) => {\n    setOverflowState(state);\n  };\n\n  const setMaxLines = (maxLines: number) => {\n    setOverflowState(prev => ({\n      ...prev,\n      maxLines,\n      isOverflowing: prev.currentLines > maxLines,\n    }));\n  };\n\n  const setCurrentLines = (currentLines: number) => {\n    setOverflowState(prev => ({\n      ...prev,\n      currentLines,\n      isOverflowing: currentLines > prev.maxLines,\n      showMoreAvailable: currentLines > prev.maxLines,\n    }));\n  };\n\n  const toggleShowMore = () => {\n    setOverflowState(prev => ({\n      ...prev,\n      maxLines: prev.isOverflowing ? prev.currentLines : Math.min(prev.maxLines, 20),\n      isOverflowing: !prev.isOverflowing,\n    }));\n  };\n\n  const resetOverflow = () => {\n    setOverflowState({\n      isOverflowing: false,\n      maxLines: defaultMaxLines,\n      currentLines: 0,\n      showMoreAvailable: false,\n    });\n  };\n\n  // Overflow actions\n  const addOverflowingId = (id: string) => {\n    setOverflowingIds(prev => new Set([...prev, id]));\n  };\n\n  const removeOverflowingId = (id: string) => {\n    setOverflowingIds(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(id);\n      return newSet;\n    });\n  };\n\n  const getOverflowingIds = () => {\n    return Array.from(overflowingIds);\n  };\n\n  const value: OverflowContextValue = {\n    overflow,\n    setOverflow,\n    setMaxLines,\n    setCurrentLines,\n    toggleShowMore,\n    resetOverflow,\n  };\n\n  const actionsValue: OverflowActionsValue = {\n    addOverflowingId,\n    removeOverflowingId,\n    getOverflowingIds,\n  };\n\n  return (\n    <OverflowContext.Provider value={value}>\n      <OverflowActionsContext.Provider value={actionsValue}>\n        {children}\n      </OverflowActionsContext.Provider>\n    </OverflowContext.Provider>\n  );\n};\n\nexport const useOverflow = (): OverflowContextValue => {\n  const context = useContext(OverflowContext);\n  if (!context) {\n    throw new Error('useOverflow must be used within an OverflowProvider');\n  }\n  return context;\n};\n\nexport const useOverflowActions = (): OverflowActionsValue => {\n  const context = useContext(OverflowActionsContext);\n  if (!context) {\n    throw new Error('useOverflowActions must be used within an OverflowProvider');\n  }\n  return context;\n};\n"], "mappings": "AA2HM;AA3HN;AAAA;AAAA;AAAA;AAAA;AAMA,SAAgB,eAAe,YAAY,gBAA2B;AAwBtE,MAAM,kBAAkB,cAAgD,MAAS;AACjF,MAAM,yBAAyB,cAAgD,MAAS;AAOjF,MAAM,mBAAoD,CAAC;AAAA,EAChE;AAAA,EACA,kBAAkB;AACpB,MAAM;AACJ,QAAM,CAAC,UAAU,gBAAgB,IAAI,SAAwB;AAAA,IAC3D,eAAe;AAAA,IACf,UAAU;AAAA,IACV,cAAc;AAAA,IACd,mBAAmB;AAAA,EACrB,CAAC;AAED,QAAM,CAAC,gBAAgB,iBAAiB,IAAI,SAAsB,oBAAI,IAAI,CAAC;AAE3E,QAAM,cAAc,CAAC,UAAyB;AAC5C,qBAAiB,KAAK;AAAA,EACxB;AAEA,QAAM,cAAc,CAAC,aAAqB;AACxC,qBAAiB,WAAS;AAAA,MACxB,GAAG;AAAA,MACH;AAAA,MACA,eAAe,KAAK,eAAe;AAAA,IACrC,EAAE;AAAA,EACJ;AAEA,QAAM,kBAAkB,CAAC,iBAAyB;AAChD,qBAAiB,WAAS;AAAA,MACxB,GAAG;AAAA,MACH;AAAA,MACA,eAAe,eAAe,KAAK;AAAA,MACnC,mBAAmB,eAAe,KAAK;AAAA,IACzC,EAAE;AAAA,EACJ;AAEA,QAAM,iBAAiB,MAAM;AAC3B,qBAAiB,WAAS;AAAA,MACxB,GAAG;AAAA,MACH,UAAU,KAAK,gBAAgB,KAAK,eAAe,KAAK,IAAI,KAAK,UAAU,EAAE;AAAA,MAC7E,eAAe,CAAC,KAAK;AAAA,IACvB,EAAE;AAAA,EACJ;AAEA,QAAM,gBAAgB,MAAM;AAC1B,qBAAiB;AAAA,MACf,eAAe;AAAA,MACf,UAAU;AAAA,MACV,cAAc;AAAA,MACd,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAGA,QAAM,mBAAmB,CAAC,OAAe;AACvC,sBAAkB,UAAQ,oBAAI,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC;AAAA,EAClD;AAEA,QAAM,sBAAsB,CAAC,OAAe;AAC1C,sBAAkB,UAAQ;AACxB,YAAM,SAAS,IAAI,IAAI,IAAI;AAC3B,aAAO,OAAO,EAAE;AAChB,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,QAAM,oBAAoB,MAAM;AAC9B,WAAO,MAAM,KAAK,cAAc;AAAA,EAClC;AAEA,QAAM,QAA8B;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,eAAqC;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SACE,oBAAC,gBAAgB,UAAhB,EAAyB,OACxB,8BAAC,uBAAuB,UAAvB,EAAgC,OAAO,cACrC,UACH,GACF;AAEJ;AAEO,MAAM,cAAc,MAA4B;AACrD,QAAM,UAAU,WAAW,eAAe;AAC1C,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,qDAAqD;AAAA,EACvE;AACA,SAAO;AACT;AAEO,MAAM,qBAAqB,MAA4B;AAC5D,QAAM,UAAU,WAAW,sBAAsB;AACjD,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,4DAA4D;AAAA,EAC9E;AACA,SAAO;AACT;", "names": []}