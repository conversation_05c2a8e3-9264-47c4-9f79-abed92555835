/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import {
  ToolCallRequestInfo,
  ToolCallResponseInfo,
  ToolConfirmationOutcome,
  Tool,
  ToolCallConfirmationDetails,
  ToolResult,
  ToolRegistry,
  ApprovalMode,
  EditorType,
  Config,
  logToolCall,
  ToolCallEvent,
} from '../index.js';
import { Part, PartListUnion } from '@google/genai';
import { getResponseTextFromParts } from '../utils/generateContentResponseUtilities.js';
import {
  isModifiableTool,
  ModifyContext,
  modifyWithEditor,
} from '../tools/modifiable-tool.js';

export type ValidatingToolCall = {
  status: 'validating';
  request: ToolCallRequestInfo;
  tool: Tool;
  startTime?: number;
  outcome?: ToolConfirmationOutcome;
};

export type ScheduledToolCall = {
  status: 'scheduled';
  request: ToolCallRequestInfo;
  tool: Tool;
  startTime?: number;
  outcome?: ToolConfirmationOutcome;
};

export type ErroredToolCall = {
  status: 'error';
  request: ToolCallRequestInfo;
  response: ToolCallResponseInfo;
  durationMs?: number;
  outcome?: ToolConfirmationOutcome;
};

export type SuccessfulToolCall = {
  status: 'success';
  request: ToolCallRequestInfo;
  tool: Tool;
  response: ToolCallResponseInfo;
  durationMs?: number;
  outcome?: ToolConfirmationOutcome;
};

export type ExecutingToolCall = {
  status: 'executing';
  request: ToolCallRequestInfo;
  tool: Tool;
  liveOutput?: string;
  startTime?: number;
  outcome?: ToolConfirmationOutcome;
};

export type CancelledToolCall = {
  status: 'cancelled';
  request: ToolCallRequestInfo;
  response: ToolCallResponseInfo;
  tool: Tool;
  durationMs?: number;
  outcome?: ToolConfirmationOutcome;
};

export type WaitingToolCall = {
  status: 'awaiting_approval';
  request: ToolCallRequestInfo;
  tool: Tool;
  confirmationDetails: ToolCallConfirmationDetails;
  startTime?: number;
  outcome?: ToolConfirmationOutcome;
};

export type Status = ToolCall['status'];

export type ToolCall =
  | ValidatingToolCall
  | ScheduledToolCall
  | ErroredToolCall
  | SuccessfulToolCall
  | ExecutingToolCall
  | CancelledToolCall
  | WaitingToolCall;

export type CompletedToolCall =
  | ErroredToolCall
  | SuccessfulToolCall
  | CancelledToolCall;

export interface ToolSchedulerCallbacks {
  onToolCallUpdate?: (toolCall: ToolCall) => void;
  onToolCallComplete?: (toolCall: CompletedToolCall) => void;
  onAllToolCallsComplete?: (toolCalls: CompletedToolCall[]) => void;
  onToolCallConfirmation?: (
    toolCall: WaitingToolCall,
  ) => Promise<ToolConfirmationOutcome>;
}

/**
 * Core tool scheduler for managing tool execution
 */
export class CoreToolScheduler {
  private toolCalls: Map<string, ToolCall> = new Map();
  private callbacks: ToolSchedulerCallbacks;
  private config: Config;
  private toolRegistry: ToolRegistry;

  constructor(
    config: Config,
    toolRegistry: ToolRegistry,
    callbacks: ToolSchedulerCallbacks = {},
  ) {
    this.config = config;
    this.toolRegistry = toolRegistry;
    this.callbacks = callbacks;
  }

  /**
   * Schedule tool calls for execution
   */
  async scheduleToolCalls(
    toolCallRequests: ToolCallRequestInfo[],
  ): Promise<ToolCallResponseInfo[]> {
    const responses: ToolCallResponseInfo[] = [];

    for (const request of toolCallRequests) {
      try {
        const tool = await this.toolRegistry.getTool(request.name);
        if (!tool) {
          const errorResponse: ToolCallResponseInfo = {
            name: request.name,
            response: {
              error: `Tool '${request.name}' not found`,
            },
          };
          responses.push(errorResponse);
          continue;
        }

        const toolCall: ScheduledToolCall = {
          status: 'scheduled',
          request,
          tool,
          startTime: Date.now(),
        };

        this.toolCalls.set(request.name, toolCall);
        this.callbacks.onToolCallUpdate?.(toolCall);

        const response = await this.executeToolCall(toolCall);
        responses.push(response);
      } catch (error) {
        const errorResponse: ToolCallResponseInfo = {
          name: request.name,
          response: {
            error: error instanceof Error ? error.message : String(error),
          },
        };
        responses.push(errorResponse);
      }
    }

    return responses;
  }

  /**
   * Execute a single tool call
   */
  private async executeToolCall(
    toolCall: ScheduledToolCall,
  ): Promise<ToolCallResponseInfo> {
    const executingToolCall: ExecutingToolCall = {
      ...toolCall,
      status: 'executing',
    };

    this.toolCalls.set(toolCall.request.name, executingToolCall);
    this.callbacks.onToolCallUpdate?.(executingToolCall);

    try {
      const result = await toolCall.tool.execute(
        toolCall.request.args,
        this.config,
      );

      const successfulToolCall: SuccessfulToolCall = {
        ...executingToolCall,
        status: 'success',
        response: {
          name: toolCall.request.name,
          response: result,
        },
        durationMs: Date.now() - (toolCall.startTime || 0),
      };

      this.toolCalls.set(toolCall.request.name, successfulToolCall);
      this.callbacks.onToolCallUpdate?.(successfulToolCall);
      this.callbacks.onToolCallComplete?.(successfulToolCall);

      return successfulToolCall.response;
    } catch (error) {
      const erroredToolCall: ErroredToolCall = {
        ...executingToolCall,
        status: 'error',
        response: {
          name: toolCall.request.name,
          response: {
            error: error instanceof Error ? error.message : String(error),
          },
        },
        durationMs: Date.now() - (toolCall.startTime || 0),
      };

      this.toolCalls.set(toolCall.request.name, erroredToolCall);
      this.callbacks.onToolCallUpdate?.(erroredToolCall);
      this.callbacks.onToolCallComplete?.(erroredToolCall);

      return erroredToolCall.response;
    }
  }

  /**
   * Get all tool calls
   */
  getToolCalls(): ToolCall[] {
    return Array.from(this.toolCalls.values());
  }

  /**
   * Get a specific tool call by name
   */
  getToolCall(name: string): ToolCall | undefined {
    return this.toolCalls.get(name);
  }

  /**
   * Clear all tool calls
   */
  clear(): void {
    this.toolCalls.clear();
  }
}
