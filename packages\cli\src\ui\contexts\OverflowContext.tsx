/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface OverflowState {
  isOverflowing: boolean;
  maxLines: number;
  currentLines: number;
  showMoreAvailable: boolean;
}

export interface OverflowContextValue {
  overflow: OverflowState;
  setOverflow: (state: OverflowState) => void;
  setMaxLines: (maxLines: number) => void;
  setCurrentLines: (currentLines: number) => void;
  toggleShowMore: () => void;
  resetOverflow: () => void;
}

export interface OverflowActionsValue {
  addOverflowingId: (id: string) => void;
  removeOverflowingId: (id: string) => void;
  getOverflowingIds: () => string[];
}

const OverflowContext = createContext<OverflowContextValue | undefined>(undefined);
const OverflowActionsContext = createContext<OverflowActionsValue | undefined>(undefined);

interface OverflowProviderProps {
  children: ReactNode;
  defaultMaxLines?: number;
}

export const OverflowProvider: React.FC<OverflowProviderProps> = ({
  children,
  defaultMaxLines = 20
}) => {
  const [overflow, setOverflowState] = useState<OverflowState>({
    isOverflowing: false,
    maxLines: defaultMaxLines,
    currentLines: 0,
    showMoreAvailable: false,
  });

  const [overflowingIds, setOverflowingIds] = useState<Set<string>>(new Set());

  const setOverflow = (state: OverflowState) => {
    setOverflowState(state);
  };

  const setMaxLines = (maxLines: number) => {
    setOverflowState(prev => ({
      ...prev,
      maxLines,
      isOverflowing: prev.currentLines > maxLines,
    }));
  };

  const setCurrentLines = (currentLines: number) => {
    setOverflowState(prev => ({
      ...prev,
      currentLines,
      isOverflowing: currentLines > prev.maxLines,
      showMoreAvailable: currentLines > prev.maxLines,
    }));
  };

  const toggleShowMore = () => {
    setOverflowState(prev => ({
      ...prev,
      maxLines: prev.isOverflowing ? prev.currentLines : Math.min(prev.maxLines, 20),
      isOverflowing: !prev.isOverflowing,
    }));
  };

  const resetOverflow = () => {
    setOverflowState({
      isOverflowing: false,
      maxLines: defaultMaxLines,
      currentLines: 0,
      showMoreAvailable: false,
    });
  };

  // Overflow actions
  const addOverflowingId = (id: string) => {
    setOverflowingIds(prev => new Set([...prev, id]));
  };

  const removeOverflowingId = (id: string) => {
    setOverflowingIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const getOverflowingIds = () => {
    return Array.from(overflowingIds);
  };

  const value: OverflowContextValue = {
    overflow,
    setOverflow,
    setMaxLines,
    setCurrentLines,
    toggleShowMore,
    resetOverflow,
  };

  const actionsValue: OverflowActionsValue = {
    addOverflowingId,
    removeOverflowingId,
    getOverflowingIds,
  };

  return (
    <OverflowContext.Provider value={value}>
      <OverflowActionsContext.Provider value={actionsValue}>
        {children}
      </OverflowActionsContext.Provider>
    </OverflowContext.Provider>
  );
};

export const useOverflow = (): OverflowContextValue => {
  const context = useContext(OverflowContext);
  if (!context) {
    throw new Error('useOverflow must be used within an OverflowProvider');
  }
  return context;
};

export const useOverflowActions = (): OverflowActionsValue => {
  const context = useContext(OverflowActionsContext);
  if (!context) {
    throw new Error('useOverflowActions must be used within an OverflowProvider');
  }
  return context;
};
