{"version": 3, "sources": ["../../../src/ui/App.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { useState, useCallback } from 'react';\nimport { Box, Text, useInput, useApp } from 'ink';\nimport { Config } from '@arien/arien-cli-core';\n\ninterface AppProps {\n  config: Config;\n  settings: Config; // Now using unified Config instead of LoadedSettings\n  startupWarnings?: string[];\n}\n\nexport const AppWrapper = (props: AppProps) => {\n  return <App {...props} />;\n};\n\nconst App = ({ config, settings, startupWarnings = [] }: AppProps) => {\n  // Suppress unused parameter warnings\n  void config;\n  void settings;\n  void startupWarnings;\n  // State\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [messages, setMessages] = useState<Array<{ type: string; text: string; id: string }>>([]);\n\n  // Hooks\n  const { exit } = useApp();\n\n  // Input handling\n  const handleInput = useCallback(async (inputText: string) => {\n    if (!inputText.trim()) return;\n\n    setInput('');\n    setIsLoading(true);\n\n    try {\n      // Add user input to messages\n      const userMessage = {\n        type: 'user',\n        text: inputText,\n        id: Date.now().toString(),\n      };\n      setMessages(prev => [...prev, userMessage]);\n\n      // Process input (placeholder for now)\n      if (inputText.startsWith('/help')) {\n        const helpMessage = {\n          type: 'system',\n          text: 'Available commands:\\n/help - Show this help\\n/clear - Clear history\\n/exit - Exit application',\n          id: (Date.now() + 1).toString(),\n        };\n        setMessages(prev => [...prev, helpMessage]);\n      } else if (inputText.startsWith('/clear')) {\n        setMessages([]);\n      } else if (inputText.startsWith('/exit')) {\n        exit();\n      } else {\n        // Echo response\n        const echoMessage = {\n          type: 'assistant',\n          text: `Echo: ${inputText}`,\n          id: (Date.now() + 1).toString(),\n        };\n        setMessages(prev => [...prev, echoMessage]);\n      }\n    } catch (error) {\n      const errorMessage = {\n        type: 'error',\n        text: `Error: ${error}`,\n        id: (Date.now() + 1).toString(),\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [exit]);\n\n  // Handle keyboard input\n  useInput((input, key) => {\n    if (key.return) {\n      handleInput(input);\n      setInput('');\n    } else if (key.ctrl && input === 'c') {\n      exit();\n    } else if (!key.ctrl && !key.meta && input.length === 1) {\n      setInput(prev => prev + input);\n    } else if (key.backspace || key.delete) {\n      setInput(prev => prev.slice(0, -1));\n    }\n  });\n\n  return (\n    <Box flexDirection=\"column\" height=\"100%\">\n      {/* Header */}\n      <Box borderStyle=\"single\" paddingX={1}>\n        <Text color=\"cyan\" bold>\n          🤖 Arien CLI - AI Assistant for Coding\n        </Text>\n      </Box>\n\n      {/* Messages area */}\n      <Box flexDirection=\"column\" flexGrow={1} paddingX={1}>\n        {messages.map((message) => (\n          <Box key={message.id} marginY={0}>\n            <Text color={\n              message.type === 'user' ? 'green' :\n              message.type === 'error' ? 'red' :\n              message.type === 'system' ? 'yellow' :\n              'white'\n            }>\n              {message.type === 'user' ? '> ' : ''}\n              {message.text}\n            </Text>\n          </Box>\n        ))}\n\n        {isLoading && (\n          <Box>\n            <Text color=\"gray\">Processing...</Text>\n          </Box>\n        )}\n      </Box>\n\n      {/* Input area */}\n      <Box borderStyle=\"single\" paddingX={1}>\n        <Text color=\"blue\">{'> '}</Text>\n        <Text>{input}</Text>\n        <Text color=\"gray\">█</Text>\n      </Box>\n\n      {/* Footer */}\n      <Box paddingX={1}>\n        <Text color=\"gray\" dimColor>\n          Type /help for commands, /exit to quit, Ctrl+C to exit\n        </Text>\n      </Box>\n    </Box>\n  );\n};\n\nexport default App;\n"], "mappings": "AAiBS,cA4FG,YA5FH;AAjBT;AAAA;AAAA;AAAA;AAAA;AAMA,SAAgB,UAAU,mBAAmB;AAC7C,SAAS,KAAK,MAAM,UAAU,cAAc;AASrC,MAAM,aAAa,CAAC,UAAoB;AAC7C,SAAO,oBAAC,OAAK,GAAG,OAAO;AACzB;AAEA,MAAM,MAAM,CAAC,EAAE,QAAQ,UAAU,kBAAkB,CAAC,EAAE,MAAgB;AAEpE,OAAK;AACL,OAAK;AACL,OAAK;AAEL,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAS,EAAE;AACrC,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,KAAK;AAChD,QAAM,CAAC,UAAU,WAAW,IAAI,SAA4D,CAAC,CAAC;AAG9F,QAAM,EAAE,KAAK,IAAI,OAAO;AAGxB,QAAM,cAAc,YAAY,OAAO,cAAsB;AAC3D,QAAI,CAAC,UAAU,KAAK,EAAG;AAEvB,aAAS,EAAE;AACX,iBAAa,IAAI;AAEjB,QAAI;AAEF,YAAM,cAAc;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,MAC1B;AACA,kBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAG1C,UAAI,UAAU,WAAW,OAAO,GAAG;AACjC,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;AAAA,QAChC;AACA,oBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAAA,MAC5C,WAAW,UAAU,WAAW,QAAQ,GAAG;AACzC,oBAAY,CAAC,CAAC;AAAA,MAChB,WAAW,UAAU,WAAW,OAAO,GAAG;AACxC,aAAK;AAAA,MACP,OAAO;AAEL,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM,SAAS,SAAS;AAAA,UACxB,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;AAAA,QAChC;AACA,oBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAAA,MAC5C;AAAA,IACF,SAAS,OAAO;AACd,YAAM,eAAe;AAAA,QACnB,MAAM;AAAA,QACN,MAAM,UAAU,KAAK;AAAA,QACrB,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;AAAA,MAChC;AACA,kBAAY,UAAQ,CAAC,GAAG,MAAM,YAAY,CAAC;AAAA,IAC7C,UAAE;AACA,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AAGT,WAAS,CAACA,QAAO,QAAQ;AACvB,QAAI,IAAI,QAAQ;AACd,kBAAYA,MAAK;AACjB,eAAS,EAAE;AAAA,IACb,WAAW,IAAI,QAAQA,WAAU,KAAK;AACpC,WAAK;AAAA,IACP,WAAW,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQA,OAAM,WAAW,GAAG;AACvD,eAAS,UAAQ,OAAOA,MAAK;AAAA,IAC/B,WAAW,IAAI,aAAa,IAAI,QAAQ;AACtC,eAAS,UAAQ,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,IACpC;AAAA,EACF,CAAC;AAED,SACE,qBAAC,OAAI,eAAc,UAAS,QAAO,QAEjC;AAAA,wBAAC,OAAI,aAAY,UAAS,UAAU,GAClC,8BAAC,QAAK,OAAM,QAAO,MAAI,MAAC,2DAExB,GACF;AAAA,IAGA,qBAAC,OAAI,eAAc,UAAS,UAAU,GAAG,UAAU,GAChD;AAAA,eAAS,IAAI,CAAC,YACb,oBAAC,OAAqB,SAAS,GAC7B,+BAAC,QAAK,OACJ,QAAQ,SAAS,SAAS,UAC1B,QAAQ,SAAS,UAAU,QAC3B,QAAQ,SAAS,WAAW,WAC5B,SAEC;AAAA,gBAAQ,SAAS,SAAS,OAAO;AAAA,QACjC,QAAQ;AAAA,SACX,KATQ,QAAQ,EAUlB,CACD;AAAA,MAEA,aACC,oBAAC,OACC,8BAAC,QAAK,OAAM,QAAO,2BAAa,GAClC;AAAA,OAEJ;AAAA,IAGA,qBAAC,OAAI,aAAY,UAAS,UAAU,GAClC;AAAA,0BAAC,QAAK,OAAM,QAAQ,gBAAK;AAAA,MACzB,oBAAC,QAAM,iBAAM;AAAA,MACb,oBAAC,QAAK,OAAM,QAAO,oBAAC;AAAA,OACtB;AAAA,IAGA,oBAAC,OAAI,UAAU,GACb,8BAAC,QAAK,OAAM,QAAO,UAAQ,MAAC,oEAE5B,GACF;AAAA,KACF;AAEJ;AAEA,IAAO,cAAQ;", "names": ["input"]}