/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { logger } from "../core/logger.js";
import {
  ToolUsageTracker
} from "./tools.js";
import { ToolExecutionError } from "../utils/errors.js";
class ToolRegistry {
  tools = /* @__PURE__ */ new Map();
  config;
  usageTracker;
  initialized = false;
  constructor(config) {
    this.config = config;
    this.usageTracker = new ToolUsageTracker();
  }
  /**
   * Initialize the registry with built-in tools
   * This is called automatically when tools are first accessed
   */
  async initializeBuiltInTools() {
    if (this.initialized) {
      return;
    }
    try {
      const { ReadFileTool } = await import("./read-file.js");
      const { WriteFileTool } = await import("./write-file.js");
      const { ShellTool } = await import("./shell.js");
      const { LsTool } = await import("./ls.js");
      const { GrepTool } = await import("./grep.js");
      const { GlobTool } = await import("./glob.js");
      const { EditTool } = await import("./edit.js");
      const { WebFetchTool } = await import("./web-fetch.js");
      const { WebSearchTool } = await import("./web-search.js");
      const { MemoryTool } = await import("./memory.js");
      const { ReadManyFilesTool } = await import("./read-many-files.js");
      const { MCPClientTool } = await import("./mcp-client.js");
      const builtInTools = [
        { tool: new ReadFileTool(this.config), category: "file-system" },
        { tool: new WriteFileTool(this.config), category: "file-system" },
        { tool: new ShellTool(this.config), category: "system" },
        { tool: new LsTool(this.config), category: "file-system" },
        { tool: new GrepTool(this.config), category: "file-system" },
        { tool: new GlobTool(this.config), category: "file-system" },
        { tool: new EditTool(this.config), category: "file-system" },
        { tool: new WebFetchTool(this.config), category: "web" },
        { tool: new WebSearchTool(this.config), category: "web" },
        { tool: new MemoryTool(this.config), category: "utility" },
        { tool: new ReadManyFilesTool(this.config), category: "file-system" },
        { tool: new MCPClientTool(), category: "mcp" }
      ];
      for (const { tool, category } of builtInTools) {
        this.register(tool, { category });
      }
      this.initialized = true;
      logger.debug(`Initialized ToolRegistry with ${builtInTools.length} built-in tools`);
    } catch (error) {
      logger.error("Failed to initialize built-in tools:", error);
      this.initialized = true;
    }
  }
  register(tool, options = {}) {
    const { enabled = true, category, metadata } = options;
    this.tools.set(tool.name, {
      tool,
      enabled,
      category,
      metadata
    });
    logger.debug(`Registered tool: ${tool.name}`, {
      enabled,
      category,
      description: tool.description
    });
  }
  unregister(toolName) {
    const removed = this.tools.delete(toolName);
    if (removed) {
      logger.debug(`Unregistered tool: ${toolName}`);
    }
    return removed;
  }
  async get(toolName) {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    return registered?.enabled ? registered.tool : void 0;
  }
  /**
   * Alias for get() method for backward compatibility
   */
  async getTool(toolName) {
    return this.get(toolName);
  }
  async has(toolName) {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    return registered?.enabled || false;
  }
  async list() {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.entries()).filter(([, registered]) => registered.enabled).map(([name]) => name);
  }
  async listAll() {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.keys());
  }
  async getDefinitions() {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.entries()).filter(([, registered]) => registered.enabled).map(([, registered]) => registered.tool.getDefinition());
  }
  async getDefinition(toolName) {
    const tool = await this.get(toolName);
    return tool?.getDefinition();
  }
  async enable(toolName) {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    if (registered) {
      registered.enabled = true;
      logger.debug(`Enabled tool: ${toolName}`);
      return true;
    }
    return false;
  }
  async disable(toolName) {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    if (registered) {
      registered.enabled = false;
      logger.debug(`Disabled tool: ${toolName}`);
      return true;
    }
    return false;
  }
  async execute(toolName, params, context, options = {}) {
    const { timeout = 3e4, retries = 0, trackUsage = true } = options;
    const tool = await this.get(toolName);
    if (!tool) {
      throw new ToolExecutionError(
        `Tool not found or disabled: ${toolName}`,
        toolName
      );
    }
    const startTime = Date.now();
    let lastError;
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        logger.debug(`Executing tool: ${toolName}`, {
          attempt: attempt + 1,
          maxAttempts: retries + 1,
          params
        });
        const result = await this.executeWithTimeout(
          tool,
          params,
          context,
          timeout
        );
        const executionTime2 = Date.now() - startTime;
        if (trackUsage) {
          this.usageTracker.recordExecution(
            toolName,
            result.success,
            executionTime2
          );
        }
        logger.debug(`Tool execution completed: ${toolName}`, {
          success: result.success,
          executionTime: executionTime2,
          attempt: attempt + 1
        });
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        logger.warn(`Tool execution failed: ${toolName}`, {
          attempt: attempt + 1,
          maxAttempts: retries + 1,
          error: lastError.message
        });
        if (attempt === retries) {
          break;
        }
        await new Promise(
          (resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1e3)
        );
      }
    }
    const executionTime = Date.now() - startTime;
    if (trackUsage) {
      this.usageTracker.recordExecution(toolName, false, executionTime);
    }
    throw new ToolExecutionError(
      `Tool execution failed after ${retries + 1} attempts: ${lastError?.message || "Unknown error"}`,
      toolName,
      { originalError: lastError?.message, attempts: retries + 1 }
    );
  }
  async executeWithTimeout(tool, params, context, timeout) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${timeout}ms`));
      }, timeout);
      tool.execute(params, context).then((result) => {
        clearTimeout(timeoutId);
        resolve(result);
      }).catch((error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }
  getUsageStats(toolName) {
    if (toolName) {
      return this.usageTracker.getStats(toolName);
    }
    return this.usageTracker.getAllStats();
  }
  getTopTools(limit) {
    return this.usageTracker.getTopTools(limit);
  }
  resetUsageStats() {
    this.usageTracker.reset();
  }
  async getToolsByCategory(category) {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.entries()).filter(
      ([, registered]) => registered.enabled && registered.category === category
    ).map(([name]) => name);
  }
  async getCategories() {
    await this.initializeBuiltInTools();
    const categories = /* @__PURE__ */ new Set();
    for (const [, registered] of this.tools) {
      if (registered.enabled && registered.category) {
        categories.add(registered.category);
      }
    }
    return Array.from(categories);
  }
  async validateTool(toolName, params) {
    const tool = await this.get(toolName);
    if (!tool) {
      return { valid: false, errors: [`Tool not found: ${toolName}`] };
    }
    const definition = tool.getDefinition();
    const errors = [];
    const required = definition.parameters.required || [];
    for (const requiredParam of required) {
      if (!(requiredParam in params)) {
        errors.push(`Missing required parameter: ${requiredParam}`);
      }
    }
    for (const [paramName, paramValue] of Object.entries(params)) {
      const paramDef = definition.parameters.properties[paramName];
      if (!paramDef) {
        errors.push(`Unknown parameter: ${paramName}`);
        continue;
      }
      if (paramDef.type === "string" && typeof paramValue !== "string") {
        errors.push(`Parameter ${paramName} must be a string`);
      } else if (paramDef.type === "number" && typeof paramValue !== "number") {
        errors.push(`Parameter ${paramName} must be a number`);
      } else if (paramDef.type === "boolean" && typeof paramValue !== "boolean") {
        errors.push(`Parameter ${paramName} must be a boolean`);
      } else if (paramDef.type === "array" && !Array.isArray(paramValue)) {
        errors.push(`Parameter ${paramName} must be an array`);
      }
      if (paramDef.enum && !paramDef.enum.includes(paramValue)) {
        errors.push(
          `Parameter ${paramName} must be one of: ${paramDef.enum.join(", ")}`
        );
      }
    }
    return { valid: errors.length === 0, errors };
  }
  // Bulk operations
  enableAll() {
    for (const [toolName, registered] of this.tools) {
      registered.enabled = true;
    }
    logger.debug("Enabled all tools");
  }
  disableAll() {
    for (const [toolName, registered] of this.tools) {
      registered.enabled = false;
    }
    logger.debug("Disabled all tools");
  }
  enableCategory(category) {
    for (const [toolName, registered] of this.tools) {
      if (registered.category === category) {
        registered.enabled = true;
      }
    }
    logger.debug(`Enabled tools in category: ${category}`);
  }
  disableCategory(category) {
    for (const [toolName, registered] of this.tools) {
      if (registered.category === category) {
        registered.enabled = false;
      }
    }
    logger.debug(`Disabled tools in category: ${category}`);
  }
}
export {
  ToolRegistry
};
//# sourceMappingURL=tool-registry.js.map
