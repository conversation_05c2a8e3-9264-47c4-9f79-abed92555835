{"version": 3, "sources": ["../packages/cli/src/arien.tsx", "../packages/cli/src/ui/App.tsx", "../packages/cli/src/config/config.ts", "../packages/cli/src/utils/package.ts", "../packages/cli/src/utils/version.ts", "../packages/cli/src/config/sandboxConfig.ts", "../packages/cli/src/utils/readStdin.ts", "../packages/cli/src/utils/sandbox.ts", "../packages/cli/src/config/settings.ts", "../packages/cli/src/ui/themes/theme.ts", "../packages/cli/src/ui/themes/default-light.ts", "../packages/cli/src/ui/themes/default.ts", "../packages/cli/src/ui/themes/ansi.ts", "../packages/cli/src/ui/themes/ansi-light.ts", "../packages/cli/src/ui/themes/no-color.ts", "../packages/cli/src/ui/themes/theme-manager.ts", "../packages/cli/src/utils/startupWarnings.ts", "../packages/cli/src/nonInteractiveCli.ts", "../packages/cli/src/ui/utils/errorParsing.ts", "../packages/cli/src/config/extension.ts", "../packages/cli/src/utils/cleanup.ts", "../packages/cli/src/config/auth.ts", "../packages/cli/src/ui/components/shared/MaxSizedBox.tsx", "../packages/cli/src/ui/contexts/OverflowContext.tsx", "../packages/cli/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React from 'react';\nimport { render } from 'ink';\nimport { AppWrapper } from './ui/App.js';\nimport { loadCliConfig } from './config/config.js';\nimport { readStdin } from './utils/readStdin.js';\nimport { basename } from 'node:path';\nimport v8 from 'node:v8';\nimport os from 'node:os';\nimport { spawn } from 'node:child_process';\nimport { start_sandbox } from './utils/sandbox.js';\nimport {\n  LoadedSettings,\n  loadSettings,\n  SettingScope,\n  USER_SETTINGS_PATH,\n} from './config/settings.js';\nimport { themeManager } from './ui/themes/theme-manager.js';\nimport { getStartupWarnings } from './utils/startupWarnings.js';\nimport { runNonInteractive } from './nonInteractiveCli.js';\nimport { loadExtensions, Extension } from './config/extension.js';\nimport { cleanupCheckpoints } from './utils/cleanup.js';\nimport {\n  ApprovalMode,\n  Config,\n  EditTool,\n  ShellTool,\n  WriteFileTool,\n  sessionId,\n  logUserPrompt,\n  AuthType,\n} from '@arien/arien-cli-core';\nimport { validateAuthMethod } from './config/auth.js';\nimport { setMaxSizedBoxDebugging } from './ui/components/shared/MaxSizedBox.js';\n\nfunction getNodeMemoryArgs(config: Config): string[] {\n  const totalMemoryMB = os.totalmem() / (1024 * 1024);\n  const heapStats = v8.getHeapStatistics();\n  const currentMaxOldSpaceSizeMb = Math.floor(\n    heapStats.heap_size_limit / 1024 / 1024,\n  );\n\n  // Set target to 50% of total memory\n  const targetMaxOldSpaceSizeInMB = Math.floor(totalMemoryMB * 0.5);\n  if (config.getDebugMode()) {\n    console.debug(\n      `Current heap size ${currentMaxOldSpaceSizeMb.toFixed(2)} MB`,\n    );\n  }\n\n  if (process.env.ARIEN_CLI_NO_RELAUNCH) {\n    return [];\n  }\n\n  if (targetMaxOldSpaceSizeInMB > currentMaxOldSpaceSizeMb) {\n    if (config.getDebugMode()) {\n      console.debug(\n        `Need to relaunch with more memory: ${targetMaxOldSpaceSizeInMB.toFixed(2)} MB`,\n      );\n    }\n    return [`--max-old-space-size=${targetMaxOldSpaceSizeInMB}`];\n  }\n\n  return [];\n}\n\nasync function relaunchWithAdditionalArgs(additionalArgs: string[]) {\n  const nodeArgs = [...additionalArgs, ...process.argv.slice(1)];\n  const newEnv = { ...process.env, ARIEN_CLI_NO_RELAUNCH: 'true' };\n\n  const child = spawn(process.execPath, nodeArgs, {\n    stdio: 'inherit',\n    env: newEnv,\n  });\n\n  await new Promise((resolve) => child.on('close', resolve));\n  process.exit(0);\n}\n\nexport async function main() {\n  const workspaceRoot = process.cwd();\n  const settings = loadSettings(workspaceRoot);\n\n  await cleanupCheckpoints();\n  if (settings.errors.length > 0) {\n    for (const error of settings.errors) {\n      let errorMessage = `Error in ${error.path}: ${error.message}`;\n      if (!process.env.NO_COLOR) {\n        errorMessage = `\\x1b[31m${errorMessage}\\x1b[0m`;\n      }\n      console.error(errorMessage);\n      console.error(`Please fix ${error.path} and try again.`);\n    }\n    process.exit(1);\n  }\n\n  const extensions = loadExtensions(workspaceRoot);\n  const config = await loadCliConfig(settings.merged, extensions, sessionId);\n\n  // set default fallback to gemini api key\n  // this has to go after load cli because thats where the env is set\n  if (!settings.merged.selectedAuthType && process.env.GEMINI_API_KEY) {\n    settings.setValue(\n      SettingScope.User,\n      'selectedAuthType',\n      AuthType.USE_GEMINI,\n    );\n  }\n\n  setMaxSizedBoxDebugging(config.getDebugMode());\n\n  // Initialize centralized FileDiscoveryService\n  config.getFileService();\n  if (config.getCheckpointingEnabled()) {\n    try {\n      await config.getGitService();\n    } catch {\n      // For now swallow the error, later log it.\n    }\n  }\n\n  if (settings.merged.theme) {\n    if (!themeManager.setActiveTheme(settings.merged.theme)) {\n      // If the theme is not found during initial load, log a warning and continue.\n      // The useThemeCommand hook in App.tsx will handle opening the dialog.\n      console.warn(`Warning: Theme \"${settings.merged.theme}\" not found.`);\n    }\n  }\n\n  const memoryArgs = settings.merged.autoConfigureMaxOldSpaceSize\n    ? getNodeMemoryArgs(config)\n    : [];\n\n  // hop into sandbox if we are outside and sandboxing is enabled\n  if (!process.env.SANDBOX) {\n    const sandboxConfig = config.getSandbox();\n    if (sandboxConfig) {\n      if (settings.merged.selectedAuthType) {\n        // Validate authentication here because the sandbox will interfere with the Oauth2 web redirect.\n        try {\n          const err = validateAuthMethod(settings.merged.selectedAuthType);\n          if (err) {\n            throw new Error(err);\n          }\n          await config.refreshAuth(settings.merged.selectedAuthType);\n        } catch (err) {\n          console.error('Error authenticating:', err);\n          process.exit(1);\n        }\n      }\n      await start_sandbox(sandboxConfig, memoryArgs);\n      process.exit(0);\n    } else {\n      // Not in a sandbox and not entering one, so relaunch with additional\n      // arguments to control memory usage if needed.\n      if (memoryArgs.length > 0) {\n        await relaunchWithAdditionalArgs(memoryArgs);\n        process.exit(0);\n      }\n    }\n  }\n  let input = config.getQuestion();\n  const startupWarnings = await getStartupWarnings();\n\n  // Render UI, passing necessary config values. Check that there is no command line question.\n  if (process.stdin.isTTY && input?.length === 0) {\n    setWindowTitle(basename(workspaceRoot), settings);\n    render(\n      <React.StrictMode>\n        <AppWrapper\n          config={config}\n          settings={settings}\n          startupWarnings={startupWarnings}\n        />\n      </React.StrictMode>,\n      { exitOnCtrlC: false },\n    );\n    return;\n  }\n  // If not a TTY, read from stdin\n  // This is for cases where the user pipes input directly into the command\n  if (!process.stdin.isTTY) {\n    input += await readStdin();\n  }\n  if (!input) {\n    console.error('No input provided via stdin.');\n    process.exit(1);\n  }\n\n  logUserPrompt(config, {\n    'event.name': 'user_prompt',\n    'event.timestamp': new Date().toISOString(),\n    prompt: input,\n    prompt_length: input.length,\n  });\n\n  // Non-interactive mode handled by runNonInteractive\n  const nonInteractiveConfig = await loadNonInteractiveConfig(\n    config,\n    extensions,\n    settings,\n  );\n\n  await runNonInteractive(nonInteractiveConfig, input);\n  process.exit(0);\n}\n\nfunction setWindowTitle(title: string, settings: LoadedSettings) {\n  if (!settings.merged.hideWindowTitle) {\n    process.stdout.write(`\\x1b]2; Arien - ${title} \\x07`);\n\n    process.on('exit', () => {\n      process.stdout.write(`\\x1b]2;\\x07`);\n    });\n  }\n}\n\n// --- Global Unhandled Rejection Handler ---\nprocess.on('unhandledRejection', (reason, _promise) => {\n  // Log other unexpected unhandled rejections as critical errors\n  console.error('=========================================');\n  console.error('CRITICAL: Unhandled Promise Rejection!');\n  console.error('=========================================');\n  console.error('Reason:', reason);\n  console.error('Stack trace may follow:');\n  if (!(reason instanceof Error)) {\n    console.error(reason);\n  }\n  // Exit for genuinely unhandled errors\n  process.exit(1);\n});\n\nasync function loadNonInteractiveConfig(\n  config: Config,\n  extensions: Extension[],\n  settings: LoadedSettings,\n) {\n  let finalConfig = config;\n  if (config.getApprovalMode() !== ApprovalMode.YOLO) {\n    // Everything is not allowed, ensure that only read-only tools are configured.\n    const existingExcludeTools = settings.merged.excludeTools || [];\n    const interactiveTools = [\n      ShellTool.Name,\n      EditTool.Name,\n      WriteFileTool.Name,\n    ];\n\n    const newExcludeTools = [\n      ...new Set([...existingExcludeTools, ...interactiveTools]),\n    ];\n\n    const nonInteractiveSettings = {\n      ...settings.merged,\n      excludeTools: newExcludeTools,\n    };\n    finalConfig = await loadCliConfig(\n      nonInteractiveSettings,\n      extensions,\n      config.getSessionId(),\n    );\n  }\n\n  return await validateNonInterActiveAuth(\n    settings.merged.selectedAuthType,\n    finalConfig,\n  );\n}\n\nasync function validateNonInterActiveAuth(\n  selectedAuthType: AuthType | undefined,\n  nonInteractiveConfig: Config,\n) {\n  // making a special case for the cli. many headless environments might not have a settings.json set\n  // so if GEMINI_API_KEY is set, we'll use that. However since the oauth things are interactive anyway, we'll\n  // still expect that exists\n  if (!selectedAuthType && !process.env.GEMINI_API_KEY) {\n    console.error(\n      `Please set an Auth method in your ${USER_SETTINGS_PATH} OR specify GEMINI_API_KEY env variable file before running`,\n    );\n    process.exit(1);\n  }\n\n  selectedAuthType = selectedAuthType || AuthType.USE_GEMINI;\n  const err = validateAuthMethod(selectedAuthType);\n  if (err != null) {\n    console.error(err);\n    process.exit(1);\n  }\n\n  await nonInteractiveConfig.refreshAuth(selectedAuthType);\n  return nonInteractiveConfig;\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { useState, useCallback } from 'react';\nimport { Box, Text, useInput, useApp } from 'ink';\nimport { LoadedSettings } from '../config/settings.js';\n\ninterface AppProps {\n  config: any;\n  settings: LoadedSettings;\n  startupWarnings?: string[];\n}\n\nexport const AppWrapper = (props: AppProps) => {\n  return <App {...props} />;\n};\n\nconst App = ({ config, settings, startupWarnings = [] }: AppProps) => {\n  // Suppress unused parameter warnings\n  void config;\n  void settings;\n  void startupWarnings;\n  // State\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [messages, setMessages] = useState<Array<{ type: string; text: string; id: string }>>([]);\n\n  // Hooks\n  const { exit } = useApp();\n\n  // Input handling\n  const handleInput = useCallback(async (inputText: string) => {\n    if (!inputText.trim()) return;\n\n    setInput('');\n    setIsLoading(true);\n\n    try {\n      // Add user input to messages\n      const userMessage = {\n        type: 'user',\n        text: inputText,\n        id: Date.now().toString(),\n      };\n      setMessages(prev => [...prev, userMessage]);\n\n      // Process input (placeholder for now)\n      if (inputText.startsWith('/help')) {\n        const helpMessage = {\n          type: 'system',\n          text: 'Available commands:\\n/help - Show this help\\n/clear - Clear history\\n/exit - Exit application',\n          id: (Date.now() + 1).toString(),\n        };\n        setMessages(prev => [...prev, helpMessage]);\n      } else if (inputText.startsWith('/clear')) {\n        setMessages([]);\n      } else if (inputText.startsWith('/exit')) {\n        exit();\n      } else {\n        // Echo response\n        const echoMessage = {\n          type: 'assistant',\n          text: `Echo: ${inputText}`,\n          id: (Date.now() + 1).toString(),\n        };\n        setMessages(prev => [...prev, echoMessage]);\n      }\n    } catch (error) {\n      const errorMessage = {\n        type: 'error',\n        text: `Error: ${error}`,\n        id: (Date.now() + 1).toString(),\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [exit]);\n\n  // Handle keyboard input\n  useInput((input, key) => {\n    if (key.return) {\n      handleInput(input);\n      setInput('');\n    } else if (key.ctrl && input === 'c') {\n      exit();\n    } else if (!key.ctrl && !key.meta && input.length === 1) {\n      setInput(prev => prev + input);\n    } else if (key.backspace || key.delete) {\n      setInput(prev => prev.slice(0, -1));\n    }\n  });\n\n  return (\n    <Box flexDirection=\"column\" height=\"100%\">\n      {/* Header */}\n      <Box borderStyle=\"single\" paddingX={1}>\n        <Text color=\"cyan\" bold>\n          🤖 Arien CLI - AI Assistant for Coding\n        </Text>\n      </Box>\n\n      {/* Messages area */}\n      <Box flexDirection=\"column\" flexGrow={1} paddingX={1}>\n        {messages.map((message) => (\n          <Box key={message.id} marginY={0}>\n            <Text color={\n              message.type === 'user' ? 'green' :\n              message.type === 'error' ? 'red' :\n              message.type === 'system' ? 'yellow' :\n              'white'\n            }>\n              {message.type === 'user' ? '> ' : ''}\n              {message.text}\n            </Text>\n          </Box>\n        ))}\n\n        {isLoading && (\n          <Box>\n            <Text color=\"gray\">Processing...</Text>\n          </Box>\n        )}\n      </Box>\n\n      {/* Input area */}\n      <Box borderStyle=\"single\" paddingX={1}>\n        <Text color=\"blue\">{'> '}</Text>\n        <Text>{input}</Text>\n        <Text color=\"gray\">█</Text>\n      </Box>\n\n      {/* Footer */}\n      <Box paddingX={1}>\n        <Text color=\"gray\" dimColor>\n          Type /help for commands, /exit to quit, Ctrl+C to exit\n        </Text>\n      </Box>\n    </Box>\n  );\n};\n\nexport default App;\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport yargs from 'yargs/yargs';\nimport { hideBin } from 'yargs/helpers';\nimport process from 'node:process';\nimport {\n  Config,\n  loadServerHierarchicalMemory,\n  setGeminiMdFilename as setServerGeminiMdFilename,\n  getCurrentGeminiMdFilename,\n  ApprovalMode,\n  GEMINI_CONFIG_DIR as GEMINI_DIR,\n  DEFAULT_GEMINI_MODEL,\n  DEFAULT_GEMINI_EMBEDDING_MODEL,\n  FileDiscoveryService,\n  TelemetryTarget,\n} from '@arien/arien-cli-core';\nimport { Settings } from './settings.js';\n\nimport { Extension } from './extension.js';\nimport { getCliVersion } from '../utils/version.js';\nimport * as dotenv from 'dotenv';\nimport * as fs from 'node:fs';\nimport * as path from 'node:path';\nimport * as os from 'node:os';\nimport { loadSandboxConfig } from './sandboxConfig.js';\n\n// Simple console logger for now - replace with actual logger if available\nconst logger = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  debug: (...args: any[]) => console.debug('[DEBUG]', ...args),\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  warn: (...args: any[]) => console.warn('[WARN]', ...args),\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  error: (...args: any[]) => console.error('[ERROR]', ...args),\n};\n\ninterface CliArgs {\n  model: string | undefined;\n  sandbox: boolean | string | undefined;\n  'sandbox-image': string | undefined;\n  debug: boolean | undefined;\n  prompt: string | undefined;\n  all_files: boolean | undefined;\n  show_memory_usage: boolean | undefined;\n  yolo: boolean | undefined;\n  telemetry: boolean | undefined;\n  checkpointing: boolean | undefined;\n  telemetryTarget: string | undefined;\n  telemetryOtlpEndpoint: string | undefined;\n  telemetryLogPrompts: boolean | undefined;\n}\n\nasync function parseArguments(): Promise<CliArgs> {\n  const argv = await yargs(hideBin(process.argv))\n    .option('model', {\n      alias: 'm',\n      type: 'string',\n      description: `Model`,\n      default: process.env.GEMINI_MODEL || DEFAULT_GEMINI_MODEL,\n    })\n    .option('prompt', {\n      alias: 'p',\n      type: 'string',\n      description: 'Prompt. Appended to input on stdin (if any).',\n    })\n    .option('sandbox', {\n      alias: 's',\n      type: 'boolean',\n      description: 'Run in sandbox?',\n    })\n    .option('sandbox-image', {\n      type: 'string',\n      description: 'Sandbox image URI.',\n    })\n    .option('debug', {\n      alias: 'd',\n      type: 'boolean',\n      description: 'Run in debug mode?',\n      default: false,\n    })\n    .option('all_files', {\n      alias: 'a',\n      type: 'boolean',\n      description: 'Include ALL files in context?',\n      default: false,\n    })\n    .option('show_memory_usage', {\n      type: 'boolean',\n      description: 'Show memory usage in status bar',\n      default: false,\n    })\n    .option('yolo', {\n      alias: 'y',\n      type: 'boolean',\n      description: 'Auto-approve all tool calls (dangerous!)',\n      default: false,\n    })\n    .option('telemetry', {\n      type: 'boolean',\n      description: 'Enable telemetry',\n    })\n    .option('checkpointing', {\n      type: 'boolean',\n      description: 'Enable checkpointing',\n    })\n    .option('telemetryTarget', {\n      type: 'string',\n      description: 'Telemetry target',\n    })\n    .option('telemetryOtlpEndpoint', {\n      type: 'string',\n      description: 'OTLP endpoint for telemetry',\n    })\n    .option('telemetryLogPrompts', {\n      type: 'boolean',\n      description: 'Log prompts in telemetry',\n    })\n    .help()\n    .alias('help', 'h')\n    .version(getCliVersion())\n    .alias('version', 'v')\n    .parse();\n\n  return argv as CliArgs;\n}\n\nexport async function loadCliConfig(\n  settings: Settings,\n  extensions: Extension[],\n  sessionId: string,\n): Promise<Config> {\n  const args = await parseArguments();\n\n  // Load environment variables from .env files\n  loadEnvFiles();\n\n  // Create base config\n  const config = new Config({\n    workspaceRoot: process.cwd(),\n    sessionId,\n  });\n\n  // Apply CLI arguments\n  if (args.model) {\n    config.setModel(args.model);\n  }\n\n  if (args.debug !== undefined) {\n    config.setDebugMode(args.debug);\n  }\n\n  if (args.prompt) {\n    config.setQuestion(args.prompt);\n  }\n\n  if (args.all_files !== undefined) {\n    config.setIncludeAllFiles(args.all_files);\n  }\n\n  if (args.show_memory_usage !== undefined) {\n    config.setShowMemoryUsage(args.show_memory_usage);\n  }\n\n  if (args.yolo !== undefined) {\n    config.setApprovalMode(args.yolo ? ApprovalMode.YOLO : ApprovalMode.MANUAL);\n  }\n\n  if (args.telemetry !== undefined) {\n    config.setTelemetryEnabled(args.telemetry);\n  }\n\n  if (args.checkpointing !== undefined) {\n    config.setCheckpointingEnabled(args.checkpointing);\n  }\n\n  if (args.telemetryTarget) {\n    config.setTelemetryTarget(args.telemetryTarget as TelemetryTarget);\n  }\n\n  if (args.telemetryOtlpEndpoint) {\n    config.setTelemetryOtlpEndpoint(args.telemetryOtlpEndpoint);\n  }\n\n  if (args.telemetryLogPrompts !== undefined) {\n    config.setTelemetryLogPrompts(args.telemetryLogPrompts);\n  }\n\n  // Apply settings\n  applySettings(config, settings);\n\n  // Apply extensions\n  applyExtensions(config, extensions);\n\n  // Load sandbox configuration\n  if (args.sandbox !== undefined || args['sandbox-image']) {\n    const sandboxConfig = await loadSandboxConfig(\n      args.sandbox,\n      args['sandbox-image'],\n    );\n    if (sandboxConfig) {\n      config.setSandbox(sandboxConfig);\n    }\n  }\n\n  // Load hierarchical memory\n  try {\n    const memory = await loadServerHierarchicalMemory(\n      config.getWorkspaceRoot(),\n      getCurrentGeminiMdFilename(),\n    );\n    config.setMemory(memory);\n  } catch (error) {\n    logger.warn('Failed to load hierarchical memory:', error);\n  }\n\n  return config;\n}\n\nfunction loadEnvFiles() {\n  const envFiles = ['.env', '.env.local'];\n  \n  for (const envFile of envFiles) {\n    const envPath = path.join(process.cwd(), envFile);\n    if (fs.existsSync(envPath)) {\n      dotenv.config({ path: envPath });\n      logger.debug(`Loaded environment variables from ${envFile}`);\n    }\n  }\n\n  // Also check home directory\n  const homeEnvPath = path.join(os.homedir(), '.arien', '.env');\n  if (fs.existsSync(homeEnvPath)) {\n    dotenv.config({ path: homeEnvPath });\n    logger.debug('Loaded environment variables from ~/.arien/.env');\n  }\n}\n\nfunction applySettings(config: Config, settings: Settings) {\n  if (settings.model) {\n    config.setModel(settings.model);\n  }\n\n  if (settings.embeddingModel) {\n    config.setEmbeddingModel(settings.embeddingModel);\n  }\n\n  if (settings.debugMode !== undefined) {\n    config.setDebugMode(settings.debugMode);\n  }\n\n  if (settings.includeAllFiles !== undefined) {\n    config.setIncludeAllFiles(settings.includeAllFiles);\n  }\n\n  if (settings.showMemoryUsage !== undefined) {\n    config.setShowMemoryUsage(settings.showMemoryUsage);\n  }\n\n  if (settings.approvalMode) {\n    config.setApprovalMode(settings.approvalMode);\n  }\n\n  if (settings.telemetryEnabled !== undefined) {\n    config.setTelemetryEnabled(settings.telemetryEnabled);\n  }\n\n  if (settings.checkpointingEnabled !== undefined) {\n    config.setCheckpointingEnabled(settings.checkpointingEnabled);\n  }\n\n  if (settings.telemetryTarget) {\n    config.setTelemetryTarget(settings.telemetryTarget);\n  }\n\n  if (settings.telemetryOtlpEndpoint) {\n    config.setTelemetryOtlpEndpoint(settings.telemetryOtlpEndpoint);\n  }\n\n  if (settings.telemetryLogPrompts !== undefined) {\n    config.setTelemetryLogPrompts(settings.telemetryLogPrompts);\n  }\n\n  if (settings.excludeTools) {\n    config.setExcludeTools(settings.excludeTools);\n  }\n\n  if (settings.includeTools) {\n    config.setIncludeTools(settings.includeTools);\n  }\n\n  if (settings.geminiMdFilename) {\n    setServerGeminiMdFilename(settings.geminiMdFilename);\n  }\n}\n\nfunction applyExtensions(config: Config, extensions: Extension[]) {\n  for (const extension of extensions) {\n    try {\n      extension.configure?.(config);\n      logger.debug(`Applied extension: ${extension.name}`);\n    } catch (error) {\n      logger.error(`Failed to apply extension ${extension.name}:`, error);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { fileURLToPath } from 'url';\n\nexport interface PackageJson {\n  name?: string;\n  version?: string;\n  description?: string;\n  main?: string;\n  scripts?: Record<string, string>;\n  dependencies?: Record<string, string>;\n  devDependencies?: Record<string, string>;\n  config?: {\n    sandboxImageUri?: string;\n  };\n  [key: string]: any;\n}\n\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\nlet packageJson: PackageJson | undefined;\n\nexport function getPackageJson(): PackageJson | undefined {\n  if (packageJson) {\n    return packageJson;\n  }\n\n  // Start from current directory and walk up to find package.json\n  let currentDir = __dirname;\n  while (currentDir !== path.dirname(currentDir)) {\n    const packagePath = path.join(currentDir, 'package.json');\n    if (fs.existsSync(packagePath)) {\n      try {\n        const content = fs.readFileSync(packagePath, 'utf8');\n        packageJson = JSON.parse(content);\n        return packageJson;\n      } catch (error) {\n        console.error(`Error reading package.json at ${packagePath}:`, error);\n        break;\n      }\n    }\n    currentDir = path.dirname(currentDir);\n  }\n\n  return undefined;\n}\n\nexport function getPackageJsonSync(startDir?: string): PackageJson | undefined {\n  let currentDir = startDir || process.cwd();\n  \n  while (currentDir !== path.dirname(currentDir)) {\n    const packagePath = path.join(currentDir, 'package.json');\n    if (fs.existsSync(packagePath)) {\n      try {\n        const content = fs.readFileSync(packagePath, 'utf8');\n        return JSON.parse(content);\n      } catch (error) {\n        console.error(`Error reading package.json at ${packagePath}:`, error);\n        break;\n      }\n    }\n    currentDir = path.dirname(currentDir);\n  }\n\n  return undefined;\n}\n\nexport function findPackageRoot(startDir?: string): string | undefined {\n  let currentDir = startDir || process.cwd();\n  \n  while (currentDir !== path.dirname(currentDir)) {\n    const packagePath = path.join(currentDir, 'package.json');\n    if (fs.existsSync(packagePath)) {\n      return currentDir;\n    }\n    currentDir = path.dirname(currentDir);\n  }\n\n  return undefined;\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { getPackageJson } from './package.js';\n\nexport function getCliVersion(): string {\n  try {\n    const pkgJson = getPackageJson();\n    return process.env.CLI_VERSION || pkgJson?.version || 'unknown';\n  } catch {\n    return 'unknown';\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { SandboxConfig } from '@arien/arien-cli-core';\nimport commandExists from 'command-exists';\nimport * as os from 'node:os';\nimport { getPackageJson } from '../utils/package.js';\n\nconst VALID_SANDBOX_COMMANDS: ReadonlyArray<SandboxConfig['command']> = [\n  'docker',\n  'podman',\n  'sandbox-exec',\n];\n\nfunction isSandboxCommand(value: string): value is SandboxConfig['command'] {\n  return (VALID_SANDBOX_COMMANDS as readonly string[]).includes(value);\n}\n\nfunction getSandboxCommand(\n  sandbox?: boolean | string,\n): SandboxConfig['command'] | '' {\n  // If the SANDBOX env var is set, we're already inside the sandbox.\n  if (process.env.SANDBOX) {\n    return '';\n  }\n\n  // note environment variable takes precedence over argument (from command line or settings)\n  const environmentConfiguredSandbox =\n    process.env.ARIEN_SANDBOX?.toLowerCase().trim() ?? '';\n  sandbox =\n    environmentConfiguredSandbox?.length > 0\n      ? environmentConfiguredSandbox\n      : sandbox;\n  if (sandbox === '1' || sandbox === 'true') sandbox = true;\n  else if (sandbox === '0' || sandbox === 'false' || !sandbox) sandbox = false;\n\n  if (sandbox === false) {\n    return '';\n  }\n\n  if (typeof sandbox === 'string' && sandbox) {\n    if (!isSandboxCommand(sandbox)) {\n      console.error(\n        `ERROR: invalid sandbox command '${sandbox}'. Must be one of ${VALID_SANDBOX_COMMANDS.join(\n          ', ',\n        )}`,\n      );\n      process.exit(1);\n    }\n    // confirm that specified command exists\n    if (commandExists.sync(sandbox)) {\n      return sandbox;\n    }\n    console.error(\n      `ERROR: missing sandbox command '${sandbox}' (from ARIEN_SANDBOX)`,\n    );\n    process.exit(1);\n  }\n\n  // look for seatbelt, docker, or podman, in that order\n  // for container-based sandboxing, require sandbox to be enabled explicitly\n  if (os.platform() === 'darwin' && commandExists.sync('sandbox-exec')) {\n    return 'sandbox-exec';\n  } else if (commandExists.sync('docker') && sandbox === true) {\n    return 'docker';\n  } else if (commandExists.sync('podman') && sandbox === true) {\n    return 'podman';\n  }\n\n  // throw an error if user requested sandbox but no command was found\n  if (sandbox === true) {\n    console.error(\n      'ERROR: ARIEN_SANDBOX is true but failed to determine command for sandbox; ' +\n        'install docker or podman or specify command in ARIEN_SANDBOX',\n    );\n    process.exit(1);\n  }\n\n  return '';\n}\n\nexport async function loadSandboxConfig(\n  sandbox?: boolean | string,\n  sandboxImage?: string,\n): Promise<SandboxConfig | undefined> {\n  const command = getSandboxCommand(sandbox);\n\n  const packageJson = await getPackageJson();\n  const image =\n    sandboxImage ??\n    process.env.ARIEN_SANDBOX_IMAGE ??\n    packageJson?.config?.sandboxImageUri;\n\n  return command && image ? { command, image } : undefined;\n}\n\nexport function validateSandboxConfig(config: SandboxConfig): string[] {\n  const errors: string[] = [];\n\n  if (!config.command) {\n    errors.push('Sandbox command is required');\n  } else if (!isSandboxCommand(config.command)) {\n    errors.push(\n      `Invalid sandbox command \"${config.command}\". Must be one of: ${VALID_SANDBOX_COMMANDS.join(', ')}`,\n    );\n  }\n\n  if (!config.image) {\n    errors.push('Sandbox image is required');\n  }\n\n  return errors;\n}\n\nexport function getSupportedSandboxCommands(): readonly string[] {\n  return VALID_SANDBOX_COMMANDS;\n}\n\nexport async function checkSandboxAvailability(): Promise<{\n  available: SandboxConfig['command'][];\n  unavailable: SandboxConfig['command'][];\n}> {\n  const available: SandboxConfig['command'][] = [];\n  const unavailable: SandboxConfig['command'][] = [];\n\n  for (const command of VALID_SANDBOX_COMMANDS) {\n    try {\n      if (commandExists.sync(command)) {\n        available.push(command);\n      } else {\n        unavailable.push(command);\n      }\n    } catch {\n      unavailable.push(command);\n    }\n  }\n\n  return { available, unavailable };\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nexport async function readStdin(): Promise<string> {\n  return new Promise((resolve, reject) => {\n    let data = '';\n    process.stdin.setEncoding('utf8');\n\n    const onReadable = () => {\n      let chunk;\n      while ((chunk = process.stdin.read()) !== null) {\n        data += chunk;\n      }\n    };\n\n    const onEnd = () => {\n      cleanup();\n      resolve(data);\n    };\n\n    const onError = (err: Error) => {\n      cleanup();\n      reject(err);\n    };\n\n    const cleanup = () => {\n      process.stdin.removeListener('readable', onReadable);\n      process.stdin.removeListener('end', onEnd);\n      process.stdin.removeListener('error', onError);\n    };\n\n    process.stdin.on('readable', onReadable);\n    process.stdin.on('end', onEnd);\n    process.stdin.on('error', onError);\n  });\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { exec, execSync, spawn, type ChildProcess } from 'node:child_process';\nimport os from 'node:os';\nimport path from 'node:path';\nimport fs from 'node:fs';\nimport { readFile } from 'node:fs/promises';\nimport { quote } from 'shell-quote';\nimport {\n  USER_SETTINGS_DIR,\n  SETTINGS_DIRECTORY_NAME,\n} from '../config/settings.js';\nimport { promisify } from 'util';\nimport { SandboxConfig } from '@arien/arien-cli-core';\n\nconst execAsync = promisify(exec);\n\nfunction getContainerPath(hostPath: string): string {\n  if (os.platform() !== 'win32') {\n    return hostPath;\n  }\n  const withForwardSlashes = hostPath.replace(/\\\\/g, '/');\n  const match = withForwardSlashes.match(/^([A-Z]):\\/(.*)/i);\n  if (match) {\n    return `/${match[1].toLowerCase()}/${match[2]}`;\n  }\n  return hostPath;\n}\n\nconst LOCAL_DEV_SANDBOX_IMAGE_NAME = 'arien-cli-sandbox';\nconst SANDBOX_NETWORK_NAME = 'arien-cli-sandbox';\nconst SANDBOX_PROXY_NAME = 'arien-cli-sandbox-proxy';\nconst BUILTIN_SEATBELT_PROFILES = [\n  'permissive-open',\n  'permissive-closed',\n  'permissive-proxied',\n  'restrictive-open',\n  'restrictive-closed',\n  'restrictive-proxied',\n];\n\n/**\n * Determines whether the sandbox container should be run with the current user's UID and GID.\n */\nasync function shouldUseCurrentUserInSandbox(): Promise<boolean> {\n  const envVar = process.env.SANDBOX_SET_UID_GID?.toLowerCase().trim();\n\n  if (envVar === '1' || envVar === 'true') {\n    return true;\n  }\n  if (envVar === '0' || envVar === 'false') {\n    return false;\n  }\n\n  // If environment variable is not explicitly set, check for Debian/Ubuntu Linux\n  if (os.platform() === 'linux') {\n    try {\n      const osReleaseContent = await readFile('/etc/os-release', 'utf8');\n      if (\n        osReleaseContent.includes('ID=debian') ||\n        osReleaseContent.includes('ID=ubuntu') ||\n        osReleaseContent.match(/^ID_LIKE=.*debian.*/m) || // Covers derivatives\n        osReleaseContent.match(/^ID_LIKE=.*ubuntu.*/m) // Covers derivatives\n      ) {\n        console.error(\n          'INFO: Defaulting to use current user UID/GID for Debian/Ubuntu-based Linux.',\n        );\n        return true;\n      }\n    } catch (_err) {\n      console.warn(\n        'Warning: Could not read /etc/os-release to auto-detect Debian/Ubuntu for UID/GID default.',\n      );\n    }\n  }\n  return false; // Default to false if no other condition is met\n}\n\nexport async function start_sandbox(\n  sandboxConfig: SandboxConfig,\n  memoryArgs: string[] = [],\n): Promise<void> {\n  const { command, image } = sandboxConfig;\n\n  if (command === 'sandbox-exec') {\n    await startSeatbeltSandbox(image, memoryArgs);\n  } else if (command === 'docker' || command === 'podman') {\n    await startContainerSandbox(command, image, memoryArgs);\n  } else {\n    throw new Error(`Unsupported sandbox command: ${command}`);\n  }\n}\n\nasync function startSeatbeltSandbox(\n  profile: string,\n  memoryArgs: string[],\n): Promise<void> {\n  const profilePath = getSeatbeltProfilePath(profile);\n  if (!profilePath) {\n    throw new Error(`Unknown seatbelt profile: ${profile}`);\n  }\n\n  const args = [\n    'sandbox-exec',\n    '-f',\n    profilePath,\n    process.execPath,\n    ...memoryArgs,\n    ...process.argv.slice(1),\n  ];\n\n  const env = { ...process.env, SANDBOX: 'true' };\n  const child = spawn(args[0], args.slice(1), {\n    stdio: 'inherit',\n    env,\n  });\n\n  await new Promise<void>((resolve, reject) => {\n    child.on('close', (code) => {\n      if (code === 0) {\n        resolve();\n      } else {\n        reject(new Error(`Sandbox process exited with code ${code}`));\n      }\n    });\n    child.on('error', reject);\n  });\n}\n\nasync function startContainerSandbox(\n  command: 'docker' | 'podman',\n  image: string,\n  memoryArgs: string[],\n): Promise<void> {\n  const workspaceRoot = process.cwd();\n  const containerWorkspaceRoot = getContainerPath(workspaceRoot);\n  const containerUserSettingsDir = getContainerPath(USER_SETTINGS_DIR);\n\n  const args = [\n    command,\n    'run',\n    '--rm',\n    '-it',\n    '--network', 'host',\n    '-v', `${workspaceRoot}:${containerWorkspaceRoot}`,\n    '-v', `${USER_SETTINGS_DIR}:${containerUserSettingsDir}`,\n    '-w', containerWorkspaceRoot,\n  ];\n\n  // Add user/group mapping if needed\n  if (await shouldUseCurrentUserInSandbox()) {\n    const uid = process.getuid?.() || 0;\n    const gid = process.getgid?.() || 0;\n    args.push('--user', `${uid}:${gid}`);\n  }\n\n  // Add environment variables\n  const env = { ...process.env, SANDBOX: 'true' };\n  for (const [key, value] of Object.entries(env)) {\n    if (value !== undefined) {\n      args.push('-e', `${key}=${value}`);\n    }\n  }\n\n  args.push(image, 'node', ...memoryArgs, ...process.argv.slice(1));\n\n  const child = spawn(args[0], args.slice(1), {\n    stdio: 'inherit',\n  });\n\n  await new Promise<void>((resolve, reject) => {\n    child.on('close', (code) => {\n      if (code === 0) {\n        resolve();\n      } else {\n        reject(new Error(`Container process exited with code ${code}`));\n      }\n    });\n    child.on('error', reject);\n  });\n}\n\nfunction getSeatbeltProfilePath(profile: string): string | null {\n  if (BUILTIN_SEATBELT_PROFILES.includes(profile)) {\n    return path.join(__dirname, `sandbox-macos-${profile}.sb`);\n  }\n  \n  // Check if it's a custom profile path\n  if (fs.existsSync(profile)) {\n    return profile;\n  }\n  \n  return null;\n}\n\nexport function isSandboxSupported(): boolean {\n  if (os.platform() === 'darwin') {\n    try {\n      execSync('which sandbox-exec', { stdio: 'ignore' });\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  // Check for Docker or Podman\n  try {\n    execSync('which docker', { stdio: 'ignore' });\n    return true;\n  } catch {\n    try {\n      execSync('which podman', { stdio: 'ignore' });\n      return true;\n    } catch {\n      return false;\n    }\n  }\n}\n\nexport function getSupportedSandboxCommands(): string[] {\n  const supported: string[] = [];\n\n  if (os.platform() === 'darwin') {\n    try {\n      execSync('which sandbox-exec', { stdio: 'ignore' });\n      supported.push('sandbox-exec');\n    } catch {\n      // Not available\n    }\n  }\n\n  try {\n    execSync('which docker', { stdio: 'ignore' });\n    supported.push('docker');\n  } catch {\n    // Not available\n  }\n\n  try {\n    execSync('which podman', { stdio: 'ignore' });\n    supported.push('podman');\n  } catch {\n    // Not available\n  }\n\n  return supported;\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { homedir } from 'os';\nimport {\n  MCPServerConfig,\n  getErrorMessage,\n  BugCommandSettings,\n  TelemetrySettings,\n  AuthType,\n  ApprovalMode,\n  TelemetryTarget,\n} from '@arien/arien-cli-core';\nimport stripJsonComments from 'strip-json-comments';\nimport { DefaultLight } from '../ui/themes/default-light.js';\nimport { DefaultDark } from '../ui/themes/default.js';\n\nexport const SETTINGS_DIRECTORY_NAME = '.arien';\nexport const USER_SETTINGS_DIR = path.join(homedir(), SETTINGS_DIRECTORY_NAME);\nexport const USER_SETTINGS_PATH = path.join(USER_SETTINGS_DIR, 'settings.json');\n\nexport enum SettingScope {\n  User = 'User',\n  Workspace = 'Workspace',\n}\n\nexport interface CheckpointingSettings {\n  enabled?: boolean;\n}\n\nexport interface AccessibilitySettings {\n  disableLoadingPhrases?: boolean;\n}\n\nexport interface Settings {\n  theme?: string;\n  selectedAuthType?: AuthType;\n  sandbox?: boolean | string;\n  model?: string;\n  embeddingModel?: string;\n  debugMode?: boolean;\n  includeAllFiles?: boolean;\n  showMemoryUsage?: boolean;\n  approvalMode?: ApprovalMode;\n  telemetryEnabled?: boolean;\n  checkpointingEnabled?: boolean;\n  telemetryTarget?: TelemetryTarget;\n  telemetryOtlpEndpoint?: string;\n  telemetryLogPrompts?: boolean;\n  coreTools?: string[];\n  excludeTools?: string[];\n  includeTools?: string[];\n  toolDiscoveryCommand?: string;\n  toolCallCommand?: string;\n  mcpServerCommand?: string;\n  mcpServers?: Record<string, MCPServerConfig>;\n  contextFileName?: string | string[];\n  geminiMdFilename?: string;\n  accessibility?: AccessibilitySettings;\n  telemetry?: TelemetrySettings;\n  usageStatisticsEnabled?: boolean;\n  preferredEditor?: string;\n  bugCommand?: BugCommandSettings;\n  checkpointing?: CheckpointingSettings;\n  autoConfigureMaxOldSpaceSize?: boolean;\n\n  // Git-aware file filtering settings\n  fileFiltering?: {\n    respectGitIgnore?: boolean;\n    enableRecursiveFileSearch?: boolean;\n  };\n\n  // UI setting. Does not display the ANSI-controlled terminal title.\n  hideWindowTitle?: boolean;\n  hideTips?: boolean;\n\n  // Add other settings here.\n}\n\nexport interface SettingsError {\n  message: string;\n  path: string;\n}\n\nexport interface SettingsFile {\n  settings: Settings;\n  path: string;\n}\n\nexport class LoadedSettings {\n  constructor(\n    user: SettingsFile,\n    workspace: SettingsFile,\n    errors: SettingsError[],\n  ) {\n    this.user = user;\n    this.workspace = workspace;\n    this.errors = errors;\n    this._merged = this.computeMergedSettings();\n  }\n\n  readonly user: SettingsFile;\n  readonly workspace: SettingsFile;\n  readonly errors: SettingsError[];\n\n  private _merged: Settings;\n\n  get merged(): Settings {\n    return this._merged;\n  }\n\n  private computeMergedSettings(): Settings {\n    // Workspace settings override user settings\n    return {\n      ...this.user.settings,\n      ...this.workspace.settings,\n    };\n  }\n\n  setValue(scope: SettingScope, key: keyof Settings, value: any) {\n    const targetFile = scope === SettingScope.User ? this.user : this.workspace;\n    targetFile.settings[key] = value;\n    \n    // Write to file\n    try {\n      fs.mkdirSync(path.dirname(targetFile.path), { recursive: true });\n      fs.writeFileSync(\n        targetFile.path,\n        JSON.stringify(targetFile.settings, null, 2),\n      );\n      \n      // Recompute merged settings\n      this._merged = this.computeMergedSettings();\n    } catch (error) {\n      console.error(`Failed to save settings to ${targetFile.path}:`, error);\n    }\n  }\n\n  getValue(key: keyof Settings): any {\n    return this.merged[key];\n  }\n}\n\nexport function loadSettings(workspaceRoot: string): LoadedSettings {\n  const errors: SettingsError[] = [];\n  \n  // Load user settings\n  const userSettings = loadSettingsFile(USER_SETTINGS_PATH, errors);\n  \n  // Load workspace settings\n  const workspaceSettingsPath = path.join(workspaceRoot, SETTINGS_DIRECTORY_NAME, 'settings.json');\n  const workspaceSettings = loadSettingsFile(workspaceSettingsPath, errors);\n  \n  return new LoadedSettings(userSettings, workspaceSettings, errors);\n}\n\nfunction loadSettingsFile(filePath: string, errors: SettingsError[]): SettingsFile {\n  const defaultSettings: Settings = {\n    theme: DefaultDark.name,\n    autoConfigureMaxOldSpaceSize: true,\n    fileFiltering: {\n      respectGitIgnore: true,\n      enableRecursiveFileSearch: true,\n    },\n    telemetry: {\n      enabled: true,\n    },\n    checkpointing: {\n      enabled: true,\n    },\n    accessibility: {\n      disableLoadingPhrases: false,\n    },\n  };\n\n  if (!fs.existsSync(filePath)) {\n    return {\n      settings: defaultSettings,\n      path: filePath,\n    };\n  }\n\n  try {\n    const content = fs.readFileSync(filePath, 'utf8');\n    const cleanContent = stripJsonComments(content);\n    const parsed = JSON.parse(cleanContent);\n    \n    // Validate and merge with defaults\n    const settings: Settings = {\n      ...defaultSettings,\n      ...parsed,\n    };\n\n    return {\n      settings,\n      path: filePath,\n    };\n  } catch (error) {\n    errors.push({\n      message: getErrorMessage(error),\n      path: filePath,\n    });\n    \n    return {\n      settings: defaultSettings,\n      path: filePath,\n    };\n  }\n}\n\nexport function getDefaultThemes() {\n  return [DefaultDark, DefaultLight];\n}\n\nexport function createUserSettingsIfNotExists() {\n  if (!fs.existsSync(USER_SETTINGS_PATH)) {\n    const defaultSettings: Settings = {\n      theme: DefaultDark.name,\n      autoConfigureMaxOldSpaceSize: true,\n      fileFiltering: {\n        respectGitIgnore: true,\n        enableRecursiveFileSearch: true,\n      },\n      telemetry: {\n        enabled: true,\n      },\n      checkpointing: {\n        enabled: true,\n      },\n      accessibility: {\n        disableLoadingPhrases: false,\n      },\n    };\n\n    try {\n      fs.mkdirSync(path.dirname(USER_SETTINGS_PATH), { recursive: true });\n      fs.writeFileSync(\n        USER_SETTINGS_PATH,\n        JSON.stringify(defaultSettings, null, 2),\n      );\n    } catch (error) {\n      console.error('Failed to create default user settings:', error);\n    }\n  }\n}\n\nexport function validateSettings(settings: Settings): SettingsError[] {\n  const errors: SettingsError[] = [];\n\n  // Validate theme\n  if (settings.theme) {\n    const availableThemes = getDefaultThemes().map(t => t.name);\n    if (!availableThemes.includes(settings.theme)) {\n      errors.push({\n        message: `Invalid theme \"${settings.theme}\". Available themes: ${availableThemes.join(', ')}`,\n        path: 'theme',\n      });\n    }\n  }\n\n  // Validate auth type\n  if (settings.selectedAuthType && !Object.values(AuthType).includes(settings.selectedAuthType)) {\n    errors.push({\n      message: `Invalid auth type \"${settings.selectedAuthType}\"`,\n      path: 'selectedAuthType',\n    });\n  }\n\n  // Validate approval mode\n  if (settings.approvalMode && !Object.values(ApprovalMode).includes(settings.approvalMode)) {\n    errors.push({\n      message: `Invalid approval mode \"${settings.approvalMode}\"`,\n      path: 'approvalMode',\n    });\n  }\n\n  // Validate telemetry target\n  if (settings.telemetryTarget && !Object.values(TelemetryTarget).includes(settings.telemetryTarget)) {\n    errors.push({\n      message: `Invalid telemetry target \"${settings.telemetryTarget}\"`,\n      path: 'telemetryTarget',\n    });\n  }\n\n  return errors;\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport type { CSSProperties } from 'react';\n\nexport type ThemeType = 'light' | 'dark' | 'ansi';\n\nexport interface ColorsTheme {\n  type: ThemeType;\n  Background: string;\n  Foreground: string;\n  LightBlue: string;\n  AccentBlue: string;\n  AccentPurple: string;\n  AccentCyan: string;\n  AccentGreen: string;\n  AccentYellow: string;\n  AccentRed: string;\n  Comment: string;\n  Gray: string;\n  GradientColors?: string[];\n}\n\nexport const lightTheme: ColorsTheme = {\n  type: 'light',\n  Background: '#FAFAFA',\n  Foreground: '#3C3C43',\n  LightBlue: '#89BDCD',\n  AccentBlue: '#3B82F6',\n  AccentPurple: '#8B5CF6',\n  AccentCyan: '#06B6D4',\n  AccentGreen: '#3CA84B',\n  AccentYellow: '#D5A40A',\n  AccentRed: '#DD4C4C',\n  Comment: '#008000',\n  Gray: '#B7BECC',\n  GradientColors: ['#4796E4', '#847ACE', '#C3677F'],\n};\n\nexport const darkTheme: ColorsTheme = {\n  type: 'dark',\n  Background: '#1E1E2E',\n  Foreground: '#CDD6F4',\n  LightBlue: '#ADD8E6',\n  AccentBlue: '#89B4FA',\n  AccentPurple: '#CBA6F7',\n  AccentCyan: '#89DCEB',\n  AccentGreen: '#A6E3A1',\n  AccentYellow: '#F9E2AF',\n  AccentRed: '#F38BA8',\n  Comment: '#6C7086',\n  Gray: '#6C7086',\n  GradientColors: ['#4796E4', '#847ACE', '#C3677F'],\n};\n\nexport const ansiTheme: ColorsTheme = {\n  type: 'ansi',\n  Background: 'black',\n  Foreground: 'white',\n  LightBlue: 'blue',\n  AccentBlue: 'blue',\n  AccentPurple: 'magenta',\n  AccentCyan: 'cyan',\n  AccentGreen: 'green',\n  AccentYellow: 'yellow',\n  AccentRed: 'red',\n  Comment: 'gray',\n  Gray: 'gray',\n};\n\nexport class Theme {\n  readonly defaultColor: string;\n  protected readonly _colorMap: Readonly<Record<string, string>>;\n\n  // Define the set of Ink's named colors for quick lookup\n  private static readonly inkSupportedNames = new Set([\n    'black',\n    'red',\n    'green',\n    'yellow',\n    'blue',\n    'cyan',\n    'magenta',\n    'white',\n    'gray',\n    'grey',\n    'blackbright',\n    'redbright',\n    'greenbright',\n    'yellowbright',\n    'bluebright',\n    'cyanbright',\n    'magentabright',\n    'whitebright',\n  ]);\n\n  constructor(\n    readonly name: string,\n    readonly type: ThemeType,\n    rawMappings: Record<string, CSSProperties>,\n    readonly colors: ColorsTheme,\n  ) {\n    this._colorMap = Object.freeze(this._buildColorMap(rawMappings));\n\n    // Determine the default foreground color\n    const rawDefaultColor = rawMappings['hljs']?.color;\n    this.defaultColor =\n      (rawDefaultColor ? Theme._resolveColor(rawDefaultColor) : undefined) ??\n      colors.Foreground;\n  }\n\n  getInkColor(hljsClass: string): string | undefined {\n    return this._colorMap[hljsClass];\n  }\n\n  private static _resolveColor(colorValue: string): string | undefined {\n    const lowerColor = colorValue.toLowerCase();\n\n    // 1. Check if it's already a hex code\n    if (lowerColor.startsWith('#')) {\n      return lowerColor;\n    }\n    // 2. Check if it's an Ink supported name (lowercase)\n    else if (Theme.inkSupportedNames.has(lowerColor)) {\n      return lowerColor;\n    }\n\n    // 3. Could not resolve\n    console.warn(\n      `[Theme] Could not resolve color \"${colorValue}\" to an Ink-compatible format.`,\n    );\n    return undefined;\n  }\n\n  protected _buildColorMap(\n    hljsTheme: Record<string, CSSProperties>,\n  ): Record<string, string> {\n    const inkTheme: Record<string, string> = {};\n    for (const key in hljsTheme) {\n      // Ensure the key starts with 'hljs-' or is 'hljs' for the base style\n      if (!key.startsWith('hljs-') && key !== 'hljs') {\n        continue;\n      }\n\n      const style = hljsTheme[key];\n      if (style?.color) {\n        const resolvedColor = Theme._resolveColor(style.color);\n        if (resolvedColor !== undefined) {\n          inkTheme[key] = resolvedColor;\n        }\n      }\n    }\n    return inkTheme;\n  }\n}\n\n// Default themes\nexport const DEFAULT_THEMES = {\n  light: lightTheme,\n  dark: darkTheme,\n  ansi: ansiTheme,\n} as const;\n\nexport type ThemeName = keyof typeof DEFAULT_THEMES;\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { Theme, lightTheme } from './theme.js';\n\n// Default light theme with syntax highlighting\nconst defaultLightSyntax = {\n  'hljs': {\n    color: lightTheme.Foreground,\n  },\n  'hljs-keyword': {\n    color: lightTheme.AccentPurple,\n  },\n  'hljs-string': {\n    color: lightTheme.AccentGreen,\n  },\n  'hljs-number': {\n    color: lightTheme.AccentYellow,\n  },\n  'hljs-comment': {\n    color: lightTheme.Comment,\n  },\n  'hljs-function': {\n    color: lightTheme.AccentBlue,\n  },\n  'hljs-variable': {\n    color: lightTheme.AccentCyan,\n  },\n  'hljs-type': {\n    color: lightTheme.AccentPurple,\n  },\n  'hljs-class': {\n    color: lightTheme.AccentBlue,\n  },\n  'hljs-operator': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-punctuation': {\n    color: lightTheme.Gray,\n  },\n  'hljs-property': {\n    color: lightTheme.AccentCyan,\n  },\n  'hljs-method': {\n    color: lightTheme.AccentBlue,\n  },\n  'hljs-constant': {\n    color: lightTheme.AccentYellow,\n  },\n  'hljs-boolean': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-null': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-undefined': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-tag': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-attribute': {\n    color: lightTheme.AccentYellow,\n  },\n  'hljs-value': {\n    color: lightTheme.AccentGreen,\n  },\n  'hljs-selector': {\n    color: lightTheme.AccentPurple,\n  },\n  'hljs-rule': {\n    color: lightTheme.AccentBlue,\n  },\n  'hljs-important': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-emphasis': {\n    color: lightTheme.AccentYellow,\n  },\n  'hljs-strong': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-title': {\n    color: lightTheme.AccentBlue,\n  },\n  'hljs-section': {\n    color: lightTheme.AccentPurple,\n  },\n  'hljs-quote': {\n    color: lightTheme.Comment,\n  },\n  'hljs-name': {\n    color: lightTheme.AccentCyan,\n  },\n  'hljs-built_in': {\n    color: lightTheme.AccentPurple,\n  },\n  'hljs-literal': {\n    color: lightTheme.AccentYellow,\n  },\n  'hljs-params': {\n    color: lightTheme.Foreground,\n  },\n  'hljs-meta': {\n    color: lightTheme.Comment,\n  },\n  'hljs-link': {\n    color: lightTheme.AccentBlue,\n  },\n  'hljs-symbol': {\n    color: lightTheme.AccentCyan,\n  },\n  'hljs-bullet': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-code': {\n    color: lightTheme.AccentGreen,\n  },\n  'hljs-formula': {\n    color: lightTheme.AccentYellow,\n  },\n  'hljs-doctag': {\n    color: lightTheme.Comment,\n  },\n  'hljs-deletion': {\n    color: lightTheme.AccentRed,\n  },\n  'hljs-addition': {\n    color: lightTheme.AccentGreen,\n  },\n};\n\nexport const DefaultLight = new Theme(\n  'DefaultLight',\n  'light',\n  defaultLightSyntax,\n  lightTheme,\n);\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { Theme, darkTheme } from './theme.js';\n\n// Default dark theme with syntax highlighting\nconst defaultDarkSyntax = {\n  'hljs': {\n    color: darkTheme.Foreground,\n  },\n  'hljs-keyword': {\n    color: darkTheme.AccentPurple,\n  },\n  'hljs-string': {\n    color: darkTheme.AccentGreen,\n  },\n  'hljs-number': {\n    color: darkTheme.AccentYellow,\n  },\n  'hljs-comment': {\n    color: darkTheme.Comment,\n  },\n  'hljs-function': {\n    color: darkTheme.AccentBlue,\n  },\n  'hljs-variable': {\n    color: darkTheme.AccentCyan,\n  },\n  'hljs-type': {\n    color: darkTheme.AccentPurple,\n  },\n  'hljs-class': {\n    color: darkTheme.AccentBlue,\n  },\n  'hljs-operator': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-punctuation': {\n    color: darkTheme.Gray,\n  },\n  'hljs-property': {\n    color: darkTheme.AccentCyan,\n  },\n  'hljs-method': {\n    color: darkTheme.AccentBlue,\n  },\n  'hljs-constant': {\n    color: darkTheme.AccentYellow,\n  },\n  'hljs-boolean': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-null': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-undefined': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-tag': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-attribute': {\n    color: darkTheme.AccentYellow,\n  },\n  'hljs-value': {\n    color: darkTheme.AccentGreen,\n  },\n  'hljs-selector': {\n    color: darkTheme.AccentPurple,\n  },\n  'hljs-rule': {\n    color: darkTheme.AccentBlue,\n  },\n  'hljs-important': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-emphasis': {\n    color: darkTheme.AccentYellow,\n  },\n  'hljs-strong': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-title': {\n    color: darkTheme.AccentBlue,\n  },\n  'hljs-section': {\n    color: darkTheme.AccentPurple,\n  },\n  'hljs-quote': {\n    color: darkTheme.Comment,\n  },\n  'hljs-name': {\n    color: darkTheme.AccentCyan,\n  },\n  'hljs-built_in': {\n    color: darkTheme.AccentPurple,\n  },\n  'hljs-literal': {\n    color: darkTheme.AccentYellow,\n  },\n  'hljs-params': {\n    color: darkTheme.Foreground,\n  },\n  'hljs-meta': {\n    color: darkTheme.Comment,\n  },\n  'hljs-link': {\n    color: darkTheme.AccentBlue,\n  },\n  'hljs-symbol': {\n    color: darkTheme.AccentCyan,\n  },\n  'hljs-bullet': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-code': {\n    color: darkTheme.AccentGreen,\n  },\n  'hljs-formula': {\n    color: darkTheme.AccentYellow,\n  },\n  'hljs-doctag': {\n    color: darkTheme.Comment,\n  },\n  'hljs-deletion': {\n    color: darkTheme.AccentRed,\n  },\n  'hljs-addition': {\n    color: darkTheme.AccentGreen,\n  },\n};\n\nexport const DefaultDark = new Theme(\n  'DefaultDark',\n  'dark',\n  defaultDarkSyntax,\n  darkTheme,\n);\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { Theme, ansiTheme } from './theme.js';\n\n// ANSI theme with basic color support\nconst ansiSyntax = {\n  'hljs': {\n    color: 'white',\n  },\n  'hljs-keyword': {\n    color: 'magenta',\n  },\n  'hljs-string': {\n    color: 'green',\n  },\n  'hljs-number': {\n    color: 'yellow',\n  },\n  'hljs-comment': {\n    color: 'gray',\n  },\n  'hljs-function': {\n    color: 'blue',\n  },\n  'hljs-variable': {\n    color: 'cyan',\n  },\n  'hljs-type': {\n    color: 'magenta',\n  },\n  'hljs-class': {\n    color: 'blue',\n  },\n  'hljs-operator': {\n    color: 'red',\n  },\n  'hljs-punctuation': {\n    color: 'gray',\n  },\n  'hljs-property': {\n    color: 'cyan',\n  },\n  'hljs-method': {\n    color: 'blue',\n  },\n  'hljs-constant': {\n    color: 'yellow',\n  },\n  'hljs-boolean': {\n    color: 'red',\n  },\n  'hljs-null': {\n    color: 'red',\n  },\n  'hljs-undefined': {\n    color: 'red',\n  },\n  'hljs-tag': {\n    color: 'red',\n  },\n  'hljs-attribute': {\n    color: 'yellow',\n  },\n  'hljs-value': {\n    color: 'green',\n  },\n  'hljs-selector': {\n    color: 'magenta',\n  },\n  'hljs-rule': {\n    color: 'blue',\n  },\n  'hljs-important': {\n    color: 'red',\n  },\n  'hljs-emphasis': {\n    color: 'yellow',\n  },\n  'hljs-strong': {\n    color: 'red',\n  },\n  'hljs-title': {\n    color: 'blue',\n  },\n  'hljs-section': {\n    color: 'magenta',\n  },\n  'hljs-quote': {\n    color: 'gray',\n  },\n  'hljs-name': {\n    color: 'cyan',\n  },\n  'hljs-built_in': {\n    color: 'magenta',\n  },\n  'hljs-literal': {\n    color: 'yellow',\n  },\n  'hljs-params': {\n    color: 'white',\n  },\n  'hljs-meta': {\n    color: 'gray',\n  },\n  'hljs-link': {\n    color: 'blue',\n  },\n  'hljs-symbol': {\n    color: 'cyan',\n  },\n  'hljs-bullet': {\n    color: 'red',\n  },\n  'hljs-code': {\n    color: 'green',\n  },\n  'hljs-formula': {\n    color: 'yellow',\n  },\n  'hljs-doctag': {\n    color: 'gray',\n  },\n  'hljs-deletion': {\n    color: 'red',\n  },\n  'hljs-addition': {\n    color: 'green',\n  },\n};\n\nexport const ANSI = new Theme(\n  'ANSI',\n  'ansi',\n  ansiSyntax,\n  ansiTheme,\n);\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { Theme, ansiTheme } from './theme.js';\n\n// ANSI light theme - similar to ANSI but optimized for light backgrounds\nconst ansiLightSyntax = {\n  'hljs': {\n    color: 'black',\n  },\n  'hljs-keyword': {\n    color: 'magenta',\n  },\n  'hljs-string': {\n    color: 'green',\n  },\n  'hljs-number': {\n    color: 'blue',\n  },\n  'hljs-comment': {\n    color: 'gray',\n  },\n  'hljs-function': {\n    color: 'blue',\n  },\n  'hljs-variable': {\n    color: 'cyan',\n  },\n  'hljs-type': {\n    color: 'magenta',\n  },\n  'hljs-class': {\n    color: 'blue',\n  },\n  'hljs-operator': {\n    color: 'red',\n  },\n  'hljs-punctuation': {\n    color: 'gray',\n  },\n  'hljs-property': {\n    color: 'cyan',\n  },\n  'hljs-method': {\n    color: 'blue',\n  },\n  'hljs-constant': {\n    color: 'blue',\n  },\n  'hljs-boolean': {\n    color: 'red',\n  },\n  'hljs-null': {\n    color: 'red',\n  },\n  'hljs-undefined': {\n    color: 'red',\n  },\n  'hljs-tag': {\n    color: 'red',\n  },\n  'hljs-attribute': {\n    color: 'blue',\n  },\n  'hljs-value': {\n    color: 'green',\n  },\n  'hljs-selector': {\n    color: 'magenta',\n  },\n  'hljs-rule': {\n    color: 'blue',\n  },\n  'hljs-important': {\n    color: 'red',\n  },\n  'hljs-emphasis': {\n    color: 'blue',\n  },\n  'hljs-strong': {\n    color: 'red',\n  },\n  'hljs-title': {\n    color: 'blue',\n  },\n  'hljs-section': {\n    color: 'magenta',\n  },\n  'hljs-quote': {\n    color: 'gray',\n  },\n  'hljs-name': {\n    color: 'cyan',\n  },\n  'hljs-built_in': {\n    color: 'magenta',\n  },\n  'hljs-literal': {\n    color: 'blue',\n  },\n  'hljs-params': {\n    color: 'black',\n  },\n  'hljs-meta': {\n    color: 'gray',\n  },\n  'hljs-link': {\n    color: 'blue',\n  },\n  'hljs-symbol': {\n    color: 'cyan',\n  },\n  'hljs-bullet': {\n    color: 'red',\n  },\n  'hljs-code': {\n    color: 'green',\n  },\n  'hljs-formula': {\n    color: 'blue',\n  },\n  'hljs-doctag': {\n    color: 'gray',\n  },\n  'hljs-deletion': {\n    color: 'red',\n  },\n  'hljs-addition': {\n    color: 'green',\n  },\n};\n\nconst ansiLightTheme = {\n  ...ansiTheme,\n  type: 'light' as const,\n  Background: 'white',\n  Foreground: 'black',\n};\n\nexport const ANSILight = new Theme(\n  'ANSILight',\n  'light',\n  ansiLightSyntax,\n  ansiLightTheme,\n);\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { Theme, ColorsTheme } from './theme.js';\n\n// No-color theme for accessibility and NO_COLOR environment variable support\nconst noColorTheme: ColorsTheme = {\n  type: 'ansi',\n  Background: '',\n  Foreground: '',\n  LightBlue: '',\n  AccentBlue: '',\n  AccentPurple: '',\n  AccentCyan: '',\n  AccentGreen: '',\n  AccentYellow: '',\n  AccentRed: '',\n  Comment: '',\n  Gray: '',\n};\n\n// No-color syntax - all colors are empty strings\nconst noColorSyntax = {\n  'hljs': {\n    color: '',\n  },\n  'hljs-keyword': {\n    color: '',\n  },\n  'hljs-string': {\n    color: '',\n  },\n  'hljs-number': {\n    color: '',\n  },\n  'hljs-comment': {\n    color: '',\n  },\n  'hljs-function': {\n    color: '',\n  },\n  'hljs-variable': {\n    color: '',\n  },\n  'hljs-type': {\n    color: '',\n  },\n  'hljs-class': {\n    color: '',\n  },\n  'hljs-operator': {\n    color: '',\n  },\n  'hljs-punctuation': {\n    color: '',\n  },\n  'hljs-property': {\n    color: '',\n  },\n  'hljs-method': {\n    color: '',\n  },\n  'hljs-constant': {\n    color: '',\n  },\n  'hljs-boolean': {\n    color: '',\n  },\n  'hljs-null': {\n    color: '',\n  },\n  'hljs-undefined': {\n    color: '',\n  },\n  'hljs-tag': {\n    color: '',\n  },\n  'hljs-attribute': {\n    color: '',\n  },\n  'hljs-value': {\n    color: '',\n  },\n  'hljs-selector': {\n    color: '',\n  },\n  'hljs-rule': {\n    color: '',\n  },\n  'hljs-important': {\n    color: '',\n  },\n  'hljs-emphasis': {\n    color: '',\n  },\n  'hljs-strong': {\n    color: '',\n  },\n  'hljs-title': {\n    color: '',\n  },\n  'hljs-section': {\n    color: '',\n  },\n  'hljs-quote': {\n    color: '',\n  },\n  'hljs-name': {\n    color: '',\n  },\n  'hljs-built_in': {\n    color: '',\n  },\n  'hljs-literal': {\n    color: '',\n  },\n  'hljs-params': {\n    color: '',\n  },\n  'hljs-meta': {\n    color: '',\n  },\n  'hljs-link': {\n    color: '',\n  },\n  'hljs-symbol': {\n    color: '',\n  },\n  'hljs-bullet': {\n    color: '',\n  },\n  'hljs-code': {\n    color: '',\n  },\n  'hljs-formula': {\n    color: '',\n  },\n  'hljs-doctag': {\n    color: '',\n  },\n  'hljs-deletion': {\n    color: '',\n  },\n  'hljs-addition': {\n    color: '',\n  },\n};\n\nexport const NoColorTheme = new Theme(\n  'NoColor',\n  'ansi',\n  noColorSyntax,\n  noColorTheme,\n);\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { DefaultLight } from './default-light.js';\nimport { DefaultDark } from './default.js';\nimport { Theme, ThemeType, DEFAULT_THEMES } from './theme.js';\nimport { ANSI } from './ansi.js';\nimport { ANSILight } from './ansi-light.js';\nimport { NoColorTheme } from './no-color.js';\nimport process from 'node:process';\n\nexport interface ThemeDisplay {\n  name: string;\n  type: ThemeType;\n}\n\nexport const DEFAULT_THEME: Theme = DefaultDark;\n\nclass ThemeManager {\n  private readonly availableThemes: Theme[];\n  private activeTheme: Theme;\n\n  constructor() {\n    this.availableThemes = [\n      DefaultDark,\n      DefaultLight,\n      ANSI,\n      ANSILight,\n      NoColorTheme,\n    ];\n    this.activeTheme = DEFAULT_THEME;\n  }\n\n  /**\n   * Returns a list of available theme names.\n   */\n  getAvailableThemes(): ThemeDisplay[] {\n    const sortedThemes = [...this.availableThemes].sort((a, b) => {\n      const typeOrder = (type: ThemeType): number => {\n        switch (type) {\n          case 'dark':\n            return 1;\n          case 'light':\n            return 2;\n          default:\n            return 3;\n        }\n      };\n\n      const typeComparison = typeOrder(a.type) - typeOrder(b.type);\n      if (typeComparison !== 0) {\n        return typeComparison;\n      }\n      return a.name.localeCompare(b.name);\n    });\n\n    return sortedThemes.map((theme) => ({\n      name: theme.name,\n      type: theme.type,\n    }));\n  }\n\n  /**\n   * Sets the active theme.\n   * @param themeName The name of the theme to activate.\n   * @returns True if the theme was successfully set, false otherwise.\n   */\n  setActiveTheme(themeName: string | undefined): boolean {\n    const foundTheme = this.findThemeByName(themeName);\n\n    if (foundTheme) {\n      this.activeTheme = foundTheme;\n      return true;\n    } else {\n      // If themeName is undefined, it means we want to set the default theme.\n      if (themeName === undefined) {\n        this.activeTheme = DEFAULT_THEME;\n        return true;\n      }\n      return false;\n    }\n  }\n\n  /**\n   * Gets the currently active theme.\n   */\n  getActiveTheme(): Theme {\n    return this.activeTheme;\n  }\n\n  /**\n   * Gets the name of the currently active theme.\n   */\n  getActiveThemeName(): string {\n    return this.activeTheme.name;\n  }\n\n  /**\n   * Finds a theme by name.\n   * @param themeName The name of the theme to find.\n   * @returns The theme if found, undefined otherwise.\n   */\n  private findThemeByName(themeName: string | undefined): Theme | undefined {\n    if (!themeName) {\n      return undefined;\n    }\n\n    return this.availableThemes.find(\n      (theme) => theme.name.toLowerCase() === themeName.toLowerCase(),\n    );\n  }\n\n  /**\n   * Determines if NO_COLOR environment variable is set and should use no-color theme.\n   */\n  shouldUseNoColor(): boolean {\n    return process.env.NO_COLOR !== undefined && process.env.NO_COLOR !== '';\n  }\n\n  /**\n   * Auto-detects the appropriate theme based on environment.\n   */\n  autoDetectTheme(): string {\n    if (this.shouldUseNoColor()) {\n      return 'NoColor';\n    }\n\n    // Check if terminal supports colors\n    const colorTerm = process.env.COLORTERM;\n    const term = process.env.TERM;\n\n    if (colorTerm === 'truecolor' || term?.includes('256color')) {\n      return 'DefaultDark'; // Use rich colors\n    } else if (term?.includes('color')) {\n      return 'ANSI'; // Use basic ANSI colors\n    } else {\n      return 'NoColor'; // Fallback to no colors\n    }\n  }\n\n  /**\n   * Initializes the theme manager with user preferences.\n   */\n  initialize(userTheme?: string): void {\n    let themeToUse = userTheme;\n\n    if (!themeToUse) {\n      themeToUse = this.autoDetectTheme();\n    }\n\n    if (!this.setActiveTheme(themeToUse)) {\n      console.warn(`Theme \"${themeToUse}\" not found, using default theme.`);\n      this.setActiveTheme(undefined); // Use default\n    }\n  }\n\n  /**\n   * Gets theme by type (light/dark/ansi).\n   */\n  getThemesByType(type: ThemeType): Theme[] {\n    return this.availableThemes.filter((theme) => theme.type === type);\n  }\n\n  /**\n   * Switches between light and dark variants of the current theme.\n   */\n  toggleThemeVariant(): boolean {\n    const currentType = this.activeTheme.type;\n    const targetType = currentType === 'dark' ? 'light' : 'dark';\n    \n    const variants = this.getThemesByType(targetType);\n    if (variants.length > 0) {\n      this.activeTheme = variants[0];\n      return true;\n    }\n    \n    return false;\n  }\n}\n\n// Export singleton instance\nexport const themeManager = new ThemeManager();\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport fs from 'fs/promises';\nimport os from 'os';\nimport { join as pathJoin } from 'node:path';\nimport { getErrorMessage } from '@arien/arien-cli-core';\n\nconst warningsFilePath = pathJoin(os.tmpdir(), 'arien-cli-warnings.txt');\n\nexport async function getStartupWarnings(): Promise<string[]> {\n  try {\n    await fs.access(warningsFilePath); // Check if file exists\n    const warningsContent = await fs.readFile(warningsFilePath, 'utf-8');\n    const warnings = warningsContent\n      .split('\\n')\n      .filter((line) => line.trim() !== '');\n    try {\n      await fs.unlink(warningsFilePath);\n    } catch {\n      warnings.push('Warning: Could not delete temporary warnings file.');\n    }\n    return warnings;\n  } catch (err: unknown) {\n    // If fs.access throws, it means the file doesn't exist or is not accessible.\n    // This is not an error in the context of fetching warnings, so return empty.\n    // Only return an error message if it's not a \"file not found\" type error.\n    // However, the original logic returned an error message for any fs.existsSync failure.\n    // To maintain closer parity while making it async, we'll check the error code.\n    // ENOENT is \"Error NO ENTry\" (file not found).\n    if (err instanceof Error && 'code' in err && err.code === 'ENOENT') {\n      return []; // File not found, no warnings to return.\n    }\n    // For other errors (permissions, etc.), return the error message.\n    return [`Error checking/reading warnings file: ${getErrorMessage(err)}`];\n  }\n}\n\nexport async function addStartupWarning(warning: string): Promise<void> {\n  try {\n    const existingWarnings = await getStartupWarningsContent();\n    const newWarnings = existingWarnings ? `${existingWarnings}\\n${warning}` : warning;\n    await fs.writeFile(warningsFilePath, newWarnings, 'utf-8');\n  } catch (err) {\n    console.error('Failed to add startup warning:', getErrorMessage(err));\n  }\n}\n\nexport async function clearStartupWarnings(): Promise<void> {\n  try {\n    await fs.unlink(warningsFilePath);\n  } catch (err) {\n    // Ignore errors if file doesn't exist\n    if (err instanceof Error && 'code' in err && err.code !== 'ENOENT') {\n      console.error('Failed to clear startup warnings:', getErrorMessage(err));\n    }\n  }\n}\n\nasync function getStartupWarningsContent(): Promise<string | null> {\n  try {\n    return await fs.readFile(warningsFilePath, 'utf-8');\n  } catch (err) {\n    if (err instanceof Error && 'code' in err && err.code === 'ENOENT') {\n      return null; // File doesn't exist\n    }\n    throw err;\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport {\n  Config,\n  ToolCallRequestInfo,\n  executeToolCall,\n  ToolRegistry,\n  shutdownTelemetry,\n  isTelemetrySdkInitialized,\n} from '@arien/arien-cli-core';\nimport {\n  Content,\n  Part,\n  FunctionCall,\n  GenerateContentResponse,\n} from '@google/genai';\n\nimport { parseAndFormatApiError } from './ui/utils/errorParsing.js';\n\nfunction getResponseText(response: GenerateContentResponse): string | null {\n  if (response.candidates && response.candidates.length > 0) {\n    const candidate = response.candidates[0];\n    if (\n      candidate.content &&\n      candidate.content.parts &&\n      candidate.content.parts.length > 0\n    ) {\n      // We are running in headless mode so we don't need to return thoughts to STDOUT.\n      const thoughtPart = candidate.content.parts[0];\n      if (thoughtPart?.thought) {\n        return null;\n      }\n      return candidate.content.parts\n        .filter((part) => part.text)\n        .map((part) => part.text)\n        .join('');\n    }\n  }\n  return null;\n}\n\nexport async function runNonInteractive(\n  config: Config,\n  input: string,\n): Promise<void> {\n  // Handle EPIPE errors when the output is piped to a command that closes early.\n  process.stdout.on('error', (err: NodeJS.ErrnoException) => {\n    if (err.code === 'EPIPE') {\n      // Exit gracefully if the pipe is closed.\n      process.exit(0);\n    }\n  });\n\n  const geminiClient = config.getGeminiClient();\n  const toolRegistry: ToolRegistry = await config.getToolRegistry();\n\n  const chat = await geminiClient.getChat();\n  const abortController = new AbortController();\n  let currentMessages: Content[] = [{ role: 'user', parts: [{ text: input }] }];\n\n  try {\n    while (true) {\n      const functionCalls: FunctionCall[] = [];\n\n      const responseStream = await chat.sendMessageStream({\n        message: currentMessages[0]?.parts || [], // Ensure parts are always provided\n        config: {\n          abortSignal: abortController.signal,\n          tools: [\n            { functionDeclarations: toolRegistry.getFunctionDeclarations() },\n          ],\n        },\n      });\n\n      for await (const resp of responseStream) {\n        if (abortController.signal.aborted) {\n          console.error('Operation cancelled.');\n          return;\n        }\n        const textPart = getResponseText(resp);\n        if (textPart) {\n          process.stdout.write(textPart);\n        }\n        if (resp.functionCalls) {\n          functionCalls.push(...resp.functionCalls);\n        }\n      }\n\n      if (functionCalls.length > 0) {\n        const toolResponseParts: Part[] = [];\n\n        for (const fc of functionCalls) {\n          const callId = fc.id ?? `${fc.name}-${Date.now()}`;\n          const requestInfo: ToolCallRequestInfo = {\n            callId,\n            name: fc.name as string,\n            args: fc.args || {},\n          };\n\n          try {\n            const result = await executeToolCall(\n              requestInfo,\n              toolRegistry,\n              config,\n              abortController.signal,\n            );\n\n            toolResponseParts.push({\n              functionResponse: {\n                name: fc.name,\n                response: result,\n              },\n            });\n          } catch (error) {\n            console.error(`Error executing tool ${fc.name}:`, error);\n            toolResponseParts.push({\n              functionResponse: {\n                name: fc.name,\n                response: {\n                  error: error instanceof Error ? error.message : String(error),\n                },\n              },\n            });\n          }\n        }\n\n        // Continue the conversation with tool responses\n        currentMessages = [\n          {\n            role: 'model',\n            parts: functionCalls.map((fc) => ({\n              functionCall: {\n                name: fc.name,\n                args: fc.args || {},\n              },\n            })),\n          },\n          {\n            role: 'user',\n            parts: toolResponseParts,\n          },\n        ];\n      } else {\n        // No more function calls, we're done\n        break;\n      }\n    }\n  } catch (error) {\n    const errorMessage = parseAndFormatApiError(error);\n    console.error('\\nError:', errorMessage);\n    process.exit(1);\n  } finally {\n    // Shutdown telemetry if it was initialized\n    if (isTelemetrySdkInitialized()) {\n      await shutdownTelemetry();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nexport interface ParsedError {\n  message: string;\n  stack?: string;\n  code?: string;\n  type: 'error' | 'warning' | 'info';\n  file?: string;\n  line?: number;\n  column?: number;\n}\n\nexport function parseError(error: unknown): ParsedError {\n  if (error instanceof Error) {\n    return {\n      message: error.message,\n      stack: error.stack,\n      type: 'error',\n    };\n  }\n\n  if (typeof error === 'string') {\n    return {\n      message: error,\n      type: 'error',\n    };\n  }\n\n  if (typeof error === 'object' && error !== null) {\n    const obj = error as Record<string, unknown>;\n    return {\n      message: String(obj.message || obj.toString?.() || 'Unknown error'),\n      stack: typeof obj.stack === 'string' ? obj.stack : undefined,\n      code: typeof obj.code === 'string' ? obj.code : undefined,\n      type: 'error',\n      file: typeof obj.file === 'string' ? obj.file : undefined,\n      line: typeof obj.line === 'number' ? obj.line : undefined,\n      column: typeof obj.column === 'number' ? obj.column : undefined,\n    };\n  }\n\n  return {\n    message: 'Unknown error occurred',\n    type: 'error',\n  };\n}\n\nexport function formatError(error: ParsedError): string {\n  let formatted = error.message;\n\n  if (error.file) {\n    formatted += ` (${error.file}`;\n    if (error.line) {\n      formatted += `:${error.line}`;\n      if (error.column) {\n        formatted += `:${error.column}`;\n      }\n    }\n    formatted += ')';\n  }\n\n  if (error.code) {\n    formatted += ` [${error.code}]`;\n  }\n\n  return formatted;\n}\n\nexport function getErrorMessage(error: unknown): string {\n  const parsed = parseError(error);\n  return formatError(parsed);\n}\n\n/**\n * Alias for getErrorMessage for API error handling\n */\nexport function parseAndFormatApiError(error: unknown): string {\n  return getErrorMessage(error);\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { MCPServerConfig, Config } from '@arien/arien-cli-core';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport * as os from 'os';\n\nexport const EXTENSIONS_DIRECTORY_NAME = path.join('.arien', 'extensions');\nexport const EXTENSIONS_CONFIG_FILENAME = 'arien-extension.json';\n\nexport interface Extension {\n  name: string;\n  version: string;\n  config: ExtensionConfig;\n  contextFiles: string[];\n  configure?: (config: Config) => void;\n}\n\nexport interface ExtensionConfig {\n  name: string;\n  version: string;\n  mcpServers?: Record<string, MCPServerConfig>;\n  contextFileName?: string | string[];\n  excludeTools?: string[];\n  includeTools?: string[];\n}\n\nexport function loadExtensions(workspaceDir: string): Extension[] {\n  const allExtensions = [\n    ...loadExtensionsFromDir(workspaceDir),\n    ...loadExtensionsFromDir(os.homedir()),\n  ];\n\n  const uniqueExtensions: Extension[] = [];\n  const seenNames = new Set<string>();\n  for (const extension of allExtensions) {\n    if (!seenNames.has(extension.config.name)) {\n      console.log(\n        `Loading extension: ${extension.config.name} (version: ${extension.config.version})`,\n      );\n      uniqueExtensions.push(extension);\n      seenNames.add(extension.config.name);\n    }\n  }\n\n  return uniqueExtensions;\n}\n\nfunction loadExtensionsFromDir(dir: string): Extension[] {\n  const extensionsDir = path.join(dir, EXTENSIONS_DIRECTORY_NAME);\n  if (!fs.existsSync(extensionsDir)) {\n    return [];\n  }\n\n  const extensions: Extension[] = [];\n  for (const subdir of fs.readdirSync(extensionsDir)) {\n    const extensionDir = path.join(extensionsDir, subdir);\n\n    const extension = loadExtension(extensionDir);\n    if (extension != null) {\n      extensions.push(extension);\n    }\n  }\n  return extensions;\n}\n\nfunction loadExtension(extensionDir: string): Extension | null {\n  if (!fs.statSync(extensionDir).isDirectory()) {\n    console.error(\n      `Warning: unexpected file ${extensionDir} in extensions directory.`,\n    );\n    return null;\n  }\n\n  const configFilePath = path.join(extensionDir, EXTENSIONS_CONFIG_FILENAME);\n  if (!fs.existsSync(configFilePath)) {\n    console.error(\n      `Warning: extension directory ${extensionDir} does not contain a config file ${configFilePath}.`,\n    );\n    return null;\n  }\n\n  try {\n    const configContent = fs.readFileSync(configFilePath, 'utf-8');\n    const config = JSON.parse(configContent) as ExtensionConfig;\n    if (!config.name || !config.version) {\n      console.error(\n        `Invalid extension config in ${configFilePath}: missing name or version.`,\n      );\n      return null;\n    }\n\n    const contextFiles = getContextFileNames(config)\n      .map((contextFileName) => path.join(extensionDir, contextFileName))\n      .filter((contextFilePath) => fs.existsSync(contextFilePath));\n\n    // Try to load the extension's main module if it exists\n    let configure: ((config: Config) => void) | undefined;\n    const mainModulePath = path.join(extensionDir, 'index.js');\n    if (fs.existsSync(mainModulePath)) {\n      try {\n        const extensionModule = require(mainModulePath);\n        if (typeof extensionModule.configure === 'function') {\n          configure = extensionModule.configure;\n        }\n      } catch (error) {\n        console.error(\n          `Warning: failed to load extension module ${mainModulePath}:`,\n          error,\n        );\n      }\n    }\n\n    return {\n      name: config.name,\n      version: config.version,\n      config,\n      contextFiles,\n      configure,\n    };\n  } catch (error) {\n    console.error(`Error loading extension config from ${configFilePath}:`, error);\n    return null;\n  }\n}\n\nfunction getContextFileNames(config: ExtensionConfig): string[] {\n  if (!config.contextFileName) {\n    return [];\n  }\n\n  if (Array.isArray(config.contextFileName)) {\n    return config.contextFileName;\n  }\n\n  return [config.contextFileName];\n}\n\nexport function createExtensionTemplate(extensionDir: string, name: string, version: string = '1.0.0') {\n  const config: ExtensionConfig = {\n    name,\n    version,\n    contextFileName: 'CONTEXT.md',\n  };\n\n  const configPath = path.join(extensionDir, EXTENSIONS_CONFIG_FILENAME);\n  const contextPath = path.join(extensionDir, 'CONTEXT.md');\n  const indexPath = path.join(extensionDir, 'index.js');\n\n  try {\n    fs.mkdirSync(extensionDir, { recursive: true });\n\n    // Create config file\n    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));\n\n    // Create context file\n    const contextContent = `# ${name} Extension\n\nThis is the context file for the ${name} extension.\n\nAdd your extension's documentation and context here.\n`;\n    fs.writeFileSync(contextPath, contextContent);\n\n    // Create main module file\n    const indexContent = `/**\n * ${name} Extension\n * Version: ${version}\n */\n\n/**\n * Configure the extension\n * @param {Config} config - The Arien CLI configuration object\n */\nfunction configure(config) {\n  // Add your extension configuration here\n  console.log('Configuring ${name} extension');\n}\n\nmodule.exports = {\n  configure,\n};\n`;\n    fs.writeFileSync(indexPath, indexContent);\n\n    console.log(`Extension template created at ${extensionDir}`);\n    return true;\n  } catch (error) {\n    console.error(`Failed to create extension template:`, error);\n    return false;\n  }\n}\n\nexport function validateExtensionConfig(config: ExtensionConfig): string[] {\n  const errors: string[] = [];\n\n  if (!config.name) {\n    errors.push('Extension name is required');\n  }\n\n  if (!config.version) {\n    errors.push('Extension version is required');\n  }\n\n  // Validate MCP servers if present\n  if (config.mcpServers) {\n    for (const [serverName, serverConfig] of Object.entries(config.mcpServers)) {\n      if (!serverConfig.command) {\n        errors.push(`MCP server \"${serverName}\" is missing command`);\n      }\n    }\n  }\n\n  return errors;\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { promises as fs } from 'fs';\nimport { join } from 'path';\nimport { getProjectTempDir } from '@arien/arien-cli-core';\n\nexport async function cleanupCheckpoints() {\n  const tempDir = getProjectTempDir(process.cwd());\n  const checkpointsDir = join(tempDir, 'checkpoints');\n  try {\n    await fs.rm(checkpointsDir, { recursive: true, force: true });\n  } catch {\n    // Ignore errors if the directory doesn't exist or fails to delete.\n  }\n}\n\nexport async function cleanupTempFiles() {\n  const tempDir = getProjectTempDir(process.cwd());\n  try {\n    await fs.rm(tempDir, { recursive: true, force: true });\n  } catch {\n    // Ignore errors if the directory doesn't exist or fails to delete.\n  }\n}\n\nexport async function cleanupOldFiles(directory: string, maxAgeMs: number) {\n  try {\n    const files = await fs.readdir(directory);\n    const now = Date.now();\n    \n    for (const file of files) {\n      const filePath = join(directory, file);\n      try {\n        const stats = await fs.stat(filePath);\n        const ageMs = now - stats.mtime.getTime();\n        \n        if (ageMs > maxAgeMs) {\n          await fs.rm(filePath, { recursive: true, force: true });\n        }\n      } catch {\n        // Ignore errors for individual files\n      }\n    }\n  } catch {\n    // Ignore errors if directory doesn't exist\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { AuthType } from '@arien/arien-cli-core';\n\nexport const validateAuthMethod = (authMethod: string): string | null => {\n  if (authMethod === AuthType.LOGIN_WITH_GOOGLE) {\n    return null;\n  }\n\n  if (authMethod === AuthType.USE_GEMINI) {\n    if (!process.env.GEMINI_API_KEY) {\n      return 'GEMINI_API_KEY environment variable not found. Add that to your .env and try again, no reload needed!';\n    }\n    return null;\n  }\n\n  if (authMethod === AuthType.USE_VERTEX_AI) {\n    const hasVertexProjectLocationConfig =\n      !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;\n    const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;\n    if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {\n      return (\n        'Must specify GOOGLE_GENAI_USE_VERTEXAI=true and either:\\n' +\n        '• GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\\n' +\n        '• GOOGLE_API_KEY environment variable (if using express mode).\\n' +\n        'Update your .env and try again, no reload needed!'\n      );\n    }\n    return null;\n  }\n\n  return 'Invalid auth method selected.';\n};\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { Fragment, useEffect, useId } from 'react';\nimport { Box, Text } from 'ink';\nimport stringWidth from 'string-width';\nimport { Colors } from '../../colors.js';\nimport { toCodePoints } from '../../utils/textUtils.js';\nimport { useOverflowActions } from '../../contexts/OverflowContext.js';\n\nlet enableDebugLog = false;\n\n/**\n * Minimum height for the MaxSizedBox component.\n * This ensures there is room for at least one line of content as well as the\n * message that content was truncated.\n */\nexport const MINIMUM_MAX_HEIGHT = 2;\n\nexport function setMaxSizedBoxDebugging(value: boolean) {\n  enableDebugLog = value;\n}\n\nfunction debugReportError(message: string, element: React.ReactNode) {\n  if (!enableDebugLog) return;\n\n  if (!React.isValidElement(element)) {\n    console.error(\n      message,\n      `Invalid element: '${String(element)}' typeof=${typeof element}`,\n    );\n    return;\n  }\n\n  let sourceMessage = '<Unknown file>';\n  try {\n    const elementWithSource = element as {\n      _source?: { fileName?: string; lineNumber?: number };\n    };\n    const fileName = elementWithSource._source?.fileName;\n    const lineNumber = elementWithSource._source?.lineNumber;\n    sourceMessage = fileName ? `${fileName}:${lineNumber}` : '<Unknown file>';\n  } catch (error) {\n    console.error('Error while trying to get file name:', error);\n  }\n\n  console.error(message, `${String(element.type)}. Source: ${sourceMessage}`);\n}\n\ninterface MaxSizedBoxProps {\n  children?: React.ReactNode;\n  maxWidth?: number;\n  maxHeight: number | undefined;\n  overflowDirection?: 'top' | 'bottom';\n  additionalHiddenLinesCount?: number;\n}\n\ninterface StyledText {\n  text: string;\n  color?: string;\n  backgroundColor?: string;\n  bold?: boolean;\n  italic?: boolean;\n  underline?: boolean;\n  strikethrough?: boolean;\n  inverse?: boolean;\n  dimColor?: boolean;\n  wrap?: 'wrap' | 'truncate' | 'truncate-start' | 'truncate-middle';\n}\n\n/**\n * A React component that constrains the size of its children and provides\n * content-aware truncation when the content exceeds the specified `maxHeight`.\n *\n * `MaxSizedBox` requires a specific structure for its children to correctly\n * measure and render the content:\n *\n * 1.  **Direct children must be `<Box>` elements.** Each `<Box>` represents a\n *     single row of content.\n * 2.  **Row `<Box>` elements must contain only `<Text>` elements.** These\n *     `<Text>` elements can be nested and there are no restrictions to Text\n *     element styling other than that non-wrapping text elements must be\n *     before wrapping text elements.\n *\n * **Constraints:**\n * - **Box Properties:** Custom properties on the child `<Box>` elements are\n *   ignored. In debug mode, runtime checks will report errors for any\n *   unsupported properties.\n * - **Text Wrapping:** Within a single row, `<Text>` elements with no wrapping\n *   (e.g., headers, labels) must appear before any `<Text>` elements that wrap.\n * - **Element Types:** Runtime checks will warn if unsupported element types\n *   are used as children.\n *\n * @example\n * <MaxSizedBox maxWidth={80} maxHeight={10}>\n *   <Box>\n *     <Text>This is the first line.</Text>\n *   </Box>\n *   <Box>\n *     <Text color=\"cyan\" wrap=\"truncate\">Non-wrapping Header: </Text>\n *     <Text>This is the rest of the line which will wrap if it's too long.</Text>\n *   </Box>\n *   <Box>\n *     <Text>\n *       Line 3 with <Text color=\"yellow\">nested styled text</Text> inside of it.\n *     </Text>\n *   </Box>\n * </MaxSizedBox>\n */\nexport const MaxSizedBox: React.FC<MaxSizedBoxProps> = ({\n  children,\n  maxWidth,\n  maxHeight,\n  overflowDirection = 'top',\n  additionalHiddenLinesCount = 0,\n}) => {\n  const id = useId();\n  const { addOverflowingId, removeOverflowingId } = useOverflowActions() || {};\n\n  const laidOutStyledText: StyledText[][] = [];\n  const targetMaxHeight = Math.max(\n    Math.round(maxHeight ?? Number.MAX_SAFE_INTEGER),\n    MINIMUM_MAX_HEIGHT,\n  );\n\n  if (maxWidth === undefined) {\n    throw new Error('maxWidth must be defined when maxHeight is set.');\n  }\n\n  function visitRows(element: React.ReactNode) {\n    if (!React.isValidElement<{ children?: React.ReactNode }>(element)) {\n      return;\n    }\n\n    if (element.type === Fragment) {\n      React.Children.forEach(element.props.children, visitRows);\n      return;\n    }\n\n    if (element.type === Box) {\n      layoutInkElementAsStyledText(element, maxWidth!, laidOutStyledText);\n      return;\n    }\n\n    debugReportError('MaxSizedBox children must be <Box> elements', element);\n  }\n\n  React.Children.forEach(children, visitRows);\n\n  const contentWillOverflow =\n    (targetMaxHeight !== undefined &&\n      laidOutStyledText.length > targetMaxHeight) ||\n    additionalHiddenLinesCount > 0;\n  const visibleContentHeight =\n    contentWillOverflow && targetMaxHeight !== undefined\n      ? targetMaxHeight - 1\n      : targetMaxHeight;\n\n  // Determine which lines to show based on overflow direction\n  let visibleLines: StyledText[][];\n  if (contentWillOverflow) {\n    if (overflowDirection === 'bottom') {\n      visibleLines = laidOutStyledText.slice(0, visibleContentHeight);\n    } else {\n      // 'top' - show the last lines\n      visibleLines = laidOutStyledText.slice(-visibleContentHeight);\n    }\n  } else {\n    visibleLines = laidOutStyledText;\n  }\n\n  // Track overflow state\n  useEffect(() => {\n    if (contentWillOverflow && addOverflowingId) {\n      addOverflowingId(id);\n    } else if (!contentWillOverflow && removeOverflowingId) {\n      removeOverflowingId(id);\n    }\n\n    return () => {\n      if (removeOverflowingId) {\n        removeOverflowingId(id);\n      }\n    };\n  }, [contentWillOverflow, id, addOverflowingId, removeOverflowingId]);\n\n  return (\n    <Box flexDirection=\"column\">\n      {visibleLines.map((line, lineIndex) => (\n        <Box key={lineIndex} flexDirection=\"row\">\n          {line.map((styledText, textIndex) => (\n            <Text\n              key={textIndex}\n              color={styledText.color}\n              backgroundColor={styledText.backgroundColor}\n              bold={styledText.bold}\n              italic={styledText.italic}\n              underline={styledText.underline}\n              strikethrough={styledText.strikethrough}\n              inverse={styledText.inverse}\n              dimColor={styledText.dimColor}\n              wrap={styledText.wrap}\n            >\n              {styledText.text}\n            </Text>\n          ))}\n        </Box>\n      ))}\n      {contentWillOverflow && (\n        <Box>\n          <Text color=\"gray\" dimColor>\n            ... {laidOutStyledText.length - visibleLines.length + additionalHiddenLinesCount} more lines\n          </Text>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\n/**\n * Simplified layout function for Ink elements as styled text\n */\nfunction layoutInkElementAsStyledText(\n  element: React.ReactElement,\n  maxWidth: number,\n  output: StyledText[][],\n): void {\n  const row: StyledText[] = [];\n  \n  function extractTextFromElement(el: React.ReactNode, inheritedStyle: Partial<StyledText> = {}): void {\n    if (typeof el === 'string') {\n      row.push({ text: el, ...inheritedStyle });\n      return;\n    }\n\n    if (typeof el === 'number') {\n      row.push({ text: String(el), ...inheritedStyle });\n      return;\n    }\n\n    if (!React.isValidElement(el)) {\n      return;\n    }\n\n    if (el.type === Text) {\n      const props = el.props as any;\n      const style: Partial<StyledText> = {\n        ...inheritedStyle,\n        color: props.color,\n        backgroundColor: props.backgroundColor,\n        bold: props.bold,\n        italic: props.italic,\n        underline: props.underline,\n        strikethrough: props.strikethrough,\n        inverse: props.inverse,\n        dimColor: props.dimColor,\n        wrap: props.wrap,\n      };\n\n      React.Children.forEach(props.children, (child) => {\n        extractTextFromElement(child, style);\n      });\n    } else {\n      // For other elements, just extract children\n      React.Children.forEach(el.props.children, (child) => {\n        extractTextFromElement(child, inheritedStyle);\n      });\n    }\n  }\n\n  extractTextFromElement(element);\n  \n  if (row.length > 0) {\n    output.push(row);\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\n\nexport interface OverflowState {\n  isOverflowing: boolean;\n  maxLines: number;\n  currentLines: number;\n  showMoreAvailable: boolean;\n}\n\nexport interface OverflowContextValue {\n  overflow: OverflowState;\n  setOverflow: (state: OverflowState) => void;\n  setMaxLines: (maxLines: number) => void;\n  setCurrentLines: (currentLines: number) => void;\n  toggleShowMore: () => void;\n  resetOverflow: () => void;\n}\n\nexport interface OverflowActionsValue {\n  addOverflowingId: (id: string) => void;\n  removeOverflowingId: (id: string) => void;\n  getOverflowingIds: () => string[];\n}\n\nconst OverflowContext = createContext<OverflowContextValue | undefined>(undefined);\nconst OverflowActionsContext = createContext<OverflowActionsValue | undefined>(undefined);\n\ninterface OverflowProviderProps {\n  children: ReactNode;\n  defaultMaxLines?: number;\n}\n\nexport const OverflowProvider: React.FC<OverflowProviderProps> = ({\n  children,\n  defaultMaxLines = 20\n}) => {\n  const [overflow, setOverflowState] = useState<OverflowState>({\n    isOverflowing: false,\n    maxLines: defaultMaxLines,\n    currentLines: 0,\n    showMoreAvailable: false,\n  });\n\n  const [overflowingIds, setOverflowingIds] = useState<Set<string>>(new Set());\n\n  const setOverflow = (state: OverflowState) => {\n    setOverflowState(state);\n  };\n\n  const setMaxLines = (maxLines: number) => {\n    setOverflowState(prev => ({\n      ...prev,\n      maxLines,\n      isOverflowing: prev.currentLines > maxLines,\n    }));\n  };\n\n  const setCurrentLines = (currentLines: number) => {\n    setOverflowState(prev => ({\n      ...prev,\n      currentLines,\n      isOverflowing: currentLines > prev.maxLines,\n      showMoreAvailable: currentLines > prev.maxLines,\n    }));\n  };\n\n  const toggleShowMore = () => {\n    setOverflowState(prev => ({\n      ...prev,\n      maxLines: prev.isOverflowing ? prev.currentLines : Math.min(prev.maxLines, 20),\n      isOverflowing: !prev.isOverflowing,\n    }));\n  };\n\n  const resetOverflow = () => {\n    setOverflowState({\n      isOverflowing: false,\n      maxLines: defaultMaxLines,\n      currentLines: 0,\n      showMoreAvailable: false,\n    });\n  };\n\n  // Overflow actions\n  const addOverflowingId = (id: string) => {\n    setOverflowingIds(prev => new Set([...prev, id]));\n  };\n\n  const removeOverflowingId = (id: string) => {\n    setOverflowingIds(prev => {\n      const newSet = new Set(prev);\n      newSet.delete(id);\n      return newSet;\n    });\n  };\n\n  const getOverflowingIds = () => {\n    return Array.from(overflowingIds);\n  };\n\n  const value: OverflowContextValue = {\n    overflow,\n    setOverflow,\n    setMaxLines,\n    setCurrentLines,\n    toggleShowMore,\n    resetOverflow,\n  };\n\n  const actionsValue: OverflowActionsValue = {\n    addOverflowingId,\n    removeOverflowingId,\n    getOverflowingIds,\n  };\n\n  return (\n    <OverflowContext.Provider value={value}>\n      <OverflowActionsContext.Provider value={actionsValue}>\n        {children}\n      </OverflowActionsContext.Provider>\n    </OverflowContext.Provider>\n  );\n};\n\nexport const useOverflow = (): OverflowContextValue => {\n  const context = useContext(OverflowContext);\n  if (!context) {\n    throw new Error('useOverflow must be used within an OverflowProvider');\n  }\n  return context;\n};\n\nexport const useOverflowActions = (): OverflowActionsValue => {\n  const context = useContext(OverflowActionsContext);\n  if (!context) {\n    throw new Error('useOverflowActions must be used within an OverflowProvider');\n  }\n  return context;\n};\n", "#!/usr/bin/env node\n\n/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { main } from './src/arien.js';\n\n// --- Global Entry Point ---\nmain().catch(error => {\n  console.error('An unexpected critical error occurred:');\n  if (error instanceof Error) {\n    console.error(error.stack);\n  } else {\n    console.error(String(error));\n  }\n  process.exit(1);\n});\n"], "mappings": ";;;;;;;;;AAMA,OAAOA,YAAW;AAClB,SAAS,cAAc;;;ACDvB,SAAgB,UAAU,mBAAmB;AAC7C,SAAS,KAAK,MAAM,UAAU,cAAc;AAUnC,cA4FG,YA5FH;AADF,IAAM,aAAa,CAAC,UAAoB;AAC7C,SAAO,oBAAC,OAAK,GAAG,OAAO;AACzB;AAEA,IAAM,MAAM,CAAC,EAAE,QAAAC,SAAQ,UAAU,kBAAkB,CAAC,EAAE,MAAgB;AAEpE,OAAKA;AACL,OAAK;AACL,OAAK;AAEL,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAS,EAAE;AACrC,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,KAAK;AAChD,QAAM,CAAC,UAAU,WAAW,IAAI,SAA4D,CAAC,CAAC;AAG9F,QAAM,EAAE,KAAK,IAAI,OAAO;AAGxB,QAAM,cAAc,YAAY,OAAO,cAAsB;AAC3D,QAAI,CAAC,UAAU,KAAK,EAAG;AAEvB,aAAS,EAAE;AACX,iBAAa,IAAI;AAEjB,QAAI;AAEF,YAAM,cAAc;AAAA,QAClB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,MAC1B;AACA,kBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAG1C,UAAI,UAAU,WAAW,OAAO,GAAG;AACjC,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;AAAA,QAChC;AACA,oBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAAA,MAC5C,WAAW,UAAU,WAAW,QAAQ,GAAG;AACzC,oBAAY,CAAC,CAAC;AAAA,MAChB,WAAW,UAAU,WAAW,OAAO,GAAG;AACxC,aAAK;AAAA,MACP,OAAO;AAEL,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM,SAAS,SAAS;AAAA,UACxB,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;AAAA,QAChC;AACA,oBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAAA,MAC5C;AAAA,IACF,SAAS,OAAO;AACd,YAAM,eAAe;AAAA,QACnB,MAAM;AAAA,QACN,MAAM,UAAU,KAAK;AAAA,QACrB,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;AAAA,MAChC;AACA,kBAAY,UAAQ,CAAC,GAAG,MAAM,YAAY,CAAC;AAAA,IAC7C,UAAE;AACA,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AAGT,WAAS,CAACC,QAAO,QAAQ;AACvB,QAAI,IAAI,QAAQ;AACd,kBAAYA,MAAK;AACjB,eAAS,EAAE;AAAA,IACb,WAAW,IAAI,QAAQA,WAAU,KAAK;AACpC,WAAK;AAAA,IACP,WAAW,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQA,OAAM,WAAW,GAAG;AACvD,eAAS,UAAQ,OAAOA,MAAK;AAAA,IAC/B,WAAW,IAAI,aAAa,IAAI,QAAQ;AACtC,eAAS,UAAQ,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,IACpC;AAAA,EACF,CAAC;AAED,SACE,qBAAC,OAAI,eAAc,UAAS,QAAO,QAEjC;AAAA,wBAAC,OAAI,aAAY,UAAS,UAAU,GAClC,8BAAC,QAAK,OAAM,QAAO,MAAI,MAAC,2DAExB,GACF;AAAA,IAGA,qBAAC,OAAI,eAAc,UAAS,UAAU,GAAG,UAAU,GAChD;AAAA,eAAS,IAAI,CAAC,YACb,oBAAC,OAAqB,SAAS,GAC7B,+BAAC,QAAK,OACJ,QAAQ,SAAS,SAAS,UAC1B,QAAQ,SAAS,UAAU,QAC3B,QAAQ,SAAS,WAAW,WAC5B,SAEC;AAAA,gBAAQ,SAAS,SAAS,OAAO;AAAA,QACjC,QAAQ;AAAA,SACX,KATQ,QAAQ,EAUlB,CACD;AAAA,MAEA,aACC,oBAAC,OACC,8BAAC,QAAK,OAAM,QAAO,2BAAa,GAClC;AAAA,OAEJ;AAAA,IAGA,qBAAC,OAAI,aAAY,UAAS,UAAU,GAClC;AAAA,0BAAC,QAAK,OAAM,QAAQ,gBAAK;AAAA,MACzB,oBAAC,QAAM,iBAAM;AAAA,MACb,oBAAC,QAAK,OAAM,QAAO,oBAAC;AAAA,OACtB;AAAA,IAGA,oBAAC,OAAI,UAAU,GACb,8BAAC,QAAK,OAAM,QAAO,UAAQ,MAAC,oEAE5B,GACF;AAAA,KACF;AAEJ;;;ACzIA,OAAO,WAAW;AAClB,SAAS,eAAe;AACxB,OAAOC,cAAa;AACpB;AAAA,EACE;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB;AAAA,EACA;AAAA,EAEA;AAAA,OAIK;;;ACdP,YAAY,QAAQ;AACpB,YAAY,UAAU;AACtB,SAAS,qBAAqB;AAgB9B,IAAM,aAAa,cAAc,YAAY,GAAG;AAChD,IAAMC,aAAiB,aAAQ,UAAU;AAEzC,IAAI;AAEG,SAAS,iBAA0C;AACxD,MAAI,aAAa;AACf,WAAO;AAAA,EACT;AAGA,MAAI,aAAaA;AACjB,SAAO,eAAoB,aAAQ,UAAU,GAAG;AAC9C,UAAM,cAAmB,UAAK,YAAY,cAAc;AACxD,QAAO,cAAW,WAAW,GAAG;AAC9B,UAAI;AACF,cAAM,UAAa,gBAAa,aAAa,MAAM;AACnD,sBAAc,KAAK,MAAM,OAAO;AAChC,eAAO;AAAA,MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,iCAAiC,WAAW,KAAK,KAAK;AACpE;AAAA,MACF;AAAA,IACF;AACA,iBAAkB,aAAQ,UAAU;AAAA,EACtC;AAEA,SAAO;AACT;;;AC5CO,SAAS,gBAAwB;AACtC,MAAI;AACF,UAAM,UAAU,eAAe;AAC/B,WAAO,QAAQ,IAAI,eAAe,SAAS,WAAW;AAAA,EACxD,QAAQ;AACN,WAAO;AAAA,EACT;AACF;;;AFUA,YAAY,YAAY;AACxB,YAAYC,SAAQ;AACpB,YAAYC,WAAU;AACtB,YAAYC,SAAQ;;;AGrBpB,OAAO,mBAAmB;AAC1B,YAAY,QAAQ;AAGpB,IAAM,yBAAkE;AAAA,EACtE;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,iBAAiB,OAAkD;AAC1E,SAAQ,uBAA6C,SAAS,KAAK;AACrE;AAEA,SAAS,kBACP,SAC+B;AAE/B,MAAI,QAAQ,IAAI,SAAS;AACvB,WAAO;AAAA,EACT;AAGA,QAAM,+BACJ,QAAQ,IAAI,eAAe,YAAY,EAAE,KAAK,KAAK;AACrD,YACE,8BAA8B,SAAS,IACnC,+BACA;AACN,MAAI,YAAY,OAAO,YAAY,OAAQ,WAAU;AAAA,WAC5C,YAAY,OAAO,YAAY,WAAW,CAAC,QAAS,WAAU;AAEvE,MAAI,YAAY,OAAO;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY,YAAY,SAAS;AAC1C,QAAI,CAAC,iBAAiB,OAAO,GAAG;AAC9B,cAAQ;AAAA,QACN,mCAAmC,OAAO,qBAAqB,uBAAuB;AAAA,UACpF;AAAA,QACF,CAAC;AAAA,MACH;AACA,cAAQ,KAAK,CAAC;AAAA,IAChB;AAEA,QAAI,cAAc,KAAK,OAAO,GAAG;AAC/B,aAAO;AAAA,IACT;AACA,YAAQ;AAAA,MACN,mCAAmC,OAAO;AAAA,IAC5C;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAIA,MAAO,YAAS,MAAM,YAAY,cAAc,KAAK,cAAc,GAAG;AACpE,WAAO;AAAA,EACT,WAAW,cAAc,KAAK,QAAQ,KAAK,YAAY,MAAM;AAC3D,WAAO;AAAA,EACT,WAAW,cAAc,KAAK,QAAQ,KAAK,YAAY,MAAM;AAC3D,WAAO;AAAA,EACT;AAGA,MAAI,YAAY,MAAM;AACpB,YAAQ;AAAA,MACN;AAAA,IAEF;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,SAAO;AACT;AAEA,eAAsB,kBACpB,SACA,cACoC;AACpC,QAAM,UAAU,kBAAkB,OAAO;AAEzC,QAAMC,eAAc,MAAM,eAAe;AACzC,QAAM,QACJ,gBACA,QAAQ,IAAI,uBACZA,cAAa,QAAQ;AAEvB,SAAO,WAAW,QAAQ,EAAE,SAAS,MAAM,IAAI;AACjD;;;AHjEA,IAAM,SAAS;AAAA;AAAA,EAEb,OAAO,IAAI,SAAgB,QAAQ,MAAM,WAAW,GAAG,IAAI;AAAA;AAAA,EAE3D,MAAM,IAAI,SAAgB,QAAQ,KAAK,UAAU,GAAG,IAAI;AAAA;AAAA,EAExD,OAAO,IAAI,SAAgB,QAAQ,MAAM,WAAW,GAAG,IAAI;AAC7D;AAkBA,eAAe,iBAAmC;AAChD,QAAM,OAAO,MAAM,MAAM,QAAQC,SAAQ,IAAI,CAAC,EAC3C,OAAO,SAAS;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAASA,SAAQ,IAAI,gBAAgB;AAAA,EACvC,CAAC,EACA,OAAO,UAAU;AAAA,IAChB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,WAAW;AAAA,IACjB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,iBAAiB;AAAA,IACvB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,SAAS;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,aAAa;AAAA,IACnB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,qBAAqB;AAAA,IAC3B,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,QAAQ;AAAA,IACd,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,aAAa;AAAA,IACnB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,iBAAiB;AAAA,IACvB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,mBAAmB;AAAA,IACzB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,yBAAyB;AAAA,IAC/B,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,uBAAuB;AAAA,IAC7B,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,KAAK,EACL,MAAM,QAAQ,GAAG,EACjB,QAAQ,cAAc,CAAC,EACvB,MAAM,WAAW,GAAG,EACpB,MAAM;AAET,SAAO;AACT;AAEA,eAAsB,cACpB,UACA,YACAC,YACiB;AACjB,QAAM,OAAO,MAAM,eAAe;AAGlC,eAAa;AAGb,QAAMC,UAAS,IAAI,OAAO;AAAA,IACxB,eAAeF,SAAQ,IAAI;AAAA,IAC3B,WAAAC;AAAA,EACF,CAAC;AAGD,MAAI,KAAK,OAAO;AACd,IAAAC,QAAO,SAAS,KAAK,KAAK;AAAA,EAC5B;AAEA,MAAI,KAAK,UAAU,QAAW;AAC5B,IAAAA,QAAO,aAAa,KAAK,KAAK;AAAA,EAChC;AAEA,MAAI,KAAK,QAAQ;AACf,IAAAA,QAAO,YAAY,KAAK,MAAM;AAAA,EAChC;AAEA,MAAI,KAAK,cAAc,QAAW;AAChC,IAAAA,QAAO,mBAAmB,KAAK,SAAS;AAAA,EAC1C;AAEA,MAAI,KAAK,sBAAsB,QAAW;AACxC,IAAAA,QAAO,mBAAmB,KAAK,iBAAiB;AAAA,EAClD;AAEA,MAAI,KAAK,SAAS,QAAW;AAC3B,IAAAA,QAAO,gBAAgB,KAAK,OAAO,aAAa,OAAO,aAAa,MAAM;AAAA,EAC5E;AAEA,MAAI,KAAK,cAAc,QAAW;AAChC,IAAAA,QAAO,oBAAoB,KAAK,SAAS;AAAA,EAC3C;AAEA,MAAI,KAAK,kBAAkB,QAAW;AACpC,IAAAA,QAAO,wBAAwB,KAAK,aAAa;AAAA,EACnD;AAEA,MAAI,KAAK,iBAAiB;AACxB,IAAAA,QAAO,mBAAmB,KAAK,eAAkC;AAAA,EACnE;AAEA,MAAI,KAAK,uBAAuB;AAC9B,IAAAA,QAAO,yBAAyB,KAAK,qBAAqB;AAAA,EAC5D;AAEA,MAAI,KAAK,wBAAwB,QAAW;AAC1C,IAAAA,QAAO,uBAAuB,KAAK,mBAAmB;AAAA,EACxD;AAGA,gBAAcA,SAAQ,QAAQ;AAG9B,kBAAgBA,SAAQ,UAAU;AAGlC,MAAI,KAAK,YAAY,UAAa,KAAK,eAAe,GAAG;AACvD,UAAM,gBAAgB,MAAM;AAAA,MAC1B,KAAK;AAAA,MACL,KAAK,eAAe;AAAA,IACtB;AACA,QAAI,eAAe;AACjB,MAAAA,QAAO,WAAW,aAAa;AAAA,IACjC;AAAA,EACF;AAGA,MAAI;AACF,UAAM,SAAS,MAAM;AAAA,MACnBA,QAAO,iBAAiB;AAAA,MACxB,2BAA2B;AAAA,IAC7B;AACA,IAAAA,QAAO,UAAU,MAAM;AAAA,EACzB,SAAS,OAAO;AACd,WAAO,KAAK,uCAAuC,KAAK;AAAA,EAC1D;AAEA,SAAOA;AACT;AAEA,SAAS,eAAe;AACtB,QAAM,WAAW,CAAC,QAAQ,YAAY;AAEtC,aAAW,WAAW,UAAU;AAC9B,UAAM,UAAe,WAAKF,SAAQ,IAAI,GAAG,OAAO;AAChD,QAAO,eAAW,OAAO,GAAG;AAC1B,MAAO,cAAO,EAAE,MAAM,QAAQ,CAAC;AAC/B,aAAO,MAAM,qCAAqC,OAAO,EAAE;AAAA,IAC7D;AAAA,EACF;AAGA,QAAM,cAAmB,WAAQ,YAAQ,GAAG,UAAU,MAAM;AAC5D,MAAO,eAAW,WAAW,GAAG;AAC9B,IAAO,cAAO,EAAE,MAAM,YAAY,CAAC;AACnC,WAAO,MAAM,iDAAiD;AAAA,EAChE;AACF;AAEA,SAAS,cAAcE,SAAgB,UAAoB;AACzD,MAAI,SAAS,OAAO;AAClB,IAAAA,QAAO,SAAS,SAAS,KAAK;AAAA,EAChC;AAEA,MAAI,SAAS,gBAAgB;AAC3B,IAAAA,QAAO,kBAAkB,SAAS,cAAc;AAAA,EAClD;AAEA,MAAI,SAAS,cAAc,QAAW;AACpC,IAAAA,QAAO,aAAa,SAAS,SAAS;AAAA,EACxC;AAEA,MAAI,SAAS,oBAAoB,QAAW;AAC1C,IAAAA,QAAO,mBAAmB,SAAS,eAAe;AAAA,EACpD;AAEA,MAAI,SAAS,oBAAoB,QAAW;AAC1C,IAAAA,QAAO,mBAAmB,SAAS,eAAe;AAAA,EACpD;AAEA,MAAI,SAAS,cAAc;AACzB,IAAAA,QAAO,gBAAgB,SAAS,YAAY;AAAA,EAC9C;AAEA,MAAI,SAAS,qBAAqB,QAAW;AAC3C,IAAAA,QAAO,oBAAoB,SAAS,gBAAgB;AAAA,EACtD;AAEA,MAAI,SAAS,yBAAyB,QAAW;AAC/C,IAAAA,QAAO,wBAAwB,SAAS,oBAAoB;AAAA,EAC9D;AAEA,MAAI,SAAS,iBAAiB;AAC5B,IAAAA,QAAO,mBAAmB,SAAS,eAAe;AAAA,EACpD;AAEA,MAAI,SAAS,uBAAuB;AAClC,IAAAA,QAAO,yBAAyB,SAAS,qBAAqB;AAAA,EAChE;AAEA,MAAI,SAAS,wBAAwB,QAAW;AAC9C,IAAAA,QAAO,uBAAuB,SAAS,mBAAmB;AAAA,EAC5D;AAEA,MAAI,SAAS,cAAc;AACzB,IAAAA,QAAO,gBAAgB,SAAS,YAAY;AAAA,EAC9C;AAEA,MAAI,SAAS,cAAc;AACzB,IAAAA,QAAO,gBAAgB,SAAS,YAAY;AAAA,EAC9C;AAEA,MAAI,SAAS,kBAAkB;AAC7B,8BAA0B,SAAS,gBAAgB;AAAA,EACrD;AACF;AAEA,SAAS,gBAAgBA,SAAgB,YAAyB;AAChE,aAAW,aAAa,YAAY;AAClC,QAAI;AACF,gBAAU,YAAYA,OAAM;AAC5B,aAAO,MAAM,sBAAsB,UAAU,IAAI,EAAE;AAAA,IACrD,SAAS,OAAO;AACd,aAAO,MAAM,6BAA6B,UAAU,IAAI,KAAK,KAAK;AAAA,IACpE;AAAA,EACF;AACF;;;AI/SA,eAAsB,YAA6B;AACjD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,QAAI,OAAO;AACX,YAAQ,MAAM,YAAY,MAAM;AAEhC,UAAM,aAAa,MAAM;AACvB,UAAI;AACJ,cAAQ,QAAQ,QAAQ,MAAM,KAAK,OAAO,MAAM;AAC9C,gBAAQ;AAAA,MACV;AAAA,IACF;AAEA,UAAM,QAAQ,MAAM;AAClB,cAAQ;AACR,cAAQ,IAAI;AAAA,IACd;AAEA,UAAM,UAAU,CAAC,QAAe;AAC9B,cAAQ;AACR,aAAO,GAAG;AAAA,IACZ;AAEA,UAAM,UAAU,MAAM;AACpB,cAAQ,MAAM,eAAe,YAAY,UAAU;AACnD,cAAQ,MAAM,eAAe,OAAO,KAAK;AACzC,cAAQ,MAAM,eAAe,SAAS,OAAO;AAAA,IAC/C;AAEA,YAAQ,MAAM,GAAG,YAAY,UAAU;AACvC,YAAQ,MAAM,GAAG,OAAO,KAAK;AAC7B,YAAQ,MAAM,GAAG,SAAS,OAAO;AAAA,EACnC,CAAC;AACH;;;AN3BA,SAAS,gBAAgB;AACzB,OAAO,QAAQ;AACf,OAAOC,SAAQ;AACf,SAAS,SAAAC,cAAa;;;AORtB,SAAS,MAAM,UAAU,aAAgC;AACzD,OAAOC,SAAQ;AACf,OAAOC,WAAU;AACjB,OAAOC,SAAQ;AACf,SAAS,gBAAgB;;;ACJzB,YAAYC,SAAQ;AACpB,YAAYC,WAAU;AACtB,SAAS,WAAAC,gBAAe;AACxB;AAAA,EAEE;AAAA,EAGA;AAAA,EACA,gBAAAC;AAAA,EACA,mBAAAC;AAAA,OACK;AACP,OAAO,uBAAuB;;;ACQvB,IAAM,aAA0B;AAAA,EACrC,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AAAA,EACN,gBAAgB,CAAC,WAAW,WAAW,SAAS;AAClD;AAEO,IAAM,YAAyB;AAAA,EACpC,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AAAA,EACN,gBAAgB,CAAC,WAAW,WAAW,SAAS;AAClD;AAEO,IAAM,YAAyB;AAAA,EACpC,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AACR;AAEO,IAAM,QAAN,MAAM,OAAM;AAAA,EA0BjB,YACW,MACA,MACT,aACS,QACT;AAJS;AACA;AAEA;AAET,SAAK,YAAY,OAAO,OAAO,KAAK,eAAe,WAAW,CAAC;AAG/D,UAAM,kBAAkB,YAAY,MAAM,GAAG;AAC7C,SAAK,gBACF,kBAAkB,OAAM,cAAc,eAAe,IAAI,WAC1D,OAAO;AAAA,EACX;AAAA,EAtCS;AAAA,EACU;AAAA;AAAA,EAGnB,OAAwB,oBAAoB,oBAAI,IAAI;AAAA,IAClD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAAA,EAiBD,YAAY,WAAuC;AACjD,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAAA,EAEA,OAAe,cAAc,YAAwC;AACnE,UAAM,aAAa,WAAW,YAAY;AAG1C,QAAI,WAAW,WAAW,GAAG,GAAG;AAC9B,aAAO;AAAA,IACT,WAES,OAAM,kBAAkB,IAAI,UAAU,GAAG;AAChD,aAAO;AAAA,IACT;AAGA,YAAQ;AAAA,MACN,oCAAoC,UAAU;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AAAA,EAEU,eACR,WACwB;AACxB,UAAM,WAAmC,CAAC;AAC1C,eAAW,OAAO,WAAW;AAE3B,UAAI,CAAC,IAAI,WAAW,OAAO,KAAK,QAAQ,QAAQ;AAC9C;AAAA,MACF;AAEA,YAAM,QAAQ,UAAU,GAAG;AAC3B,UAAI,OAAO,OAAO;AAChB,cAAM,gBAAgB,OAAM,cAAc,MAAM,KAAK;AACrD,YAAI,kBAAkB,QAAW;AAC/B,mBAAS,GAAG,IAAI;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;ACpJA,IAAM,qBAAqB;AAAA,EACzB,QAAQ;AAAA,IACN,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,YAAY;AAAA,IACV,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,WAAW;AAAA,EACpB;AACF;AAEO,IAAM,eAAe,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACnIA,IAAM,oBAAoB;AAAA,EACxB,QAAQ;AAAA,IACN,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,IACV,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,aAAa;AAAA,IACX,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,IACb,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO,UAAU;AAAA,EACnB;AACF;AAEO,IAAM,cAAc,IAAI;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AHtHO,IAAM,0BAA0B;AAChC,IAAM,oBAAyB,WAAKC,SAAQ,GAAG,uBAAuB;AACtE,IAAM,qBAA0B,WAAK,mBAAmB,eAAe;AAsEvE,IAAM,iBAAN,MAAqB;AAAA,EAC1B,YACE,MACA,WACA,QACA;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,UAAU,KAAK,sBAAsB;AAAA,EAC5C;AAAA,EAES;AAAA,EACA;AAAA,EACA;AAAA,EAED;AAAA,EAER,IAAI,SAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ,wBAAkC;AAExC,WAAO;AAAA,MACL,GAAG,KAAK,KAAK;AAAA,MACb,GAAG,KAAK,UAAU;AAAA,IACpB;AAAA,EACF;AAAA,EAEA,SAAS,OAAqB,KAAqB,OAAY;AAC7D,UAAM,aAAa,UAAU,oBAAoB,KAAK,OAAO,KAAK;AAClE,eAAW,SAAS,GAAG,IAAI;AAG3B,QAAI;AACF,MAAG,cAAe,cAAQ,WAAW,IAAI,GAAG,EAAE,WAAW,KAAK,CAAC;AAC/D,MAAG;AAAA,QACD,WAAW;AAAA,QACX,KAAK,UAAU,WAAW,UAAU,MAAM,CAAC;AAAA,MAC7C;AAGA,WAAK,UAAU,KAAK,sBAAsB;AAAA,IAC5C,SAAS,OAAO;AACd,cAAQ,MAAM,8BAA8B,WAAW,IAAI,KAAK,KAAK;AAAA,IACvE;AAAA,EACF;AAAA,EAEA,SAAS,KAA0B;AACjC,WAAO,KAAK,OAAO,GAAG;AAAA,EACxB;AACF;AAEO,SAAS,aAAa,eAAuC;AAClE,QAAM,SAA0B,CAAC;AAGjC,QAAM,eAAe,iBAAiB,oBAAoB,MAAM;AAGhE,QAAM,wBAA6B,WAAK,eAAe,yBAAyB,eAAe;AAC/F,QAAM,oBAAoB,iBAAiB,uBAAuB,MAAM;AAExE,SAAO,IAAI,eAAe,cAAc,mBAAmB,MAAM;AACnE;AAEA,SAAS,iBAAiB,UAAkB,QAAuC;AACjF,QAAM,kBAA4B;AAAA,IAChC,OAAO,YAAY;AAAA,IACnB,8BAA8B;AAAA,IAC9B,eAAe;AAAA,MACb,kBAAkB;AAAA,MAClB,2BAA2B;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,uBAAuB;AAAA,IACzB;AAAA,EACF;AAEA,MAAI,CAAI,eAAW,QAAQ,GAAG;AAC5B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI;AACF,UAAM,UAAa,iBAAa,UAAU,MAAM;AAChD,UAAM,eAAe,kBAAkB,OAAO;AAC9C,UAAM,SAAS,KAAK,MAAM,YAAY;AAGtC,UAAM,WAAqB;AAAA,MACzB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,WAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF,SAAS,OAAO;AACd,WAAO,KAAK;AAAA,MACV,SAAS,gBAAgB,KAAK;AAAA,MAC9B,MAAM;AAAA,IACR,CAAC;AAED,WAAO;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,IACR;AAAA,EACF;AACF;;;ADrMA,SAAS,iBAAiB;AAG1B,IAAM,YAAY,UAAU,IAAI;AAEhC,SAAS,iBAAiB,UAA0B;AAClD,MAAIC,IAAG,SAAS,MAAM,SAAS;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,qBAAqB,SAAS,QAAQ,OAAO,GAAG;AACtD,QAAM,QAAQ,mBAAmB,MAAM,kBAAkB;AACzD,MAAI,OAAO;AACT,WAAO,IAAI,MAAM,CAAC,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,EAC/C;AACA,SAAO;AACT;AAKA,IAAM,4BAA4B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAKA,eAAe,gCAAkD;AAC/D,QAAM,SAAS,QAAQ,IAAI,qBAAqB,YAAY,EAAE,KAAK;AAEnE,MAAI,WAAW,OAAO,WAAW,QAAQ;AACvC,WAAO;AAAA,EACT;AACA,MAAI,WAAW,OAAO,WAAW,SAAS;AACxC,WAAO;AAAA,EACT;AAGA,MAAIC,IAAG,SAAS,MAAM,SAAS;AAC7B,QAAI;AACF,YAAM,mBAAmB,MAAM,SAAS,mBAAmB,MAAM;AACjE,UACE,iBAAiB,SAAS,WAAW,KACrC,iBAAiB,SAAS,WAAW,KACrC,iBAAiB,MAAM,sBAAsB;AAAA,MAC7C,iBAAiB,MAAM,sBAAsB,GAC7C;AACA,gBAAQ;AAAA,UACN;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF,SAAS,MAAM;AACb,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,eAAsB,cACpB,eACA,aAAuB,CAAC,GACT;AACf,QAAM,EAAE,SAAS,MAAM,IAAI;AAE3B,MAAI,YAAY,gBAAgB;AAC9B,UAAM,qBAAqB,OAAO,UAAU;AAAA,EAC9C,WAAW,YAAY,YAAY,YAAY,UAAU;AACvD,UAAM,sBAAsB,SAAS,OAAO,UAAU;AAAA,EACxD,OAAO;AACL,UAAM,IAAI,MAAM,gCAAgC,OAAO,EAAE;AAAA,EAC3D;AACF;AAEA,eAAe,qBACb,SACA,YACe;AACf,QAAM,cAAc,uBAAuB,OAAO;AAClD,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,6BAA6B,OAAO,EAAE;AAAA,EACxD;AAEA,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,GAAG;AAAA,IACH,GAAG,QAAQ,KAAK,MAAM,CAAC;AAAA,EACzB;AAEA,QAAM,MAAM,EAAE,GAAG,QAAQ,KAAK,SAAS,OAAO;AAC9C,QAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG;AAAA,IAC1C,OAAO;AAAA,IACP;AAAA,EACF,CAAC;AAED,QAAM,IAAI,QAAc,CAAC,SAAS,WAAW;AAC3C,UAAM,GAAG,SAAS,CAAC,SAAS;AAC1B,UAAI,SAAS,GAAG;AACd,gBAAQ;AAAA,MACV,OAAO;AACL,eAAO,IAAI,MAAM,oCAAoC,IAAI,EAAE,CAAC;AAAA,MAC9D;AAAA,IACF,CAAC;AACD,UAAM,GAAG,SAAS,MAAM;AAAA,EAC1B,CAAC;AACH;AAEA,eAAe,sBACb,SACA,OACA,YACe;AACf,QAAM,gBAAgB,QAAQ,IAAI;AAClC,QAAM,yBAAyB,iBAAiB,aAAa;AAC7D,QAAM,2BAA2B,iBAAiB,iBAAiB;AAEnE,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IAAa;AAAA,IACb;AAAA,IAAM,GAAG,aAAa,IAAI,sBAAsB;AAAA,IAChD;AAAA,IAAM,GAAG,iBAAiB,IAAI,wBAAwB;AAAA,IACtD;AAAA,IAAM;AAAA,EACR;AAGA,MAAI,MAAM,8BAA8B,GAAG;AACzC,UAAM,MAAM,QAAQ,SAAS,KAAK;AAClC,UAAM,MAAM,QAAQ,SAAS,KAAK;AAClC,SAAK,KAAK,UAAU,GAAG,GAAG,IAAI,GAAG,EAAE;AAAA,EACrC;AAGA,QAAM,MAAM,EAAE,GAAG,QAAQ,KAAK,SAAS,OAAO;AAC9C,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC9C,QAAI,UAAU,QAAW;AACvB,WAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE;AAAA,IACnC;AAAA,EACF;AAEA,OAAK,KAAK,OAAO,QAAQ,GAAG,YAAY,GAAG,QAAQ,KAAK,MAAM,CAAC,CAAC;AAEhE,QAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG;AAAA,IAC1C,OAAO;AAAA,EACT,CAAC;AAED,QAAM,IAAI,QAAc,CAAC,SAAS,WAAW;AAC3C,UAAM,GAAG,SAAS,CAAC,SAAS;AAC1B,UAAI,SAAS,GAAG;AACd,gBAAQ;AAAA,MACV,OAAO;AACL,eAAO,IAAI,MAAM,sCAAsC,IAAI,EAAE,CAAC;AAAA,MAChE;AAAA,IACF,CAAC;AACD,UAAM,GAAG,SAAS,MAAM;AAAA,EAC1B,CAAC;AACH;AAEA,SAAS,uBAAuB,SAAgC;AAC9D,MAAI,0BAA0B,SAAS,OAAO,GAAG;AAC/C,WAAOC,MAAK,KAAK,qBAAW,iBAAiB,OAAO,KAAK;AAAA,EAC3D;AAGA,MAAIC,IAAG,WAAW,OAAO,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AK5LA,IAAM,aAAa;AAAA,EACjB,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AACF;AAEO,IAAM,OAAO,IAAI;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACnIA,IAAM,kBAAkB;AAAA,EACtB,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB;AAAA,EACrB,GAAG;AAAA,EACH,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AACd;AAEO,IAAM,YAAY,IAAI;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AC1IA,IAAM,eAA4B;AAAA,EAChC,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,MAAM;AACR;AAGA,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,IACf,OAAO;AAAA,EACT;AACF;AAEO,IAAM,eAAe,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AChJA,OAAOC,cAAa;AAOb,IAAM,gBAAuB;AAEpC,IAAM,eAAN,MAAmB;AAAA,EACA;AAAA,EACT;AAAA,EAER,cAAc;AACZ,SAAK,kBAAkB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqC;AACnC,UAAM,eAAe,CAAC,GAAG,KAAK,eAAe,EAAE,KAAK,CAAC,GAAG,MAAM;AAC5D,YAAM,YAAY,CAAC,SAA4B;AAC7C,gBAAQ,MAAM;AAAA,UACZ,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,YAAM,iBAAiB,UAAU,EAAE,IAAI,IAAI,UAAU,EAAE,IAAI;AAC3D,UAAI,mBAAmB,GAAG;AACxB,eAAO;AAAA,MACT;AACA,aAAO,EAAE,KAAK,cAAc,EAAE,IAAI;AAAA,IACpC,CAAC;AAED,WAAO,aAAa,IAAI,CAAC,WAAW;AAAA,MAClC,MAAM,MAAM;AAAA,MACZ,MAAM,MAAM;AAAA,IACd,EAAE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,WAAwC;AACrD,UAAM,aAAa,KAAK,gBAAgB,SAAS;AAEjD,QAAI,YAAY;AACd,WAAK,cAAc;AACnB,aAAO;AAAA,IACT,OAAO;AAEL,UAAI,cAAc,QAAW;AAC3B,aAAK,cAAc;AACnB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,qBAA6B;AAC3B,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOQ,gBAAgB,WAAkD;AACxE,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,gBAAgB;AAAA,MAC1B,CAAC,UAAU,MAAM,KAAK,YAAY,MAAM,UAAU,YAAY;AAAA,IAChE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAA4B;AAC1B,WAAOA,SAAQ,IAAI,aAAa,UAAaA,SAAQ,IAAI,aAAa;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA,EAKA,kBAA0B;AACxB,QAAI,KAAK,iBAAiB,GAAG;AAC3B,aAAO;AAAA,IACT;AAGA,UAAM,YAAYA,SAAQ,IAAI;AAC9B,UAAM,OAAOA,SAAQ,IAAI;AAEzB,QAAI,cAAc,eAAe,MAAM,SAAS,UAAU,GAAG;AAC3D,aAAO;AAAA,IACT,WAAW,MAAM,SAAS,OAAO,GAAG;AAClC,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAA0B;AACnC,QAAI,aAAa;AAEjB,QAAI,CAAC,YAAY;AACf,mBAAa,KAAK,gBAAgB;AAAA,IACpC;AAEA,QAAI,CAAC,KAAK,eAAe,UAAU,GAAG;AACpC,cAAQ,KAAK,UAAU,UAAU,mCAAmC;AACpE,WAAK,eAAe,MAAS;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAA0B;AACxC,WAAO,KAAK,gBAAgB,OAAO,CAAC,UAAU,MAAM,SAAS,IAAI;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKA,qBAA8B;AAC5B,UAAM,cAAc,KAAK,YAAY;AACrC,UAAM,aAAa,gBAAgB,SAAS,UAAU;AAEtD,UAAM,WAAW,KAAK,gBAAgB,UAAU;AAChD,QAAI,SAAS,SAAS,GAAG;AACvB,WAAK,cAAc,SAAS,CAAC;AAC7B,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;AAGO,IAAM,eAAe,IAAI,aAAa;;;AClL7C,OAAOC,SAAQ;AACf,OAAOC,SAAQ;AACf,SAAS,QAAQ,gBAAgB;AACjC,SAAS,mBAAAC,wBAAuB;AAEhC,IAAM,mBAAmB,SAASD,IAAG,OAAO,GAAG,wBAAwB;AAEvE,eAAsB,qBAAwC;AAC5D,MAAI;AACF,UAAMD,IAAG,OAAO,gBAAgB;AAChC,UAAM,kBAAkB,MAAMA,IAAG,SAAS,kBAAkB,OAAO;AACnE,UAAM,WAAW,gBACd,MAAM,IAAI,EACV,OAAO,CAAC,SAAS,KAAK,KAAK,MAAM,EAAE;AACtC,QAAI;AACF,YAAMA,IAAG,OAAO,gBAAgB;AAAA,IAClC,QAAQ;AACN,eAAS,KAAK,oDAAoD;AAAA,IACpE;AACA,WAAO;AAAA,EACT,SAAS,KAAc;AAOrB,QAAI,eAAe,SAAS,UAAU,OAAO,IAAI,SAAS,UAAU;AAClE,aAAO,CAAC;AAAA,IACV;AAEA,WAAO,CAAC,yCAAyCE,iBAAgB,GAAG,CAAC,EAAE;AAAA,EACzE;AACF;;;ACjCA;AAAA,EAGE;AAAA,EAEA;AAAA,EACA;AAAA,OACK;;;ACGA,SAAS,WAAW,OAA6B;AACtD,MAAI,iBAAiB,OAAO;AAC1B,WAAO;AAAA,MACL,SAAS,MAAM;AAAA,MACf,OAAO,MAAM;AAAA,MACb,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,SAAS,OAAO,IAAI,WAAW,IAAI,WAAW,KAAK,eAAe;AAAA,MAClE,OAAO,OAAO,IAAI,UAAU,WAAW,IAAI,QAAQ;AAAA,MACnD,MAAM,OAAO,IAAI,SAAS,WAAW,IAAI,OAAO;AAAA,MAChD,MAAM;AAAA,MACN,MAAM,OAAO,IAAI,SAAS,WAAW,IAAI,OAAO;AAAA,MAChD,MAAM,OAAO,IAAI,SAAS,WAAW,IAAI,OAAO;AAAA,MAChD,QAAQ,OAAO,IAAI,WAAW,WAAW,IAAI,SAAS;AAAA,IACxD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AACF;AAEO,SAAS,YAAY,OAA4B;AACtD,MAAI,YAAY,MAAM;AAEtB,MAAI,MAAM,MAAM;AACd,iBAAa,KAAK,MAAM,IAAI;AAC5B,QAAI,MAAM,MAAM;AACd,mBAAa,IAAI,MAAM,IAAI;AAC3B,UAAI,MAAM,QAAQ;AAChB,qBAAa,IAAI,MAAM,MAAM;AAAA,MAC/B;AAAA,IACF;AACA,iBAAa;AAAA,EACf;AAEA,MAAI,MAAM,MAAM;AACd,iBAAa,KAAK,MAAM,IAAI;AAAA,EAC9B;AAEA,SAAO;AACT;AAEO,SAASC,iBAAgB,OAAwB;AACtD,QAAM,SAAS,WAAW,KAAK;AAC/B,SAAO,YAAY,MAAM;AAC3B;AAKO,SAAS,uBAAuB,OAAwB;AAC7D,SAAOA,iBAAgB,KAAK;AAC9B;;;AD3DA,SAAS,gBAAgB,UAAkD;AACzE,MAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACzD,UAAM,YAAY,SAAS,WAAW,CAAC;AACvC,QACE,UAAU,WACV,UAAU,QAAQ,SAClB,UAAU,QAAQ,MAAM,SAAS,GACjC;AAEA,YAAM,cAAc,UAAU,QAAQ,MAAM,CAAC;AAC7C,UAAI,aAAa,SAAS;AACxB,eAAO;AAAA,MACT;AACA,aAAO,UAAU,QAAQ,MACtB,OAAO,CAAC,SAAS,KAAK,IAAI,EAC1B,IAAI,CAAC,SAAS,KAAK,IAAI,EACvB,KAAK,EAAE;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AAEA,eAAsB,kBACpBC,SACA,OACe;AAEf,UAAQ,OAAO,GAAG,SAAS,CAAC,QAA+B;AACzD,QAAI,IAAI,SAAS,SAAS;AAExB,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AAED,QAAM,eAAeA,QAAO,gBAAgB;AAC5C,QAAM,eAA6B,MAAMA,QAAO,gBAAgB;AAEhE,QAAM,OAAO,MAAM,aAAa,QAAQ;AACxC,QAAM,kBAAkB,IAAI,gBAAgB;AAC5C,MAAI,kBAA6B,CAAC,EAAE,MAAM,QAAQ,OAAO,CAAC,EAAE,MAAM,MAAM,CAAC,EAAE,CAAC;AAE5E,MAAI;AACF,WAAO,MAAM;AACX,YAAM,gBAAgC,CAAC;AAEvC,YAAM,iBAAiB,MAAM,KAAK,kBAAkB;AAAA,QAClD,SAAS,gBAAgB,CAAC,GAAG,SAAS,CAAC;AAAA;AAAA,QACvC,QAAQ;AAAA,UACN,aAAa,gBAAgB;AAAA,UAC7B,OAAO;AAAA,YACL,EAAE,sBAAsB,aAAa,wBAAwB,EAAE;AAAA,UACjE;AAAA,QACF;AAAA,MACF,CAAC;AAED,uBAAiB,QAAQ,gBAAgB;AACvC,YAAI,gBAAgB,OAAO,SAAS;AAClC,kBAAQ,MAAM,sBAAsB;AACpC;AAAA,QACF;AACA,cAAM,WAAW,gBAAgB,IAAI;AACrC,YAAI,UAAU;AACZ,kBAAQ,OAAO,MAAM,QAAQ;AAAA,QAC/B;AACA,YAAI,KAAK,eAAe;AACtB,wBAAc,KAAK,GAAG,KAAK,aAAa;AAAA,QAC1C;AAAA,MACF;AAEA,UAAI,cAAc,SAAS,GAAG;AAC5B,cAAM,oBAA4B,CAAC;AAEnC,mBAAW,MAAM,eAAe;AAC9B,gBAAM,SAAS,GAAG,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC;AAChD,gBAAM,cAAmC;AAAA,YACvC;AAAA,YACA,MAAM,GAAG;AAAA,YACT,MAAM,GAAG,QAAQ,CAAC;AAAA,UACpB;AAEA,cAAI;AACF,kBAAM,SAAS,MAAM;AAAA,cACnB;AAAA,cACA;AAAA,cACAA;AAAA,cACA,gBAAgB;AAAA,YAClB;AAEA,8BAAkB,KAAK;AAAA,cACrB,kBAAkB;AAAA,gBAChB,MAAM,GAAG;AAAA,gBACT,UAAU;AAAA,cACZ;AAAA,YACF,CAAC;AAAA,UACH,SAAS,OAAO;AACd,oBAAQ,MAAM,wBAAwB,GAAG,IAAI,KAAK,KAAK;AACvD,8BAAkB,KAAK;AAAA,cACrB,kBAAkB;AAAA,gBAChB,MAAM,GAAG;AAAA,gBACT,UAAU;AAAA,kBACR,OAAO,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,gBAC9D;AAAA,cACF;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAGA,0BAAkB;AAAA,UAChB;AAAA,YACE,MAAM;AAAA,YACN,OAAO,cAAc,IAAI,CAAC,QAAQ;AAAA,cAChC,cAAc;AAAA,gBACZ,MAAM,GAAG;AAAA,gBACT,MAAM,GAAG,QAAQ,CAAC;AAAA,cACpB;AAAA,YACF,EAAE;AAAA,UACJ;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,OAAO;AAEL;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,UAAM,eAAe,uBAAuB,KAAK;AACjD,YAAQ,MAAM,YAAY,YAAY;AACtC,YAAQ,KAAK,CAAC;AAAA,EAChB,UAAE;AAEA,QAAI,0BAA0B,GAAG;AAC/B,YAAM,kBAAkB;AAAA,IAC1B;AAAA,EACF;AACF;;;AE1JA,YAAYC,SAAQ;AACpB,YAAYC,WAAU;AACtB,YAAYC,SAAQ;AAEb,IAAM,4BAAiC,WAAK,UAAU,YAAY;AAClE,IAAM,6BAA6B;AAmBnC,SAAS,eAAe,cAAmC;AAChE,QAAM,gBAAgB;AAAA,IACpB,GAAG,sBAAsB,YAAY;AAAA,IACrC,GAAG,sBAAyB,YAAQ,CAAC;AAAA,EACvC;AAEA,QAAM,mBAAgC,CAAC;AACvC,QAAM,YAAY,oBAAI,IAAY;AAClC,aAAW,aAAa,eAAe;AACrC,QAAI,CAAC,UAAU,IAAI,UAAU,OAAO,IAAI,GAAG;AACzC,cAAQ;AAAA,QACN,sBAAsB,UAAU,OAAO,IAAI,cAAc,UAAU,OAAO,OAAO;AAAA,MACnF;AACA,uBAAiB,KAAK,SAAS;AAC/B,gBAAU,IAAI,UAAU,OAAO,IAAI;AAAA,IACrC;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,sBAAsB,KAA0B;AACvD,QAAM,gBAAqB,WAAK,KAAK,yBAAyB;AAC9D,MAAI,CAAI,eAAW,aAAa,GAAG;AACjC,WAAO,CAAC;AAAA,EACV;AAEA,QAAM,aAA0B,CAAC;AACjC,aAAW,UAAa,gBAAY,aAAa,GAAG;AAClD,UAAM,eAAoB,WAAK,eAAe,MAAM;AAEpD,UAAM,YAAY,cAAc,YAAY;AAC5C,QAAI,aAAa,MAAM;AACrB,iBAAW,KAAK,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,cAAwC;AAC7D,MAAI,CAAI,aAAS,YAAY,EAAE,YAAY,GAAG;AAC5C,YAAQ;AAAA,MACN,4BAA4B,YAAY;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAEA,QAAM,iBAAsB,WAAK,cAAc,0BAA0B;AACzE,MAAI,CAAI,eAAW,cAAc,GAAG;AAClC,YAAQ;AAAA,MACN,gCAAgC,YAAY,mCAAmC,cAAc;AAAA,IAC/F;AACA,WAAO;AAAA,EACT;AAEA,MAAI;AACF,UAAM,gBAAmB,iBAAa,gBAAgB,OAAO;AAC7D,UAAMC,UAAS,KAAK,MAAM,aAAa;AACvC,QAAI,CAACA,QAAO,QAAQ,CAACA,QAAO,SAAS;AACnC,cAAQ;AAAA,QACN,+BAA+B,cAAc;AAAA,MAC/C;AACA,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,oBAAoBA,OAAM,EAC5C,IAAI,CAAC,oBAAyB,WAAK,cAAc,eAAe,CAAC,EACjE,OAAO,CAAC,oBAAuB,eAAW,eAAe,CAAC;AAG7D,QAAI;AACJ,UAAM,iBAAsB,WAAK,cAAc,UAAU;AACzD,QAAO,eAAW,cAAc,GAAG;AACjC,UAAI;AACF,cAAM,kBAAkB,UAAQ,cAAc;AAC9C,YAAI,OAAO,gBAAgB,cAAc,YAAY;AACnD,sBAAY,gBAAgB;AAAA,QAC9B;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ;AAAA,UACN,4CAA4C,cAAc;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAMA,QAAO;AAAA,MACb,SAASA,QAAO;AAAA,MAChB,QAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,uCAAuC,cAAc,KAAK,KAAK;AAC7E,WAAO;AAAA,EACT;AACF;AAEA,SAAS,oBAAoBA,SAAmC;AAC9D,MAAI,CAACA,QAAO,iBAAiB;AAC3B,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,MAAM,QAAQA,QAAO,eAAe,GAAG;AACzC,WAAOA,QAAO;AAAA,EAChB;AAEA,SAAO,CAACA,QAAO,eAAe;AAChC;;;ACtIA,SAAS,YAAYC,WAAU;AAC/B,SAAS,QAAAC,aAAY;AACrB,SAAS,yBAAyB;AAElC,eAAsB,qBAAqB;AACzC,QAAM,UAAU,kBAAkB,QAAQ,IAAI,CAAC;AAC/C,QAAM,iBAAiBA,MAAK,SAAS,aAAa;AAClD,MAAI;AACF,UAAMD,IAAG,GAAG,gBAAgB,EAAE,WAAW,MAAM,OAAO,KAAK,CAAC;AAAA,EAC9D,QAAQ;AAAA,EAER;AACF;;;ApBSA;AAAA,EACE,gBAAAE;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC;AAAA,OACK;;;AqB9BP,SAAS,YAAAC,iBAAgB;AAElB,IAAM,qBAAqB,CAAC,eAAsC;AACvE,MAAI,eAAeA,UAAS,mBAAmB;AAC7C,WAAO;AAAA,EACT;AAEA,MAAI,eAAeA,UAAS,YAAY;AACtC,QAAI,CAAC,QAAQ,IAAI,gBAAgB;AAC/B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,eAAeA,UAAS,eAAe;AACzC,UAAM,iCACJ,CAAC,CAAC,QAAQ,IAAI,wBAAwB,CAAC,CAAC,QAAQ,IAAI;AACtD,UAAM,kBAAkB,CAAC,CAAC,QAAQ,IAAI;AACtC,QAAI,CAAC,kCAAkC,CAAC,iBAAiB;AACvD,aACE;AAAA,IAKJ;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC9BA,OAAOC,UAAS,UAAU,WAAW,aAAa;AAClD,SAAS,OAAAC,MAAK,QAAAC,aAAY;;;ACD1B,SAAgB,eAAe,YAAY,YAAAC,iBAA2B;AAqHhE,gBAAAC,YAAA;AA7FN,IAAM,kBAAkB,cAAgD,MAAS;AACjF,IAAM,yBAAyB,cAAgD,MAAS;;;ADmK5E,gBAAAC,MAmBF,QAAAC,aAnBE;AArLZ,IAAI,iBAAiB;AASd,SAAS,wBAAwB,OAAgB;AACtD,mBAAiB;AACnB;;;AtBsJQ,gBAAAC,YAAA;AAtIR,SAAS,kBAAkBC,SAA0B;AACnD,QAAM,gBAAgBC,IAAG,SAAS,KAAK,OAAO;AAC9C,QAAM,YAAY,GAAG,kBAAkB;AACvC,QAAM,2BAA2B,KAAK;AAAA,IACpC,UAAU,kBAAkB,OAAO;AAAA,EACrC;AAGA,QAAM,4BAA4B,KAAK,MAAM,gBAAgB,GAAG;AAChE,MAAID,QAAO,aAAa,GAAG;AACzB,YAAQ;AAAA,MACN,qBAAqB,yBAAyB,QAAQ,CAAC,CAAC;AAAA,IAC1D;AAAA,EACF;AAEA,MAAI,QAAQ,IAAI,uBAAuB;AACrC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,4BAA4B,0BAA0B;AACxD,QAAIA,QAAO,aAAa,GAAG;AACzB,cAAQ;AAAA,QACN,sCAAsC,0BAA0B,QAAQ,CAAC,CAAC;AAAA,MAC5E;AAAA,IACF;AACA,WAAO,CAAC,wBAAwB,yBAAyB,EAAE;AAAA,EAC7D;AAEA,SAAO,CAAC;AACV;AAEA,eAAe,2BAA2B,gBAA0B;AAClE,QAAM,WAAW,CAAC,GAAG,gBAAgB,GAAG,QAAQ,KAAK,MAAM,CAAC,CAAC;AAC7D,QAAM,SAAS,EAAE,GAAG,QAAQ,KAAK,uBAAuB,OAAO;AAE/D,QAAM,QAAQE,OAAM,QAAQ,UAAU,UAAU;AAAA,IAC9C,OAAO;AAAA,IACP,KAAK;AAAA,EACP,CAAC;AAED,QAAM,IAAI,QAAQ,CAAC,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC;AACzD,UAAQ,KAAK,CAAC;AAChB;AAEA,eAAsB,OAAO;AAC3B,QAAM,gBAAgB,QAAQ,IAAI;AAClC,QAAM,WAAW,aAAa,aAAa;AAE3C,QAAM,mBAAmB;AACzB,MAAI,SAAS,OAAO,SAAS,GAAG;AAC9B,eAAW,SAAS,SAAS,QAAQ;AACnC,UAAI,eAAe,YAAY,MAAM,IAAI,KAAK,MAAM,OAAO;AAC3D,UAAI,CAAC,QAAQ,IAAI,UAAU;AACzB,uBAAe,WAAW,YAAY;AAAA,MACxC;AACA,cAAQ,MAAM,YAAY;AAC1B,cAAQ,MAAM,cAAc,MAAM,IAAI,iBAAiB;AAAA,IACzD;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,QAAM,aAAa,eAAe,aAAa;AAC/C,QAAMF,UAAS,MAAM,cAAc,SAAS,QAAQ,YAAY,SAAS;AAIzE,MAAI,CAAC,SAAS,OAAO,oBAAoB,QAAQ,IAAI,gBAAgB;AACnE,aAAS;AAAA;AAAA,MAEP;AAAA,MACAG,UAAS;AAAA,IACX;AAAA,EACF;AAEA,0BAAwBH,QAAO,aAAa,CAAC;AAG7C,EAAAA,QAAO,eAAe;AACtB,MAAIA,QAAO,wBAAwB,GAAG;AACpC,QAAI;AACF,YAAMA,QAAO,cAAc;AAAA,IAC7B,QAAQ;AAAA,IAER;AAAA,EACF;AAEA,MAAI,SAAS,OAAO,OAAO;AACzB,QAAI,CAAC,aAAa,eAAe,SAAS,OAAO,KAAK,GAAG;AAGvD,cAAQ,KAAK,mBAAmB,SAAS,OAAO,KAAK,cAAc;AAAA,IACrE;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,OAAO,+BAC/B,kBAAkBA,OAAM,IACxB,CAAC;AAGL,MAAI,CAAC,QAAQ,IAAI,SAAS;AACxB,UAAM,gBAAgBA,QAAO,WAAW;AACxC,QAAI,eAAe;AACjB,UAAI,SAAS,OAAO,kBAAkB;AAEpC,YAAI;AACF,gBAAM,MAAM,mBAAmB,SAAS,OAAO,gBAAgB;AAC/D,cAAI,KAAK;AACP,kBAAM,IAAI,MAAM,GAAG;AAAA,UACrB;AACA,gBAAMA,QAAO,YAAY,SAAS,OAAO,gBAAgB;AAAA,QAC3D,SAAS,KAAK;AACZ,kBAAQ,MAAM,yBAAyB,GAAG;AAC1C,kBAAQ,KAAK,CAAC;AAAA,QAChB;AAAA,MACF;AACA,YAAM,cAAc,eAAe,UAAU;AAC7C,cAAQ,KAAK,CAAC;AAAA,IAChB,OAAO;AAGL,UAAI,WAAW,SAAS,GAAG;AACzB,cAAM,2BAA2B,UAAU;AAC3C,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQA,QAAO,YAAY;AAC/B,QAAM,kBAAkB,MAAM,mBAAmB;AAGjD,MAAI,QAAQ,MAAM,SAAS,OAAO,WAAW,GAAG;AAC9C,mBAAe,SAAS,aAAa,GAAG,QAAQ;AAChD;AAAA,MACE,gBAAAD,KAACK,OAAM,YAAN,EACC,0BAAAL;AAAA,QAAC;AAAA;AAAA,UACC,QAAQC;AAAA,UACR;AAAA,UACA;AAAA;AAAA,MACF,GACF;AAAA,MACA,EAAE,aAAa,MAAM;AAAA,IACvB;AACA;AAAA,EACF;AAGA,MAAI,CAAC,QAAQ,MAAM,OAAO;AACxB,aAAS,MAAM,UAAU;AAAA,EAC3B;AACA,MAAI,CAAC,OAAO;AACV,YAAQ,MAAM,8BAA8B;AAC5C,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,gBAAcA,SAAQ;AAAA,IACpB,cAAc;AAAA,IACd,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,IAC1C,QAAQ;AAAA,IACR,eAAe,MAAM;AAAA,EACvB,CAAC;AAGD,QAAM,uBAAuB,MAAM;AAAA,IACjCA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,kBAAkB,sBAAsB,KAAK;AACnD,UAAQ,KAAK,CAAC;AAChB;AAEA,SAAS,eAAe,OAAe,UAA0B;AAC/D,MAAI,CAAC,SAAS,OAAO,iBAAiB;AACpC,YAAQ,OAAO,MAAM,mBAAmB,KAAK,OAAO;AAEpD,YAAQ,GAAG,QAAQ,MAAM;AACvB,cAAQ,OAAO,MAAM,aAAa;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAGA,QAAQ,GAAG,sBAAsB,CAAC,QAAQ,aAAa;AAErD,UAAQ,MAAM,2CAA2C;AACzD,UAAQ,MAAM,wCAAwC;AACtD,UAAQ,MAAM,2CAA2C;AACzD,UAAQ,MAAM,WAAW,MAAM;AAC/B,UAAQ,MAAM,yBAAyB;AACvC,MAAI,EAAE,kBAAkB,QAAQ;AAC9B,YAAQ,MAAM,MAAM;AAAA,EACtB;AAEA,UAAQ,KAAK,CAAC;AAChB,CAAC;AAED,eAAe,yBACbA,SACA,YACA,UACA;AACA,MAAI,cAAcA;AAClB,MAAIA,QAAO,gBAAgB,MAAMK,cAAa,MAAM;AAElD,UAAM,uBAAuB,SAAS,OAAO,gBAAgB,CAAC;AAC9D,UAAM,mBAAmB;AAAA,MACvB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AAEA,UAAM,kBAAkB;AAAA,MACtB,GAAG,oBAAI,IAAI,CAAC,GAAG,sBAAsB,GAAG,gBAAgB,CAAC;AAAA,IAC3D;AAEA,UAAM,yBAAyB;AAAA,MAC7B,GAAG,SAAS;AAAA,MACZ,cAAc;AAAA,IAChB;AACA,kBAAc,MAAM;AAAA,MAClB;AAAA,MACA;AAAA,MACAL,QAAO,aAAa;AAAA,IACtB;AAAA,EACF;AAEA,SAAO,MAAM;AAAA,IACX,SAAS,OAAO;AAAA,IAChB;AAAA,EACF;AACF;AAEA,eAAe,2BACb,kBACA,sBACA;AAIA,MAAI,CAAC,oBAAoB,CAAC,QAAQ,IAAI,gBAAgB;AACpD,YAAQ;AAAA,MACN,qCAAqC,kBAAkB;AAAA,IACzD;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,qBAAmB,oBAAoBG,UAAS;AAChD,QAAM,MAAM,mBAAmB,gBAAgB;AAC/C,MAAI,OAAO,MAAM;AACf,YAAQ,MAAM,GAAG;AACjB,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,QAAM,qBAAqB,YAAY,gBAAgB;AACvD,SAAO;AACT;;;AwB7RA,KAAK,EAAE,MAAM,WAAS;AACpB,UAAQ,MAAM,wCAAwC;AACtD,MAAI,iBAAiB,OAAO;AAC1B,YAAQ,MAAM,MAAM,KAAK;AAAA,EAC3B,OAAO;AACL,YAAQ,MAAM,OAAO,KAAK,CAAC;AAAA,EAC7B;AACA,UAAQ,KAAK,CAAC;AAChB,CAAC;", "names": ["React", "config", "input", "process", "__dirname", "fs", "path", "os", "packageJson", "process", "sessionId", "config", "os", "spawn", "os", "path", "fs", "fs", "path", "homedir", "ApprovalMode", "TelemetryTarget", "homedir", "os", "os", "path", "fs", "process", "fs", "os", "getErrorMessage", "getErrorMessage", "config", "fs", "path", "os", "config", "fs", "join", "ApprovalMode", "AuthType", "AuthType", "React", "Box", "Text", "useState", "jsx", "jsx", "jsxs", "jsx", "config", "os", "spawn", "AuthType", "React", "ApprovalMode"]}