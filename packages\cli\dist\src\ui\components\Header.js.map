{"version": 3, "sources": ["../../../../src/ui/components/Header.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React from 'react';\nimport { Box, Text } from 'ink';\nimport { Colors } from '../colors.js';\nimport { Config } from '@arien/arien-cli-core';\n\ninterface HeaderProps {\n  config: Config;\n  settings: Config; // Now using unified Config instead of LoadedSettings\n  onShowHelp?: () => void;\n  onShowTheme?: () => void;\n  onShowAuth?: () => void;\n}\n\nexport const Header: React.FC<HeaderProps> = ({\n  config,\n  settings,\n  onShowHelp,\n  onShowTheme,\n  onShowAuth,\n}) => {\n  const modelName = config.getModelName() || 'Unknown';\n  const authType = settings.getSelectedAuthType() || 'none';\n  const sandboxEnabled = Boolean(settings.getSandbox());\n\n  return (\n    <Box flexDirection=\"column\" marginBottom={1}>\n      {/* Title bar */}\n      <Box justifyContent=\"space-between\" alignItems=\"center\">\n        <Box alignItems=\"center\">\n          <Text color={Colors.AccentBlue} bold>\n            Arien CLI\n          </Text>\n          <Text color={Colors.Gray} dimColor>\n            {' '}v1.0.0\n          </Text>\n        </Box>\n        <Box alignItems=\"center\">\n          <Text color={Colors.Gray} dimColor>\n            Model: \n          </Text>\n          <Text color={Colors.AccentGreen}>\n            {' '}{modelName}\n          </Text>\n        </Box>\n      </Box>\n\n      {/* Status bar */}\n      <Box justifyContent=\"space-between\" alignItems=\"center\">\n        <Box alignItems=\"center\">\n          <Text color={Colors.Gray} dimColor>\n            Auth: \n          </Text>\n          <Text color={authType === 'none' ? Colors.AccentRed : Colors.AccentGreen}>\n            {' '}{authType}\n          </Text>\n          {sandboxEnabled && (\n            <>\n              <Text color={Colors.Gray} dimColor>\n                {' '}| Sandbox: \n              </Text>\n              <Text color={Colors.AccentYellow}>\n                {' '}enabled\n              </Text>\n            </>\n          )}\n        </Box>\n        <Box alignItems=\"center\">\n          <Text color={Colors.Gray} dimColor>\n            F1: Help | F2: Theme | F3: Auth\n          </Text>\n        </Box>\n      </Box>\n\n      {/* Separator */}\n      <Box>\n        <Text color={Colors.Gray}>\n          {'─'.repeat(80)}\n        </Text>\n      </Box>\n    </Box>\n  );\n};\n"], "mappings": "AAmCU,SA2BE,UA3BF,KAGA,YAHA;AAnCV;AAAA;AAAA;AAAA;AAAA;AAOA,SAAS,KAAK,YAAY;AAC1B,SAAS,cAAc;AAWhB,MAAM,SAAgC,CAAC;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,YAAY,OAAO,aAAa,KAAK;AAC3C,QAAM,WAAW,SAAS,oBAAoB,KAAK;AACnD,QAAM,iBAAiB,QAAQ,SAAS,WAAW,CAAC;AAEpD,SACE,qBAAC,OAAI,eAAc,UAAS,cAAc,GAExC;AAAA,yBAAC,OAAI,gBAAe,iBAAgB,YAAW,UAC7C;AAAA,2BAAC,OAAI,YAAW,UACd;AAAA,4BAAC,QAAK,OAAO,OAAO,YAAY,MAAI,MAAC,uBAErC;AAAA,QACA,qBAAC,QAAK,OAAO,OAAO,MAAM,UAAQ,MAC/B;AAAA;AAAA,UAAI;AAAA,WACP;AAAA,SACF;AAAA,MACA,qBAAC,OAAI,YAAW,UACd;AAAA,4BAAC,QAAK,OAAO,OAAO,MAAM,UAAQ,MAAC,oBAEnC;AAAA,QACA,qBAAC,QAAK,OAAO,OAAO,aACjB;AAAA;AAAA,UAAK;AAAA,WACR;AAAA,SACF;AAAA,OACF;AAAA,IAGA,qBAAC,OAAI,gBAAe,iBAAgB,YAAW,UAC7C;AAAA,2BAAC,OAAI,YAAW,UACd;AAAA,4BAAC,QAAK,OAAO,OAAO,MAAM,UAAQ,MAAC,mBAEnC;AAAA,QACA,qBAAC,QAAK,OAAO,aAAa,SAAS,OAAO,YAAY,OAAO,aAC1D;AAAA;AAAA,UAAK;AAAA,WACR;AAAA,QACC,kBACC,iCACE;AAAA,+BAAC,QAAK,OAAO,OAAO,MAAM,UAAQ,MAC/B;AAAA;AAAA,YAAI;AAAA,aACP;AAAA,UACA,qBAAC,QAAK,OAAO,OAAO,cACjB;AAAA;AAAA,YAAI;AAAA,aACP;AAAA,WACF;AAAA,SAEJ;AAAA,MACA,oBAAC,OAAI,YAAW,UACd,8BAAC,QAAK,OAAO,OAAO,MAAM,UAAQ,MAAC,6CAEnC,GACF;AAAA,OACF;AAAA,IAGA,oBAAC,OACC,8BAAC,QAAK,OAAO,OAAO,MACjB,mBAAI,OAAO,EAAE,GAChB,GACF;AAAA,KACF;AAEJ;", "names": []}