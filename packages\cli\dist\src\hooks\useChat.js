/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useCallback, useRef, useEffect } from "react";
import {
  Turn,
  createContentGenerator,
  createContentGeneratorConfig,
  AuthType
} from "@arien/arien-cli-core";
const useChat = (config) => {
  const [messages, setMessages] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState();
  const contentGeneratorRef = useRef(null);
  const turnCounterRef = useRef(0);
  useEffect(() => {
    const initializeGenerator = async () => {
      try {
        const configData = config.getContentGeneratorConfig();
        if (!configData) {
          throw new Error("No content generator configuration found");
        }
        const mappedAuthType = configData.authType || AuthType.USE_GEMINI;
        const generatorConfig = await createContentGeneratorConfig(
          config.getModel(),
          configData.apiKey,
          mappedAuthType
        );
        const generator = await createContentGenerator(generatorConfig);
        contentGeneratorRef.current = generator;
        setError(void 0);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to initialize content generator";
        setError(errorMessage);
        console.error("Failed to initialize content generator:", err);
      }
    };
    initializeGenerator();
  }, [config]);
  const generateId = useCallback(() => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }, []);
  const sendMessage = useCallback(
    async (content) => {
      if (!content.trim() || isGenerating) {
        return;
      }
      if (!contentGeneratorRef.current) {
        setError("Content generator not initialized");
        return;
      }
      const userMessage = {
        id: generateId(),
        role: "user",
        content: content.trim(),
        timestamp: /* @__PURE__ */ new Date()
      };
      setMessages((prev) => [...prev, userMessage]);
      setIsGenerating(true);
      setError(void 0);
      try {
        const turn = new Turn();
        turnCounterRef.current += 1;
        const response = await contentGeneratorRef.current.generateContent({
          model: config.getModel(),
          contents: [
            {
              role: "user",
              parts: [{ text: content.trim() }]
            }
          ]
        });
        if (response.candidates && response.candidates.length > 0) {
          const candidate = response.candidates[0];
          const responseText = candidate.content?.parts?.map((part) => "text" in part ? part.text : "").join("") || "No response generated";
          const assistantMessage = {
            id: generateId(),
            role: "assistant",
            content: responseText,
            timestamp: /* @__PURE__ */ new Date(),
            turn
          };
          setMessages((prev) => [...prev, assistantMessage]);
        } else {
          throw new Error("No response candidates received");
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to generate response";
        setError(errorMessage);
        const errorChatMessage = {
          id: generateId(),
          role: "assistant",
          content: `\u274C Error: ${errorMessage}`,
          timestamp: /* @__PURE__ */ new Date(),
          error: errorMessage
        };
        setMessages((prev) => [...prev, errorChatMessage]);
      } finally {
        setIsGenerating(false);
      }
    },
    [isGenerating, generateId]
  );
  const clearChat = useCallback(() => {
    setMessages([]);
    setError(void 0);
    turnCounterRef.current = 0;
  }, []);
  return {
    messages,
    isGenerating,
    sendMessage,
    clearChat,
    error
  };
};
export {
  useChat
};
//# sourceMappingURL=useChat.js.map
