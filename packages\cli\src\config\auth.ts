/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { AuthType } from '@arien/arien-cli-core';
import { AuthSettings } from '../ui/types.js';

/**
 * Maps UI-friendly auth method keys to system AuthType enum values
 */
export const mapAuthMethodToAuthType = (method: AuthSettings['method']): AuthType => {
  switch (method) {
    case 'google':
      return AuthType.LOGIN_WITH_GOOGLE;
    case 'api_key':
      return AuthType.USE_GEMINI;
    case 'vertex':
      return AuthType.USE_VERTEX_AI;
    default:
      return AuthType.USE_GEMINI;
  }
};

/**
 * Maps system AuthType enum values to UI-friendly auth method keys
 */
export const mapAuthTypeToAuthMethod = (authType: AuthType): AuthSettings['method'] => {
  switch (authType) {
    case AuthType.LOGIN_WITH_GOOGLE:
      return 'google';
    case AuthType.USE_GEMINI:
      return 'api_key';
    case AuthType.USE_VERTEX_AI:
      return 'vertex';
    default:
      return 'api_key';
  }
};

export const validateAuthMethod = (authMethod: string): string | null => {
  if (authMethod === AuthType.LOGIN_WITH_GOOGLE) {
    return null;
  }

  if (authMethod === AuthType.USE_GEMINI) {
    if (!process.env.GEMINI_API_KEY) {
      return 'GEMINI_API_KEY environment variable not found. Add that to your .env and try again, no reload needed!';
    }
    return null;
  }

  if (authMethod === AuthType.USE_VERTEX_AI) {
    const hasVertexProjectLocationConfig =
      !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;
    const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
    if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {
      return (
        'Must specify GOOGLE_GENAI_USE_VERTEXAI=true and either:\n' +
        '• GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\n' +
        '• GOOGLE_API_KEY environment variable (if using express mode).\n' +
        'Update your .env and try again, no reload needed!'
      );
    }
    return null;
  }

  return 'Invalid auth method selected.';
};
