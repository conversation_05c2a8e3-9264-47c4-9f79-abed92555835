/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

export interface ParsedError {
  message: string;
  stack?: string;
  code?: string;
  type: 'error' | 'warning' | 'info';
  file?: string;
  line?: number;
  column?: number;
}

export function parseError(error: unknown): ParsedError {
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: error.stack,
      type: 'error',
    };
  }

  if (typeof error === 'string') {
    return {
      message: error,
      type: 'error',
    };
  }

  if (typeof error === 'object' && error !== null) {
    const obj = error as Record<string, unknown>;
    return {
      message: String(obj.message || obj.toString?.() || 'Unknown error'),
      stack: typeof obj.stack === 'string' ? obj.stack : undefined,
      code: typeof obj.code === 'string' ? obj.code : undefined,
      type: 'error',
      file: typeof obj.file === 'string' ? obj.file : undefined,
      line: typeof obj.line === 'number' ? obj.line : undefined,
      column: typeof obj.column === 'number' ? obj.column : undefined,
    };
  }

  return {
    message: 'Unknown error occurred',
    type: 'error',
  };
}

export function formatError(error: ParsedError): string {
  let formatted = error.message;

  if (error.file) {
    formatted += ` (${error.file}`;
    if (error.line) {
      formatted += `:${error.line}`;
      if (error.column) {
        formatted += `:${error.column}`;
      }
    }
    formatted += ')';
  }

  if (error.code) {
    formatted += ` [${error.code}]`;
  }

  return formatted;
}

export function getErrorMessage(error: unknown): string {
  const parsed = parseError(error);
  return formatError(parsed);
}

/**
 * Alias for getErrorMessage for API error handling
 */
export function parseAndFormatApiError(error: unknown): string {
  return getErrorMessage(error);
}
