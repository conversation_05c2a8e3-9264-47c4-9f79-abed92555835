{"version": 3, "sources": ["../../../src/config/config.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport yargs from 'yargs/yargs';\nimport { hideBin } from 'yargs/helpers';\nimport process from 'node:process';\nimport {\n  Config,\n  loadServerHierarchicalMemory,\n  getCurrentGeminiMdFilename,\n  ApprovalMode,\n  GEMINI_CONFIG_DIR as GEMINI_DIR,\n  DEFAULT_GEMINI_MODEL,\n  DEFAULT_GEMINI_EMBEDDING_MODEL,\n  FileDiscoveryService,\n  TelemetryTarget,\n} from '@arien/arien-cli-core';\nimport { Settings } from './settings.js';\n\nimport { Extension } from './extension.js';\nimport { getCliVersion } from '../utils/version.js';\nimport * as dotenv from 'dotenv';\nimport * as fs from 'node:fs';\nimport * as path from 'node:path';\nimport * as os from 'node:os';\nimport { loadSandboxConfig } from './sandboxConfig.js';\n\n// Simple console logger for now - replace with actual logger if available\nconst logger = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  debug: (...args: any[]) => console.debug('[DEBUG]', ...args),\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  warn: (...args: any[]) => console.warn('[WARN]', ...args),\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  error: (...args: any[]) => console.error('[ERROR]', ...args),\n};\n\ninterface CliArgs {\n  model: string | undefined;\n  sandbox: boolean | string | undefined;\n  'sandbox-image': string | undefined;\n  debug: boolean | undefined;\n  prompt: string | undefined;\n  all_files: boolean | undefined;\n  show_memory_usage: boolean | undefined;\n  yolo: boolean | undefined;\n  telemetry: boolean | undefined;\n  checkpointing: boolean | undefined;\n  telemetryTarget: string | undefined;\n  telemetryOtlpEndpoint: string | undefined;\n  telemetryLogPrompts: boolean | undefined;\n}\n\nasync function parseArguments(): Promise<CliArgs> {\n  const argv = await yargs(hideBin(process.argv))\n    .option('model', {\n      alias: 'm',\n      type: 'string',\n      description: `Model`,\n      default: process.env.GEMINI_MODEL || DEFAULT_GEMINI_MODEL,\n    })\n    .option('prompt', {\n      alias: 'p',\n      type: 'string',\n      description: 'Prompt. Appended to input on stdin (if any).',\n    })\n    .option('sandbox', {\n      alias: 's',\n      type: 'boolean',\n      description: 'Run in sandbox?',\n    })\n    .option('sandbox-image', {\n      type: 'string',\n      description: 'Sandbox image URI.',\n    })\n    .option('debug', {\n      alias: 'd',\n      type: 'boolean',\n      description: 'Run in debug mode?',\n      default: false,\n    })\n    .option('all_files', {\n      alias: 'a',\n      type: 'boolean',\n      description: 'Include ALL files in context?',\n      default: false,\n    })\n    .option('show_memory_usage', {\n      type: 'boolean',\n      description: 'Show memory usage in status bar',\n      default: false,\n    })\n    .option('yolo', {\n      alias: 'y',\n      type: 'boolean',\n      description: 'Auto-approve all tool calls (dangerous!)',\n      default: false,\n    })\n    .option('telemetry', {\n      type: 'boolean',\n      description: 'Enable telemetry',\n    })\n    .option('checkpointing', {\n      type: 'boolean',\n      description: 'Enable checkpointing',\n    })\n    .option('telemetryTarget', {\n      type: 'string',\n      description: 'Telemetry target',\n    })\n    .option('telemetryOtlpEndpoint', {\n      type: 'string',\n      description: 'OTLP endpoint for telemetry',\n    })\n    .option('telemetryLogPrompts', {\n      type: 'boolean',\n      description: 'Log prompts in telemetry',\n    })\n    .help()\n    .alias('help', 'h')\n    .version(getCliVersion())\n    .alias('version', 'v')\n    .parse();\n\n  return argv as CliArgs;\n}\n\nexport async function loadCliConfig(\n  settings: Settings,\n  extensions: Extension[],\n  sessionId: string,\n): Promise<Config> {\n  const args = await parseArguments();\n\n  // Load environment variables from .env files\n  loadEnvFiles();\n\n  // Create config with hierarchical settings loading\n  // Convert Settings to ConfigOptions for initial configuration\n  const initialOptions = convertSettingsToConfigOptions(settings, sessionId);\n  const config = await Config.createWithSettings(process.cwd(), initialOptions);\n\n  // Apply CLI arguments (these override settings)\n  if (args.model) {\n    config.setModel(args.model);\n  }\n\n  if (args.debug !== undefined) {\n    config.setDebugMode(args.debug);\n  }\n\n  if (args.prompt) {\n    config.setQuestion(args.prompt);\n  }\n\n  if (args.all_files !== undefined) {\n    config.setIncludeAllFiles(args.all_files);\n  }\n\n  if (args.show_memory_usage !== undefined) {\n    config.setShowMemoryUsage(args.show_memory_usage);\n  }\n\n  if (args.yolo !== undefined) {\n    config.setApprovalMode(args.yolo ? ApprovalMode.YOLO : ApprovalMode.MANUAL);\n  }\n\n  if (args.telemetry !== undefined) {\n    config.setTelemetryEnabled(args.telemetry);\n  }\n\n  if (args.checkpointing !== undefined) {\n    config.setCheckpointingEnabled(args.checkpointing);\n  }\n\n  if (args.telemetryTarget) {\n    config.setTelemetryTarget(args.telemetryTarget as TelemetryTarget);\n  }\n\n  if (args.telemetryOtlpEndpoint) {\n    config.setTelemetryOtlpEndpoint(args.telemetryOtlpEndpoint);\n  }\n\n  if (args.telemetryLogPrompts !== undefined) {\n    config.setTelemetryLogPrompts(args.telemetryLogPrompts);\n  }\n\n  // Apply extensions\n  applyExtensions(config, extensions);\n\n  // Load sandbox configuration\n  if (args.sandbox !== undefined || args['sandbox-image']) {\n    const sandboxConfig = await loadSandboxConfig(\n      args.sandbox,\n      args['sandbox-image'],\n    );\n    if (sandboxConfig) {\n      config.setSandbox(sandboxConfig);\n    }\n  }\n\n  // Load hierarchical memory\n  try {\n    const memory = await loadServerHierarchicalMemory(\n      config.getWorkspaceRoot(),\n      getCurrentGeminiMdFilename(),\n    );\n    config.setMemory(memory);\n  } catch (error) {\n    logger.warn('Failed to load hierarchical memory:', error);\n  }\n\n  return config;\n}\n\nfunction loadEnvFiles() {\n  const envFiles = ['.env', '.env.local'];\n  \n  for (const envFile of envFiles) {\n    const envPath = path.join(process.cwd(), envFile);\n    if (fs.existsSync(envPath)) {\n      dotenv.config({ path: envPath });\n      logger.debug(`Loaded environment variables from ${envFile}`);\n    }\n  }\n\n  // Also check home directory\n  const homeEnvPath = path.join(os.homedir(), '.arien', '.env');\n  if (fs.existsSync(homeEnvPath)) {\n    dotenv.config({ path: homeEnvPath });\n    logger.debug('Loaded environment variables from ~/.arien/.env');\n  }\n}\n\n\n\nfunction applyExtensions(config: Config, extensions: Extension[]) {\n  for (const extension of extensions) {\n    try {\n      extension.configure?.(config);\n      logger.debug(`Applied extension: ${extension.name}`);\n    } catch (error) {\n      logger.error(`Failed to apply extension ${extension.name}:`, error);\n    }\n  }\n}\n\n/**\n * Convert Settings object to ConfigOptions for unified configuration\n */\nfunction convertSettingsToConfigOptions(settings: Settings, sessionId: string): any {\n  return {\n    sessionId,\n    model: settings.model,\n    embeddingModel: settings.embeddingModel,\n    debugMode: settings.debugMode,\n    includeAllFiles: settings.includeAllFiles,\n    showMemoryUsage: settings.showMemoryUsage,\n    approvalMode: settings.approvalMode,\n    telemetryEnabled: settings.telemetryEnabled,\n    checkpointingEnabled: settings.checkpointingEnabled,\n    telemetryTarget: settings.telemetryTarget,\n    telemetryOtlpEndpoint: settings.telemetryOtlpEndpoint,\n    telemetryLogPrompts: settings.telemetryLogPrompts,\n    excludeTools: settings.excludeTools,\n    includeTools: settings.includeTools,\n    geminiMdFilename: settings.geminiMdFilename,\n    theme: settings.theme,\n    selectedAuthType: settings.selectedAuthType,\n    usageStatisticsEnabled: settings.usageStatisticsEnabled,\n    preferredEditor: settings.preferredEditor,\n    accessibility: settings.accessibility,\n    telemetry: settings.telemetry,\n    bugCommand: settings.bugCommand,\n    checkpointing: settings.checkpointing,\n    autoConfigureMaxOldSpaceSize: settings.autoConfigureMaxOldSpaceSize,\n    fileFiltering: settings.fileFiltering,\n    hideWindowTitle: settings.hideWindowTitle,\n    hideTips: settings.hideTips,\n  };\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,OAAO,WAAW;AAClB,SAAS,eAAe;AACxB,OAAO,aAAa;AACpB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,OAIK;AAIP,SAAS,qBAAqB;AAC9B,YAAY,YAAY;AACxB,YAAY,QAAQ;AACpB,YAAY,UAAU;AACtB,YAAY,QAAQ;AACpB,SAAS,yBAAyB;AAGlC,MAAM,SAAS;AAAA;AAAA,EAEb,OAAO,IAAI,SAAgB,QAAQ,MAAM,WAAW,GAAG,IAAI;AAAA;AAAA,EAE3D,MAAM,IAAI,SAAgB,QAAQ,KAAK,UAAU,GAAG,IAAI;AAAA;AAAA,EAExD,OAAO,IAAI,SAAgB,QAAQ,MAAM,WAAW,GAAG,IAAI;AAC7D;AAkBA,eAAe,iBAAmC;AAChD,QAAM,OAAO,MAAM,MAAM,QAAQ,QAAQ,IAAI,CAAC,EAC3C,OAAO,SAAS;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS,QAAQ,IAAI,gBAAgB;AAAA,EACvC,CAAC,EACA,OAAO,UAAU;AAAA,IAChB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,WAAW;AAAA,IACjB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,iBAAiB;AAAA,IACvB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,SAAS;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,aAAa;AAAA,IACnB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,qBAAqB;AAAA,IAC3B,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,QAAQ;AAAA,IACd,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,EACX,CAAC,EACA,OAAO,aAAa;AAAA,IACnB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,iBAAiB;AAAA,IACvB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,mBAAmB;AAAA,IACzB,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,yBAAyB;AAAA,IAC/B,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,OAAO,uBAAuB;AAAA,IAC7B,MAAM;AAAA,IACN,aAAa;AAAA,EACf,CAAC,EACA,KAAK,EACL,MAAM,QAAQ,GAAG,EACjB,QAAQ,cAAc,CAAC,EACvB,MAAM,WAAW,GAAG,EACpB,MAAM;AAET,SAAO;AACT;AAEA,eAAsB,cACpB,UACA,YACA,WACiB;AACjB,QAAM,OAAO,MAAM,eAAe;AAGlC,eAAa;AAIb,QAAM,iBAAiB,+BAA+B,UAAU,SAAS;AACzE,QAAM,SAAS,MAAM,OAAO,mBAAmB,QAAQ,IAAI,GAAG,cAAc;AAG5E,MAAI,KAAK,OAAO;AACd,WAAO,SAAS,KAAK,KAAK;AAAA,EAC5B;AAEA,MAAI,KAAK,UAAU,QAAW;AAC5B,WAAO,aAAa,KAAK,KAAK;AAAA,EAChC;AAEA,MAAI,KAAK,QAAQ;AACf,WAAO,YAAY,KAAK,MAAM;AAAA,EAChC;AAEA,MAAI,KAAK,cAAc,QAAW;AAChC,WAAO,mBAAmB,KAAK,SAAS;AAAA,EAC1C;AAEA,MAAI,KAAK,sBAAsB,QAAW;AACxC,WAAO,mBAAmB,KAAK,iBAAiB;AAAA,EAClD;AAEA,MAAI,KAAK,SAAS,QAAW;AAC3B,WAAO,gBAAgB,KAAK,OAAO,aAAa,OAAO,aAAa,MAAM;AAAA,EAC5E;AAEA,MAAI,KAAK,cAAc,QAAW;AAChC,WAAO,oBAAoB,KAAK,SAAS;AAAA,EAC3C;AAEA,MAAI,KAAK,kBAAkB,QAAW;AACpC,WAAO,wBAAwB,KAAK,aAAa;AAAA,EACnD;AAEA,MAAI,KAAK,iBAAiB;AACxB,WAAO,mBAAmB,KAAK,eAAkC;AAAA,EACnE;AAEA,MAAI,KAAK,uBAAuB;AAC9B,WAAO,yBAAyB,KAAK,qBAAqB;AAAA,EAC5D;AAEA,MAAI,KAAK,wBAAwB,QAAW;AAC1C,WAAO,uBAAuB,KAAK,mBAAmB;AAAA,EACxD;AAGA,kBAAgB,QAAQ,UAAU;AAGlC,MAAI,KAAK,YAAY,UAAa,KAAK,eAAe,GAAG;AACvD,UAAM,gBAAgB,MAAM;AAAA,MAC1B,KAAK;AAAA,MACL,KAAK,eAAe;AAAA,IACtB;AACA,QAAI,eAAe;AACjB,aAAO,WAAW,aAAa;AAAA,IACjC;AAAA,EACF;AAGA,MAAI;AACF,UAAM,SAAS,MAAM;AAAA,MACnB,OAAO,iBAAiB;AAAA,MACxB,2BAA2B;AAAA,IAC7B;AACA,WAAO,UAAU,MAAM;AAAA,EACzB,SAAS,OAAO;AACd,WAAO,KAAK,uCAAuC,KAAK;AAAA,EAC1D;AAEA,SAAO;AACT;AAEA,SAAS,eAAe;AACtB,QAAM,WAAW,CAAC,QAAQ,YAAY;AAEtC,aAAW,WAAW,UAAU;AAC9B,UAAM,UAAU,KAAK,KAAK,QAAQ,IAAI,GAAG,OAAO;AAChD,QAAI,GAAG,WAAW,OAAO,GAAG;AAC1B,aAAO,OAAO,EAAE,MAAM,QAAQ,CAAC;AAC/B,aAAO,MAAM,qCAAqC,OAAO,EAAE;AAAA,IAC7D;AAAA,EACF;AAGA,QAAM,cAAc,KAAK,KAAK,GAAG,QAAQ,GAAG,UAAU,MAAM;AAC5D,MAAI,GAAG,WAAW,WAAW,GAAG;AAC9B,WAAO,OAAO,EAAE,MAAM,YAAY,CAAC;AACnC,WAAO,MAAM,iDAAiD;AAAA,EAChE;AACF;AAIA,SAAS,gBAAgB,QAAgB,YAAyB;AAChE,aAAW,aAAa,YAAY;AAClC,QAAI;AACF,gBAAU,YAAY,MAAM;AAC5B,aAAO,MAAM,sBAAsB,UAAU,IAAI,EAAE;AAAA,IACrD,SAAS,OAAO;AACd,aAAO,MAAM,6BAA6B,UAAU,IAAI,KAAK,KAAK;AAAA,IACpE;AAAA,EACF;AACF;AAKA,SAAS,+BAA+B,UAAoB,WAAwB;AAClF,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAS;AAAA,IAChB,gBAAgB,SAAS;AAAA,IACzB,WAAW,SAAS;AAAA,IACpB,iBAAiB,SAAS;AAAA,IAC1B,iBAAiB,SAAS;AAAA,IAC1B,cAAc,SAAS;AAAA,IACvB,kBAAkB,SAAS;AAAA,IAC3B,sBAAsB,SAAS;AAAA,IAC/B,iBAAiB,SAAS;AAAA,IAC1B,uBAAuB,SAAS;AAAA,IAChC,qBAAqB,SAAS;AAAA,IAC9B,cAAc,SAAS;AAAA,IACvB,cAAc,SAAS;AAAA,IACvB,kBAAkB,SAAS;AAAA,IAC3B,OAAO,SAAS;AAAA,IAChB,kBAAkB,SAAS;AAAA,IAC3B,wBAAwB,SAAS;AAAA,IACjC,iBAAiB,SAAS;AAAA,IAC1B,eAAe,SAAS;AAAA,IACxB,WAAW,SAAS;AAAA,IACpB,YAAY,SAAS;AAAA,IACrB,eAAe,SAAS;AAAA,IACxB,8BAA8B,SAAS;AAAA,IACvC,eAAe,SAAS;AAAA,IACxB,iBAAiB,SAAS;AAAA,IAC1B,UAAU,SAAS;AAAA,EACrB;AACF;", "names": []}