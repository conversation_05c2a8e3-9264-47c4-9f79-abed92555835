{"version": 3, "sources": ["../../../../src/ui/utils/errorParsing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nexport interface ParsedError {\n  message: string;\n  stack?: string;\n  code?: string;\n  type: 'error' | 'warning' | 'info';\n  file?: string;\n  line?: number;\n  column?: number;\n}\n\nexport function parseError(error: unknown): ParsedError {\n  if (error instanceof Error) {\n    return {\n      message: error.message,\n      stack: error.stack,\n      type: 'error',\n    };\n  }\n\n  if (typeof error === 'string') {\n    return {\n      message: error,\n      type: 'error',\n    };\n  }\n\n  if (typeof error === 'object' && error !== null) {\n    const obj = error as Record<string, unknown>;\n    return {\n      message: String(obj.message || obj.toString?.() || 'Unknown error'),\n      stack: typeof obj.stack === 'string' ? obj.stack : undefined,\n      code: typeof obj.code === 'string' ? obj.code : undefined,\n      type: 'error',\n      file: typeof obj.file === 'string' ? obj.file : undefined,\n      line: typeof obj.line === 'number' ? obj.line : undefined,\n      column: typeof obj.column === 'number' ? obj.column : undefined,\n    };\n  }\n\n  return {\n    message: 'Unknown error occurred',\n    type: 'error',\n  };\n}\n\nexport function formatError(error: ParsedError): string {\n  let formatted = error.message;\n\n  if (error.file) {\n    formatted += ` (${error.file}`;\n    if (error.line) {\n      formatted += `:${error.line}`;\n      if (error.column) {\n        formatted += `:${error.column}`;\n      }\n    }\n    formatted += ')';\n  }\n\n  if (error.code) {\n    formatted += ` [${error.code}]`;\n  }\n\n  return formatted;\n}\n\nexport function getErrorMessage(error: unknown): string {\n  const parsed = parseError(error);\n  return formatError(parsed);\n}\n\n/**\n * Alias for getErrorMessage for API error handling\n */\nexport function parseAndFormatApiError(error: unknown): string {\n  return getErrorMessage(error);\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAgBO,SAAS,WAAW,OAA6B;AACtD,MAAI,iBAAiB,OAAO;AAC1B,WAAO;AAAA,MACL,SAAS,MAAM;AAAA,MACf,OAAO,MAAM;AAAA,MACb,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,UAAM,MAAM;AACZ,WAAO;AAAA,MACL,SAAS,OAAO,IAAI,WAAW,IAAI,WAAW,KAAK,eAAe;AAAA,MAClE,OAAO,OAAO,IAAI,UAAU,WAAW,IAAI,QAAQ;AAAA,MACnD,MAAM,OAAO,IAAI,SAAS,WAAW,IAAI,OAAO;AAAA,MAChD,MAAM;AAAA,MACN,MAAM,OAAO,IAAI,SAAS,WAAW,IAAI,OAAO;AAAA,MAChD,MAAM,OAAO,IAAI,SAAS,WAAW,IAAI,OAAO;AAAA,MAChD,QAAQ,OAAO,IAAI,WAAW,WAAW,IAAI,SAAS;AAAA,IACxD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM;AAAA,EACR;AACF;AAEO,SAAS,YAAY,OAA4B;AACtD,MAAI,YAAY,MAAM;AAEtB,MAAI,MAAM,MAAM;AACd,iBAAa,KAAK,MAAM,IAAI;AAC5B,QAAI,MAAM,MAAM;AACd,mBAAa,IAAI,MAAM,IAAI;AAC3B,UAAI,MAAM,QAAQ;AAChB,qBAAa,IAAI,MAAM,MAAM;AAAA,MAC/B;AAAA,IACF;AACA,iBAAa;AAAA,EACf;AAEA,MAAI,MAAM,MAAM;AACd,iBAAa,KAAK,MAAM,IAAI;AAAA,EAC9B;AAEA,SAAO;AACT;AAEO,SAAS,gBAAgB,OAAwB;AACtD,QAAM,SAAS,WAAW,KAAK;AAC/B,SAAO,YAAY,MAAM;AAC3B;AAKO,SAAS,uBAAuB,OAAwB;AAC7D,SAAO,gBAAgB,KAAK;AAC9B;", "names": []}