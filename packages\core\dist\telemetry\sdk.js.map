{"version": 3, "sources": ["../../src/telemetry/sdk.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { NodeSDK } from '@opentelemetry/sdk-node';\nimport { Resource } from '@opentelemetry/resources';\nimport { SEMRESATTRS_SERVICE_NAME, SEMRESATTRS_SERVICE_VERSION } from '@opentelemetry/semantic-conventions';\nimport { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';\nimport { TelemetryConfig } from './types.js';\n\nlet sdk: NodeSDK | null = null;\nlet isInitialized = false;\n\n/**\n * Initializes the OpenTelemetry SDK with the provided configuration\n */\nexport function initializeTelemetry(config: TelemetryConfig): void {\n  if (isInitialized || !config.enabled) {\n    return;\n  }\n\n  try {\n    const resource = new Resource({\n      [SEMRESATTRS_SERVICE_NAME]: 'arien-cli',\n      [SEMRESATTRS_SERVICE_VERSION]: config.version || '1.0.0',\n      'service.instance.id': config.sessionId,\n      'user.id': config.userId || 'anonymous',\n    });\n\n    sdk = new NodeSDK({\n      resource,\n      instrumentations: [getNodeAutoInstrumentations({\n        // Disable some instrumentations that might be noisy\n        '@opentelemetry/instrumentation-fs': {\n          enabled: false,\n        },\n        '@opentelemetry/instrumentation-dns': {\n          enabled: false,\n        },\n      })],\n    });\n\n    sdk.start();\n    isInitialized = true;\n    console.debug('OpenTelemetry SDK initialized successfully');\n  } catch (error) {\n    console.error('Failed to initialize OpenTelemetry SDK:', error);\n  }\n}\n\n/**\n * Shuts down the OpenTelemetry SDK\n */\nexport async function shutdownTelemetry(): Promise<void> {\n  if (sdk && isInitialized) {\n    try {\n      await sdk.shutdown();\n      isInitialized = false;\n      console.debug('OpenTelemetry SDK shut down successfully');\n    } catch (error) {\n      console.error('Failed to shut down OpenTelemetry SDK:', error);\n    }\n  }\n}\n\n/**\n * Returns whether telemetry is initialized\n */\nexport function isTelemetryInitialized(): boolean {\n  return isInitialized;\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,eAAe;AACxB,SAAS,gBAAgB;AACzB,SAAS,0BAA0B,mCAAmC;AACtE,SAAS,mCAAmC;AAG5C,IAAI,MAAsB;AAC1B,IAAI,gBAAgB;AAKb,SAAS,oBAAoB,QAA+B;AACjE,MAAI,iBAAiB,CAAC,OAAO,SAAS;AACpC;AAAA,EACF;AAEA,MAAI;AACF,UAAM,WAAW,IAAI,SAAS;AAAA,MAC5B,CAAC,wBAAwB,GAAG;AAAA,MAC5B,CAAC,2BAA2B,GAAG,OAAO,WAAW;AAAA,MACjD,uBAAuB,OAAO;AAAA,MAC9B,WAAW,OAAO,UAAU;AAAA,IAC9B,CAAC;AAED,UAAM,IAAI,QAAQ;AAAA,MAChB;AAAA,MACA,kBAAkB,CAAC,4BAA4B;AAAA;AAAA,QAE7C,qCAAqC;AAAA,UACnC,SAAS;AAAA,QACX;AAAA,QACA,sCAAsC;AAAA,UACpC,SAAS;AAAA,QACX;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,CAAC;AAED,QAAI,MAAM;AACV,oBAAgB;AAChB,YAAQ,MAAM,4CAA4C;AAAA,EAC5D,SAAS,OAAO;AACd,YAAQ,MAAM,2CAA2C,KAAK;AAAA,EAChE;AACF;AAKA,eAAsB,oBAAmC;AACvD,MAAI,OAAO,eAAe;AACxB,QAAI;AACF,YAAM,IAAI,SAAS;AACnB,sBAAgB;AAChB,cAAQ,MAAM,0CAA0C;AAAA,IAC1D,SAAS,OAAO;AACd,cAAQ,MAAM,0CAA0C,KAAK;AAAA,IAC/D;AAAA,EACF;AACF;AAKO,SAAS,yBAAkC;AAChD,SAAO;AACT;", "names": []}