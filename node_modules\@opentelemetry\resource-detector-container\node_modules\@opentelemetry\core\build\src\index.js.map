{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,mFAAkF;AAAzE,4HAAA,oBAAoB,OAAA;AAC7B,0DAA+D;AAAtD,+GAAA,aAAa,OAAA;AACtB,kDAI6B;AAH3B,4GAAA,cAAc,OAAA;AACd,8GAAA,gBAAgB,OAAA;AAChB,gHAAA,kBAAkB,OAAA;AAEpB,sEAGuC;AAFrC,0HAAA,kBAAkB,OAAA;AAClB,6HAAA,qBAAqB,OAAA;AAEvB,wEAAqE;AAA5D,4HAAA,mBAAmB,OAAA;AAC5B,sCAauB;AAZrB,kGAAA,UAAU,OAAA;AACV,qGAAA,aAAa,OAAA;AACb,8FAAA,MAAM,OAAA;AACN,sGAAA,cAAc,OAAA;AACd,4GAAA,oBAAoB,OAAA;AACpB,4GAAA,oBAAoB,OAAA;AACpB,2GAAA,mBAAmB,OAAA;AACnB,yGAAA,iBAAiB,OAAA;AACjB,mGAAA,WAAW,OAAA;AACX,yGAAA,iBAAiB,OAAA;AACjB,sGAAA,cAAc,OAAA;AACd,yGAAA,iBAAiB,OAAA;AASnB,wDAAqD;AAA5C,4GAAA,WAAW,OAAA;AACpB,+CAAgE;AAAzC,gHAAA,gBAAgB,OAAA;AACvC,2CAKyB;AACZ,QAAA,YAAY,GAAG;IAC1B,WAAW,EAAX,mBAAW;IACX,iBAAiB,EAAjB,yBAAiB;IACjB,uBAAuB,EAAvB,+BAAuB;IACvB,iBAAiB,EAAjB,yBAAiB;CAClB,CAAC;AACF,uCASoB;AARlB,6GAAA,iBAAiB,OAAA;AACjB,oGAAA,QAAQ,OAAA;AACR,uGAAA,WAAW,OAAA;AACX,kGAAA,MAAM,OAAA;AACN,iHAAA,qBAAqB,OAAA;AACrB,uGAAA,WAAW,OAAA;AACX,yGAAA,aAAa,OAAA;AACb,sGAAA,UAAU,OAAA;AAEZ,qDAGiC;AAF/B,gHAAA,mBAAmB,OAAA;AAGrB,+EAK2C;AAJzC,gIAAA,mBAAmB,OAAA;AACnB,+HAAA,kBAAkB,OAAA;AAClB,sIAAA,yBAAyB,OAAA;AACzB,6HAAA,gBAAgB,OAAA;AAGlB,qDAM8B;AAJ5B,uGAAA,OAAO,OAAA;AACP,iHAAA,iBAAiB,OAAA;AACjB,8GAAA,cAAc,OAAA;AACd,8GAAA,cAAc,OAAA;AAEhB,qEAAoE;AAA3D,oHAAA,gBAAgB,OAAA;AACzB,mEAAkE;AAAzD,kHAAA,eAAe,OAAA;AACxB,yEAAwE;AAA/D,wHAAA,kBAAkB,OAAA;AAC3B,qFAAoF;AAA3E,oIAAA,wBAAwB,OAAA;AACjC,6DAIkC;AAHhC,uHAAA,mBAAmB,OAAA;AACnB,mHAAA,eAAe,OAAA;AACf,qHAAA,iBAAiB,OAAA;AAEnB,iDAAgD;AAAvC,wGAAA,UAAU,OAAA;AACnB,mDAS6B;AAR3B,4HAAA,6BAA6B,OAAA;AAC7B,mIAAA,oCAAoC,OAAA;AACpC,kHAAA,mBAAmB,OAAA;AACnB,2IAAA,4CAA4C,OAAA;AAC5C,0IAAA,2CAA2C,OAAA;AAG3C,+GAAA,gBAAgB,OAAA;AAElB,uCAAsC;AAA7B,8FAAA,KAAK,OAAA;AACd,6CAAuD;AAA9C,+GAAA,mBAAmB,OAAA;AAC5B,2CAAgE;AAAvD,uGAAA,YAAY,OAAA;AAAE,0GAAA,eAAe,OAAA;AACtC,mCAAuD;AAA9C,mGAAA,YAAY,OAAA;AAAE,iGAAA,UAAU,OAAA;AACjC,qCAAyC;AAAhC,iGAAA,SAAS,OAAA;AAClB,6CAAkD;AAAzC,0GAAA,cAAc,OAAA;AACvB,qCAAoC;AAA3B,kGAAA,OAAO,OAAA;AAChB,kDAA8C;AACjC,QAAA,QAAQ,GAAG;IACtB,OAAO,EAAP,kBAAO;CACR,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { W3CBaggagePropagator } from './baggage/propagation/W3CBaggagePropagator';\nexport { AnchoredClock, Clock } from './common/anchored-clock';\nexport {\n  isAttributeKey,\n  isAttributeValue,\n  sanitizeAttributes,\n} from './common/attributes';\nexport {\n  globalErrorHandler,\n  setGlobalErrorHandler,\n} from './common/global-error-handler';\nexport { loggingErrorHandler } from './common/logging-error-handler';\nexport {\n  addHrTimes,\n  getTimeOrigin,\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMicroseconds,\n  hrTimeToMilliseconds,\n  hrTimeToNanoseconds,\n  hrTimeToTimeStamp,\n  isTimeInput,\n  isTimeInputHrTime,\n  millisToHrTime,\n  timeInputToHrTime,\n} from './common/time';\nexport {\n  ErrorHandler,\n  InstrumentationLibrary,\n  InstrumentationScope,\n  ShimWrapped,\n  TimeOriginLegacy,\n} from './common/types';\nexport { hexToBinary } from './common/hex-to-binary';\nexport { ExportResult, ExportResultCode } from './ExportResult';\nimport {\n  getKeyPairs,\n  serializeKeyPairs,\n  parseKeyPairsIntoRecord,\n  parsePairKeyValue,\n} from './baggage/utils';\nexport const baggageUtils = {\n  getKeyPairs,\n  serializeKeyPairs,\n  parseKeyPairsIntoRecord,\n  parsePairKeyValue,\n};\nexport {\n  RandomIdGenerator,\n  SDK_INFO,\n  _globalThis,\n  getEnv,\n  getEnvWithoutDefaults,\n  hexToBase64,\n  otperformance,\n  unrefTimer,\n} from './platform';\nexport {\n  CompositePropagator,\n  CompositePropagatorConfig,\n} from './propagation/composite';\nexport {\n  TRACE_PARENT_HEADER,\n  TRACE_STATE_HEADER,\n  W3CTraceContextPropagator,\n  parseTraceParent,\n} from './trace/W3CTraceContextPropagator';\nexport { IdGenerator } from './trace/IdGenerator';\nexport {\n  RPCMetadata,\n  RPCType,\n  deleteRPCMetadata,\n  getRPCMetadata,\n  setRPCMetadata,\n} from './trace/rpc-metadata';\nexport { AlwaysOffSampler } from './trace/sampler/AlwaysOffSampler';\nexport { AlwaysOnSampler } from './trace/sampler/AlwaysOnSampler';\nexport { ParentBasedSampler } from './trace/sampler/ParentBasedSampler';\nexport { TraceIdRatioBasedSampler } from './trace/sampler/TraceIdRatioBasedSampler';\nexport {\n  isTracingSuppressed,\n  suppressTracing,\n  unsuppressTracing,\n} from './trace/suppress-tracing';\nexport { TraceState } from './trace/TraceState';\nexport {\n  DEFAULT_ATTRIBUTE_COUNT_LIMIT,\n  DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT,\n  DEFAULT_ENVIRONMENT,\n  DEFAULT_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,\n  DEFAULT_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT,\n  ENVIRONMENT,\n  RAW_ENVIRONMENT,\n  parseEnvironment,\n} from './utils/environment';\nexport { merge } from './utils/merge';\nexport { TracesSamplerValues } from './utils/sampling';\nexport { TimeoutError, callWithTimeout } from './utils/timeout';\nexport { isUrlIgnored, urlMatches } from './utils/url';\nexport { isWrapped } from './utils/wrap';\nexport { BindOnceFuture } from './utils/callback';\nexport { VERSION } from './version';\nimport { _export } from './internal/exporter';\nexport const internal = {\n  _export,\n};\n"]}