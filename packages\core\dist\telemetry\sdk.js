/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { NodeSDK } from "@opentelemetry/sdk-node";
import { Resource } from "@opentelemetry/resources";
import { SEMRESATTRS_SERVICE_NAME, SEMRESATTRS_SERVICE_VERSION } from "@opentelemetry/semantic-conventions";
import { getNodeAutoInstrumentations } from "@opentelemetry/auto-instrumentations-node";
let sdk = null;
let isInitialized = false;
function initializeTelemetry(config) {
  if (isInitialized || !config.enabled) {
    return;
  }
  try {
    const resource = new Resource({
      [SEMRESATTRS_SERVICE_NAME]: "arien-cli",
      [SEMRESATTRS_SERVICE_VERSION]: config.version || "1.0.0",
      "service.instance.id": config.sessionId,
      "user.id": config.userId || "anonymous"
    });
    sdk = new NodeSDK({
      resource,
      instrumentations: [getNodeAutoInstrumentations({
        // Disable some instrumentations that might be noisy
        "@opentelemetry/instrumentation-fs": {
          enabled: false
        },
        "@opentelemetry/instrumentation-dns": {
          enabled: false
        }
      })]
    });
    sdk.start();
    isInitialized = true;
    console.debug("OpenTelemetry SDK initialized successfully");
  } catch (error) {
    console.error("Failed to initialize OpenTelemetry SDK:", error);
  }
}
async function shutdownTelemetry() {
  if (sdk && isInitialized) {
    try {
      await sdk.shutdown();
      isInitialized = false;
      console.debug("OpenTelemetry SDK shut down successfully");
    } catch (error) {
      console.error("Failed to shut down OpenTelemetry SDK:", error);
    }
  }
}
function isTelemetryInitialized() {
  return isInitialized;
}
export {
  initializeTelemetry,
  isTelemetryInitialized,
  shutdownTelemetry
};
//# sourceMappingURL=sdk.js.map
