#!/usr/bin/env node
var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});

// packages/cli/src/arien.tsx
import React4 from "react";
import { render } from "ink";

// packages/cli/src/ui/App.tsx
import { useState, useCallback } from "react";
import { Box, Text, useInput, useApp } from "ink";
import { jsx, jsxs } from "react/jsx-runtime";
var AppWrapper = (props) => {
  return /* @__PURE__ */ jsx(App, { ...props });
};
var App = ({ config: config2, settings, startupWarnings = [] }) => {
  void config2;
  void settings;
  void startupWarnings;
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState([]);
  const { exit } = useApp();
  const handleInput = useCallback(async (inputText) => {
    if (!inputText.trim()) return;
    setInput("");
    setIsLoading(true);
    try {
      const userMessage = {
        type: "user",
        text: inputText,
        id: Date.now().toString()
      };
      setMessages((prev) => [...prev, userMessage]);
      if (inputText.startsWith("/help")) {
        const helpMessage = {
          type: "system",
          text: "Available commands:\n/help - Show this help\n/clear - Clear history\n/exit - Exit application",
          id: (Date.now() + 1).toString()
        };
        setMessages((prev) => [...prev, helpMessage]);
      } else if (inputText.startsWith("/clear")) {
        setMessages([]);
      } else if (inputText.startsWith("/exit")) {
        exit();
      } else {
        const echoMessage = {
          type: "assistant",
          text: `Echo: ${inputText}`,
          id: (Date.now() + 1).toString()
        };
        setMessages((prev) => [...prev, echoMessage]);
      }
    } catch (error) {
      const errorMessage = {
        type: "error",
        text: `Error: ${error}`,
        id: (Date.now() + 1).toString()
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [exit]);
  useInput((input2, key) => {
    if (key.return) {
      handleInput(input2);
      setInput("");
    } else if (key.ctrl && input2 === "c") {
      exit();
    } else if (!key.ctrl && !key.meta && input2.length === 1) {
      setInput((prev) => prev + input2);
    } else if (key.backspace || key.delete) {
      setInput((prev) => prev.slice(0, -1));
    }
  });
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", height: "100%", children: [
    /* @__PURE__ */ jsx(Box, { borderStyle: "single", paddingX: 1, children: /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u{1F916} Arien CLI - AI Assistant for Coding" }) }),
    /* @__PURE__ */ jsxs(Box, { flexDirection: "column", flexGrow: 1, paddingX: 1, children: [
      messages.map((message) => /* @__PURE__ */ jsx(Box, { marginY: 0, children: /* @__PURE__ */ jsxs(Text, { color: message.type === "user" ? "green" : message.type === "error" ? "red" : message.type === "system" ? "yellow" : "white", children: [
        message.type === "user" ? "> " : "",
        message.text
      ] }) }, message.id)),
      isLoading && /* @__PURE__ */ jsx(Box, { children: /* @__PURE__ */ jsx(Text, { color: "gray", children: "Processing..." }) })
    ] }),
    /* @__PURE__ */ jsxs(Box, { borderStyle: "single", paddingX: 1, children: [
      /* @__PURE__ */ jsx(Text, { color: "blue", children: "> " }),
      /* @__PURE__ */ jsx(Text, { children: input }),
      /* @__PURE__ */ jsx(Text, { color: "gray", children: "\u2588" })
    ] }),
    /* @__PURE__ */ jsx(Box, { paddingX: 1, children: /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: "Type /help for commands, /exit to quit, Ctrl+C to exit" }) })
  ] });
};

// packages/cli/src/config/config.ts
import yargs from "yargs/yargs";
import { hideBin } from "yargs/helpers";
import process2 from "node:process";
import {
  Config,
  loadServerHierarchicalMemory,
  setGeminiMdFilename as setServerGeminiMdFilename,
  getCurrentGeminiMdFilename,
  ApprovalMode,
  DEFAULT_GEMINI_MODEL
} from "@arien/arien-cli-core";

// packages/cli/src/utils/package.ts
import * as fs from "fs";
import * as path from "path";
import { fileURLToPath } from "url";
var __filename = fileURLToPath(import.meta.url);
var __dirname2 = path.dirname(__filename);
var packageJson;
function getPackageJson() {
  if (packageJson) {
    return packageJson;
  }
  let currentDir = __dirname2;
  while (currentDir !== path.dirname(currentDir)) {
    const packagePath = path.join(currentDir, "package.json");
    if (fs.existsSync(packagePath)) {
      try {
        const content = fs.readFileSync(packagePath, "utf8");
        packageJson = JSON.parse(content);
        return packageJson;
      } catch (error) {
        console.error(`Error reading package.json at ${packagePath}:`, error);
        break;
      }
    }
    currentDir = path.dirname(currentDir);
  }
  return void 0;
}

// packages/cli/src/utils/version.ts
function getCliVersion() {
  try {
    const pkgJson = getPackageJson();
    return process.env.CLI_VERSION || pkgJson?.version || "unknown";
  } catch {
    return "unknown";
  }
}

// packages/cli/src/config/config.ts
import * as dotenv from "dotenv";
import * as fs2 from "node:fs";
import * as path2 from "node:path";
import * as os2 from "node:os";

// packages/cli/src/config/sandboxConfig.ts
import commandExists from "command-exists";
import * as os from "node:os";
var VALID_SANDBOX_COMMANDS = [
  "docker",
  "podman",
  "sandbox-exec"
];
function isSandboxCommand(value) {
  return VALID_SANDBOX_COMMANDS.includes(value);
}
function getSandboxCommand(sandbox) {
  if (process.env.SANDBOX) {
    return "";
  }
  const environmentConfiguredSandbox = process.env.ARIEN_SANDBOX?.toLowerCase().trim() ?? "";
  sandbox = environmentConfiguredSandbox?.length > 0 ? environmentConfiguredSandbox : sandbox;
  if (sandbox === "1" || sandbox === "true") sandbox = true;
  else if (sandbox === "0" || sandbox === "false" || !sandbox) sandbox = false;
  if (sandbox === false) {
    return "";
  }
  if (typeof sandbox === "string" && sandbox) {
    if (!isSandboxCommand(sandbox)) {
      console.error(
        `ERROR: invalid sandbox command '${sandbox}'. Must be one of ${VALID_SANDBOX_COMMANDS.join(
          ", "
        )}`
      );
      process.exit(1);
    }
    if (commandExists.sync(sandbox)) {
      return sandbox;
    }
    console.error(
      `ERROR: missing sandbox command '${sandbox}' (from ARIEN_SANDBOX)`
    );
    process.exit(1);
  }
  if (os.platform() === "darwin" && commandExists.sync("sandbox-exec")) {
    return "sandbox-exec";
  } else if (commandExists.sync("docker") && sandbox === true) {
    return "docker";
  } else if (commandExists.sync("podman") && sandbox === true) {
    return "podman";
  }
  if (sandbox === true) {
    console.error(
      "ERROR: ARIEN_SANDBOX is true but failed to determine command for sandbox; install docker or podman or specify command in ARIEN_SANDBOX"
    );
    process.exit(1);
  }
  return "";
}
async function loadSandboxConfig(sandbox, sandboxImage) {
  const command = getSandboxCommand(sandbox);
  const packageJson2 = await getPackageJson();
  const image = sandboxImage ?? process.env.ARIEN_SANDBOX_IMAGE ?? packageJson2?.config?.sandboxImageUri;
  return command && image ? { command, image } : void 0;
}

// packages/cli/src/config/config.ts
var logger = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  debug: (...args) => console.debug("[DEBUG]", ...args),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  warn: (...args) => console.warn("[WARN]", ...args),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: (...args) => console.error("[ERROR]", ...args)
};
async function parseArguments() {
  const argv = await yargs(hideBin(process2.argv)).option("model", {
    alias: "m",
    type: "string",
    description: `Model`,
    default: process2.env.GEMINI_MODEL || DEFAULT_GEMINI_MODEL
  }).option("prompt", {
    alias: "p",
    type: "string",
    description: "Prompt. Appended to input on stdin (if any)."
  }).option("sandbox", {
    alias: "s",
    type: "boolean",
    description: "Run in sandbox?"
  }).option("sandbox-image", {
    type: "string",
    description: "Sandbox image URI."
  }).option("debug", {
    alias: "d",
    type: "boolean",
    description: "Run in debug mode?",
    default: false
  }).option("all_files", {
    alias: "a",
    type: "boolean",
    description: "Include ALL files in context?",
    default: false
  }).option("show_memory_usage", {
    type: "boolean",
    description: "Show memory usage in status bar",
    default: false
  }).option("yolo", {
    alias: "y",
    type: "boolean",
    description: "Auto-approve all tool calls (dangerous!)",
    default: false
  }).option("telemetry", {
    type: "boolean",
    description: "Enable telemetry"
  }).option("checkpointing", {
    type: "boolean",
    description: "Enable checkpointing"
  }).option("telemetryTarget", {
    type: "string",
    description: "Telemetry target"
  }).option("telemetryOtlpEndpoint", {
    type: "string",
    description: "OTLP endpoint for telemetry"
  }).option("telemetryLogPrompts", {
    type: "boolean",
    description: "Log prompts in telemetry"
  }).help().alias("help", "h").version(getCliVersion()).alias("version", "v").parse();
  return argv;
}
async function loadCliConfig(settings, extensions, sessionId2) {
  const args = await parseArguments();
  loadEnvFiles();
  const config2 = new Config({
    workspaceRoot: process2.cwd(),
    sessionId: sessionId2
  });
  if (args.model) {
    config2.setModel(args.model);
  }
  if (args.debug !== void 0) {
    config2.setDebugMode(args.debug);
  }
  if (args.prompt) {
    config2.setQuestion(args.prompt);
  }
  if (args.all_files !== void 0) {
    config2.setIncludeAllFiles(args.all_files);
  }
  if (args.show_memory_usage !== void 0) {
    config2.setShowMemoryUsage(args.show_memory_usage);
  }
  if (args.yolo !== void 0) {
    config2.setApprovalMode(args.yolo ? ApprovalMode.YOLO : ApprovalMode.MANUAL);
  }
  if (args.telemetry !== void 0) {
    config2.setTelemetryEnabled(args.telemetry);
  }
  if (args.checkpointing !== void 0) {
    config2.setCheckpointingEnabled(args.checkpointing);
  }
  if (args.telemetryTarget) {
    config2.setTelemetryTarget(args.telemetryTarget);
  }
  if (args.telemetryOtlpEndpoint) {
    config2.setTelemetryOtlpEndpoint(args.telemetryOtlpEndpoint);
  }
  if (args.telemetryLogPrompts !== void 0) {
    config2.setTelemetryLogPrompts(args.telemetryLogPrompts);
  }
  applySettings(config2, settings);
  applyExtensions(config2, extensions);
  if (args.sandbox !== void 0 || args["sandbox-image"]) {
    const sandboxConfig = await loadSandboxConfig(
      args.sandbox,
      args["sandbox-image"]
    );
    if (sandboxConfig) {
      config2.setSandbox(sandboxConfig);
    }
  }
  try {
    const memory = await loadServerHierarchicalMemory(
      config2.getWorkspaceRoot(),
      getCurrentGeminiMdFilename()
    );
    config2.setMemory(memory);
  } catch (error) {
    logger.warn("Failed to load hierarchical memory:", error);
  }
  return config2;
}
function loadEnvFiles() {
  const envFiles = [".env", ".env.local"];
  for (const envFile of envFiles) {
    const envPath = path2.join(process2.cwd(), envFile);
    if (fs2.existsSync(envPath)) {
      dotenv.config({ path: envPath });
      logger.debug(`Loaded environment variables from ${envFile}`);
    }
  }
  const homeEnvPath = path2.join(os2.homedir(), ".arien", ".env");
  if (fs2.existsSync(homeEnvPath)) {
    dotenv.config({ path: homeEnvPath });
    logger.debug("Loaded environment variables from ~/.arien/.env");
  }
}
function applySettings(config2, settings) {
  if (settings.model) {
    config2.setModel(settings.model);
  }
  if (settings.embeddingModel) {
    config2.setEmbeddingModel(settings.embeddingModel);
  }
  if (settings.debugMode !== void 0) {
    config2.setDebugMode(settings.debugMode);
  }
  if (settings.includeAllFiles !== void 0) {
    config2.setIncludeAllFiles(settings.includeAllFiles);
  }
  if (settings.showMemoryUsage !== void 0) {
    config2.setShowMemoryUsage(settings.showMemoryUsage);
  }
  if (settings.approvalMode) {
    config2.setApprovalMode(settings.approvalMode);
  }
  if (settings.telemetryEnabled !== void 0) {
    config2.setTelemetryEnabled(settings.telemetryEnabled);
  }
  if (settings.checkpointingEnabled !== void 0) {
    config2.setCheckpointingEnabled(settings.checkpointingEnabled);
  }
  if (settings.telemetryTarget) {
    config2.setTelemetryTarget(settings.telemetryTarget);
  }
  if (settings.telemetryOtlpEndpoint) {
    config2.setTelemetryOtlpEndpoint(settings.telemetryOtlpEndpoint);
  }
  if (settings.telemetryLogPrompts !== void 0) {
    config2.setTelemetryLogPrompts(settings.telemetryLogPrompts);
  }
  if (settings.excludeTools) {
    config2.setExcludeTools(settings.excludeTools);
  }
  if (settings.includeTools) {
    config2.setIncludeTools(settings.includeTools);
  }
  if (settings.geminiMdFilename) {
    setServerGeminiMdFilename(settings.geminiMdFilename);
  }
}
function applyExtensions(config2, extensions) {
  for (const extension of extensions) {
    try {
      extension.configure?.(config2);
      logger.debug(`Applied extension: ${extension.name}`);
    } catch (error) {
      logger.error(`Failed to apply extension ${extension.name}:`, error);
    }
  }
}

// packages/cli/src/utils/readStdin.ts
async function readStdin() {
  return new Promise((resolve, reject) => {
    let data = "";
    process.stdin.setEncoding("utf8");
    const onReadable = () => {
      let chunk;
      while ((chunk = process.stdin.read()) !== null) {
        data += chunk;
      }
    };
    const onEnd = () => {
      cleanup();
      resolve(data);
    };
    const onError = (err) => {
      cleanup();
      reject(err);
    };
    const cleanup = () => {
      process.stdin.removeListener("readable", onReadable);
      process.stdin.removeListener("end", onEnd);
      process.stdin.removeListener("error", onError);
    };
    process.stdin.on("readable", onReadable);
    process.stdin.on("end", onEnd);
    process.stdin.on("error", onError);
  });
}

// packages/cli/src/arien.tsx
import { basename } from "node:path";
import v8 from "node:v8";
import os6 from "node:os";
import { spawn as spawn2 } from "node:child_process";

// packages/cli/src/utils/sandbox.ts
import { exec, execSync, spawn } from "node:child_process";
import os3 from "node:os";
import path4 from "node:path";
import fs4 from "node:fs";
import { readFile } from "node:fs/promises";

// packages/cli/src/config/settings.ts
import * as fs3 from "fs";
import * as path3 from "path";
import { homedir as homedir2 } from "os";
import {
  getErrorMessage,
  AuthType,
  ApprovalMode as ApprovalMode2,
  TelemetryTarget as TelemetryTarget2
} from "@arien/arien-cli-core";
import stripJsonComments from "strip-json-comments";

// packages/cli/src/ui/themes/theme.ts
var lightTheme = {
  type: "light",
  Background: "#FAFAFA",
  Foreground: "#3C3C43",
  LightBlue: "#89BDCD",
  AccentBlue: "#3B82F6",
  AccentPurple: "#8B5CF6",
  AccentCyan: "#06B6D4",
  AccentGreen: "#3CA84B",
  AccentYellow: "#D5A40A",
  AccentRed: "#DD4C4C",
  Comment: "#008000",
  Gray: "#B7BECC",
  GradientColors: ["#4796E4", "#847ACE", "#C3677F"]
};
var darkTheme = {
  type: "dark",
  Background: "#1E1E2E",
  Foreground: "#CDD6F4",
  LightBlue: "#ADD8E6",
  AccentBlue: "#89B4FA",
  AccentPurple: "#CBA6F7",
  AccentCyan: "#89DCEB",
  AccentGreen: "#A6E3A1",
  AccentYellow: "#F9E2AF",
  AccentRed: "#F38BA8",
  Comment: "#6C7086",
  Gray: "#6C7086",
  GradientColors: ["#4796E4", "#847ACE", "#C3677F"]
};
var ansiTheme = {
  type: "ansi",
  Background: "black",
  Foreground: "white",
  LightBlue: "blue",
  AccentBlue: "blue",
  AccentPurple: "magenta",
  AccentCyan: "cyan",
  AccentGreen: "green",
  AccentYellow: "yellow",
  AccentRed: "red",
  Comment: "gray",
  Gray: "gray"
};
var Theme = class _Theme {
  constructor(name, type, rawMappings, colors) {
    this.name = name;
    this.type = type;
    this.colors = colors;
    this._colorMap = Object.freeze(this._buildColorMap(rawMappings));
    const rawDefaultColor = rawMappings["hljs"]?.color;
    this.defaultColor = (rawDefaultColor ? _Theme._resolveColor(rawDefaultColor) : void 0) ?? colors.Foreground;
  }
  defaultColor;
  _colorMap;
  // Define the set of Ink's named colors for quick lookup
  static inkSupportedNames = /* @__PURE__ */ new Set([
    "black",
    "red",
    "green",
    "yellow",
    "blue",
    "cyan",
    "magenta",
    "white",
    "gray",
    "grey",
    "blackbright",
    "redbright",
    "greenbright",
    "yellowbright",
    "bluebright",
    "cyanbright",
    "magentabright",
    "whitebright"
  ]);
  getInkColor(hljsClass) {
    return this._colorMap[hljsClass];
  }
  static _resolveColor(colorValue) {
    const lowerColor = colorValue.toLowerCase();
    if (lowerColor.startsWith("#")) {
      return lowerColor;
    } else if (_Theme.inkSupportedNames.has(lowerColor)) {
      return lowerColor;
    }
    console.warn(
      `[Theme] Could not resolve color "${colorValue}" to an Ink-compatible format.`
    );
    return void 0;
  }
  _buildColorMap(hljsTheme) {
    const inkTheme = {};
    for (const key in hljsTheme) {
      if (!key.startsWith("hljs-") && key !== "hljs") {
        continue;
      }
      const style = hljsTheme[key];
      if (style?.color) {
        const resolvedColor = _Theme._resolveColor(style.color);
        if (resolvedColor !== void 0) {
          inkTheme[key] = resolvedColor;
        }
      }
    }
    return inkTheme;
  }
};

// packages/cli/src/ui/themes/default-light.ts
var defaultLightSyntax = {
  "hljs": {
    color: lightTheme.Foreground
  },
  "hljs-keyword": {
    color: lightTheme.AccentPurple
  },
  "hljs-string": {
    color: lightTheme.AccentGreen
  },
  "hljs-number": {
    color: lightTheme.AccentYellow
  },
  "hljs-comment": {
    color: lightTheme.Comment
  },
  "hljs-function": {
    color: lightTheme.AccentBlue
  },
  "hljs-variable": {
    color: lightTheme.AccentCyan
  },
  "hljs-type": {
    color: lightTheme.AccentPurple
  },
  "hljs-class": {
    color: lightTheme.AccentBlue
  },
  "hljs-operator": {
    color: lightTheme.AccentRed
  },
  "hljs-punctuation": {
    color: lightTheme.Gray
  },
  "hljs-property": {
    color: lightTheme.AccentCyan
  },
  "hljs-method": {
    color: lightTheme.AccentBlue
  },
  "hljs-constant": {
    color: lightTheme.AccentYellow
  },
  "hljs-boolean": {
    color: lightTheme.AccentRed
  },
  "hljs-null": {
    color: lightTheme.AccentRed
  },
  "hljs-undefined": {
    color: lightTheme.AccentRed
  },
  "hljs-tag": {
    color: lightTheme.AccentRed
  },
  "hljs-attribute": {
    color: lightTheme.AccentYellow
  },
  "hljs-value": {
    color: lightTheme.AccentGreen
  },
  "hljs-selector": {
    color: lightTheme.AccentPurple
  },
  "hljs-rule": {
    color: lightTheme.AccentBlue
  },
  "hljs-important": {
    color: lightTheme.AccentRed
  },
  "hljs-emphasis": {
    color: lightTheme.AccentYellow
  },
  "hljs-strong": {
    color: lightTheme.AccentRed
  },
  "hljs-title": {
    color: lightTheme.AccentBlue
  },
  "hljs-section": {
    color: lightTheme.AccentPurple
  },
  "hljs-quote": {
    color: lightTheme.Comment
  },
  "hljs-name": {
    color: lightTheme.AccentCyan
  },
  "hljs-built_in": {
    color: lightTheme.AccentPurple
  },
  "hljs-literal": {
    color: lightTheme.AccentYellow
  },
  "hljs-params": {
    color: lightTheme.Foreground
  },
  "hljs-meta": {
    color: lightTheme.Comment
  },
  "hljs-link": {
    color: lightTheme.AccentBlue
  },
  "hljs-symbol": {
    color: lightTheme.AccentCyan
  },
  "hljs-bullet": {
    color: lightTheme.AccentRed
  },
  "hljs-code": {
    color: lightTheme.AccentGreen
  },
  "hljs-formula": {
    color: lightTheme.AccentYellow
  },
  "hljs-doctag": {
    color: lightTheme.Comment
  },
  "hljs-deletion": {
    color: lightTheme.AccentRed
  },
  "hljs-addition": {
    color: lightTheme.AccentGreen
  }
};
var DefaultLight = new Theme(
  "DefaultLight",
  "light",
  defaultLightSyntax,
  lightTheme
);

// packages/cli/src/ui/themes/default.ts
var defaultDarkSyntax = {
  "hljs": {
    color: darkTheme.Foreground
  },
  "hljs-keyword": {
    color: darkTheme.AccentPurple
  },
  "hljs-string": {
    color: darkTheme.AccentGreen
  },
  "hljs-number": {
    color: darkTheme.AccentYellow
  },
  "hljs-comment": {
    color: darkTheme.Comment
  },
  "hljs-function": {
    color: darkTheme.AccentBlue
  },
  "hljs-variable": {
    color: darkTheme.AccentCyan
  },
  "hljs-type": {
    color: darkTheme.AccentPurple
  },
  "hljs-class": {
    color: darkTheme.AccentBlue
  },
  "hljs-operator": {
    color: darkTheme.AccentRed
  },
  "hljs-punctuation": {
    color: darkTheme.Gray
  },
  "hljs-property": {
    color: darkTheme.AccentCyan
  },
  "hljs-method": {
    color: darkTheme.AccentBlue
  },
  "hljs-constant": {
    color: darkTheme.AccentYellow
  },
  "hljs-boolean": {
    color: darkTheme.AccentRed
  },
  "hljs-null": {
    color: darkTheme.AccentRed
  },
  "hljs-undefined": {
    color: darkTheme.AccentRed
  },
  "hljs-tag": {
    color: darkTheme.AccentRed
  },
  "hljs-attribute": {
    color: darkTheme.AccentYellow
  },
  "hljs-value": {
    color: darkTheme.AccentGreen
  },
  "hljs-selector": {
    color: darkTheme.AccentPurple
  },
  "hljs-rule": {
    color: darkTheme.AccentBlue
  },
  "hljs-important": {
    color: darkTheme.AccentRed
  },
  "hljs-emphasis": {
    color: darkTheme.AccentYellow
  },
  "hljs-strong": {
    color: darkTheme.AccentRed
  },
  "hljs-title": {
    color: darkTheme.AccentBlue
  },
  "hljs-section": {
    color: darkTheme.AccentPurple
  },
  "hljs-quote": {
    color: darkTheme.Comment
  },
  "hljs-name": {
    color: darkTheme.AccentCyan
  },
  "hljs-built_in": {
    color: darkTheme.AccentPurple
  },
  "hljs-literal": {
    color: darkTheme.AccentYellow
  },
  "hljs-params": {
    color: darkTheme.Foreground
  },
  "hljs-meta": {
    color: darkTheme.Comment
  },
  "hljs-link": {
    color: darkTheme.AccentBlue
  },
  "hljs-symbol": {
    color: darkTheme.AccentCyan
  },
  "hljs-bullet": {
    color: darkTheme.AccentRed
  },
  "hljs-code": {
    color: darkTheme.AccentGreen
  },
  "hljs-formula": {
    color: darkTheme.AccentYellow
  },
  "hljs-doctag": {
    color: darkTheme.Comment
  },
  "hljs-deletion": {
    color: darkTheme.AccentRed
  },
  "hljs-addition": {
    color: darkTheme.AccentGreen
  }
};
var DefaultDark = new Theme(
  "DefaultDark",
  "dark",
  defaultDarkSyntax,
  darkTheme
);

// packages/cli/src/config/settings.ts
var SETTINGS_DIRECTORY_NAME = ".arien";
var USER_SETTINGS_DIR = path3.join(homedir2(), SETTINGS_DIRECTORY_NAME);
var USER_SETTINGS_PATH = path3.join(USER_SETTINGS_DIR, "settings.json");
var LoadedSettings = class {
  constructor(user, workspace, errors) {
    this.user = user;
    this.workspace = workspace;
    this.errors = errors;
    this._merged = this.computeMergedSettings();
  }
  user;
  workspace;
  errors;
  _merged;
  get merged() {
    return this._merged;
  }
  computeMergedSettings() {
    return {
      ...this.user.settings,
      ...this.workspace.settings
    };
  }
  setValue(scope, key, value) {
    const targetFile = scope === "User" /* User */ ? this.user : this.workspace;
    targetFile.settings[key] = value;
    try {
      fs3.mkdirSync(path3.dirname(targetFile.path), { recursive: true });
      fs3.writeFileSync(
        targetFile.path,
        JSON.stringify(targetFile.settings, null, 2)
      );
      this._merged = this.computeMergedSettings();
    } catch (error) {
      console.error(`Failed to save settings to ${targetFile.path}:`, error);
    }
  }
  getValue(key) {
    return this.merged[key];
  }
};
function loadSettings(workspaceRoot) {
  const errors = [];
  const userSettings = loadSettingsFile(USER_SETTINGS_PATH, errors);
  const workspaceSettingsPath = path3.join(workspaceRoot, SETTINGS_DIRECTORY_NAME, "settings.json");
  const workspaceSettings = loadSettingsFile(workspaceSettingsPath, errors);
  return new LoadedSettings(userSettings, workspaceSettings, errors);
}
function loadSettingsFile(filePath, errors) {
  const defaultSettings = {
    theme: DefaultDark.name,
    autoConfigureMaxOldSpaceSize: true,
    fileFiltering: {
      respectGitIgnore: true,
      enableRecursiveFileSearch: true
    },
    telemetry: {
      enabled: true
    },
    checkpointing: {
      enabled: true
    },
    accessibility: {
      disableLoadingPhrases: false
    }
  };
  if (!fs3.existsSync(filePath)) {
    return {
      settings: defaultSettings,
      path: filePath
    };
  }
  try {
    const content = fs3.readFileSync(filePath, "utf8");
    const cleanContent = stripJsonComments(content);
    const parsed = JSON.parse(cleanContent);
    const settings = {
      ...defaultSettings,
      ...parsed
    };
    return {
      settings,
      path: filePath
    };
  } catch (error) {
    errors.push({
      message: getErrorMessage(error),
      path: filePath
    });
    return {
      settings: defaultSettings,
      path: filePath
    };
  }
}

// packages/cli/src/utils/sandbox.ts
import { promisify } from "util";
var execAsync = promisify(exec);
function getContainerPath(hostPath) {
  if (os3.platform() !== "win32") {
    return hostPath;
  }
  const withForwardSlashes = hostPath.replace(/\\/g, "/");
  const match = withForwardSlashes.match(/^([A-Z]):\/(.*)/i);
  if (match) {
    return `/${match[1].toLowerCase()}/${match[2]}`;
  }
  return hostPath;
}
var BUILTIN_SEATBELT_PROFILES = [
  "permissive-open",
  "permissive-closed",
  "permissive-proxied",
  "restrictive-open",
  "restrictive-closed",
  "restrictive-proxied"
];
async function shouldUseCurrentUserInSandbox() {
  const envVar = process.env.SANDBOX_SET_UID_GID?.toLowerCase().trim();
  if (envVar === "1" || envVar === "true") {
    return true;
  }
  if (envVar === "0" || envVar === "false") {
    return false;
  }
  if (os3.platform() === "linux") {
    try {
      const osReleaseContent = await readFile("/etc/os-release", "utf8");
      if (osReleaseContent.includes("ID=debian") || osReleaseContent.includes("ID=ubuntu") || osReleaseContent.match(/^ID_LIKE=.*debian.*/m) || // Covers derivatives
      osReleaseContent.match(/^ID_LIKE=.*ubuntu.*/m)) {
        console.error(
          "INFO: Defaulting to use current user UID/GID for Debian/Ubuntu-based Linux."
        );
        return true;
      }
    } catch (_err) {
      console.warn(
        "Warning: Could not read /etc/os-release to auto-detect Debian/Ubuntu for UID/GID default."
      );
    }
  }
  return false;
}
async function start_sandbox(sandboxConfig, memoryArgs = []) {
  const { command, image } = sandboxConfig;
  if (command === "sandbox-exec") {
    await startSeatbeltSandbox(image, memoryArgs);
  } else if (command === "docker" || command === "podman") {
    await startContainerSandbox(command, image, memoryArgs);
  } else {
    throw new Error(`Unsupported sandbox command: ${command}`);
  }
}
async function startSeatbeltSandbox(profile, memoryArgs) {
  const profilePath = getSeatbeltProfilePath(profile);
  if (!profilePath) {
    throw new Error(`Unknown seatbelt profile: ${profile}`);
  }
  const args = [
    "sandbox-exec",
    "-f",
    profilePath,
    process.execPath,
    ...memoryArgs,
    ...process.argv.slice(1)
  ];
  const env = { ...process.env, SANDBOX: "true" };
  const child = spawn(args[0], args.slice(1), {
    stdio: "inherit",
    env
  });
  await new Promise((resolve, reject) => {
    child.on("close", (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Sandbox process exited with code ${code}`));
      }
    });
    child.on("error", reject);
  });
}
async function startContainerSandbox(command, image, memoryArgs) {
  const workspaceRoot = process.cwd();
  const containerWorkspaceRoot = getContainerPath(workspaceRoot);
  const containerUserSettingsDir = getContainerPath(USER_SETTINGS_DIR);
  const args = [
    command,
    "run",
    "--rm",
    "-it",
    "--network",
    "host",
    "-v",
    `${workspaceRoot}:${containerWorkspaceRoot}`,
    "-v",
    `${USER_SETTINGS_DIR}:${containerUserSettingsDir}`,
    "-w",
    containerWorkspaceRoot
  ];
  if (await shouldUseCurrentUserInSandbox()) {
    const uid = process.getuid?.() || 0;
    const gid = process.getgid?.() || 0;
    args.push("--user", `${uid}:${gid}`);
  }
  const env = { ...process.env, SANDBOX: "true" };
  for (const [key, value] of Object.entries(env)) {
    if (value !== void 0) {
      args.push("-e", `${key}=${value}`);
    }
  }
  args.push(image, "node", ...memoryArgs, ...process.argv.slice(1));
  const child = spawn(args[0], args.slice(1), {
    stdio: "inherit"
  });
  await new Promise((resolve, reject) => {
    child.on("close", (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Container process exited with code ${code}`));
      }
    });
    child.on("error", reject);
  });
}
function getSeatbeltProfilePath(profile) {
  if (BUILTIN_SEATBELT_PROFILES.includes(profile)) {
    return path4.join(import.meta.dirname, `sandbox-macos-${profile}.sb`);
  }
  if (fs4.existsSync(profile)) {
    return profile;
  }
  return null;
}

// packages/cli/src/ui/themes/ansi.ts
var ansiSyntax = {
  "hljs": {
    color: "white"
  },
  "hljs-keyword": {
    color: "magenta"
  },
  "hljs-string": {
    color: "green"
  },
  "hljs-number": {
    color: "yellow"
  },
  "hljs-comment": {
    color: "gray"
  },
  "hljs-function": {
    color: "blue"
  },
  "hljs-variable": {
    color: "cyan"
  },
  "hljs-type": {
    color: "magenta"
  },
  "hljs-class": {
    color: "blue"
  },
  "hljs-operator": {
    color: "red"
  },
  "hljs-punctuation": {
    color: "gray"
  },
  "hljs-property": {
    color: "cyan"
  },
  "hljs-method": {
    color: "blue"
  },
  "hljs-constant": {
    color: "yellow"
  },
  "hljs-boolean": {
    color: "red"
  },
  "hljs-null": {
    color: "red"
  },
  "hljs-undefined": {
    color: "red"
  },
  "hljs-tag": {
    color: "red"
  },
  "hljs-attribute": {
    color: "yellow"
  },
  "hljs-value": {
    color: "green"
  },
  "hljs-selector": {
    color: "magenta"
  },
  "hljs-rule": {
    color: "blue"
  },
  "hljs-important": {
    color: "red"
  },
  "hljs-emphasis": {
    color: "yellow"
  },
  "hljs-strong": {
    color: "red"
  },
  "hljs-title": {
    color: "blue"
  },
  "hljs-section": {
    color: "magenta"
  },
  "hljs-quote": {
    color: "gray"
  },
  "hljs-name": {
    color: "cyan"
  },
  "hljs-built_in": {
    color: "magenta"
  },
  "hljs-literal": {
    color: "yellow"
  },
  "hljs-params": {
    color: "white"
  },
  "hljs-meta": {
    color: "gray"
  },
  "hljs-link": {
    color: "blue"
  },
  "hljs-symbol": {
    color: "cyan"
  },
  "hljs-bullet": {
    color: "red"
  },
  "hljs-code": {
    color: "green"
  },
  "hljs-formula": {
    color: "yellow"
  },
  "hljs-doctag": {
    color: "gray"
  },
  "hljs-deletion": {
    color: "red"
  },
  "hljs-addition": {
    color: "green"
  }
};
var ANSI = new Theme(
  "ANSI",
  "ansi",
  ansiSyntax,
  ansiTheme
);

// packages/cli/src/ui/themes/ansi-light.ts
var ansiLightSyntax = {
  "hljs": {
    color: "black"
  },
  "hljs-keyword": {
    color: "magenta"
  },
  "hljs-string": {
    color: "green"
  },
  "hljs-number": {
    color: "blue"
  },
  "hljs-comment": {
    color: "gray"
  },
  "hljs-function": {
    color: "blue"
  },
  "hljs-variable": {
    color: "cyan"
  },
  "hljs-type": {
    color: "magenta"
  },
  "hljs-class": {
    color: "blue"
  },
  "hljs-operator": {
    color: "red"
  },
  "hljs-punctuation": {
    color: "gray"
  },
  "hljs-property": {
    color: "cyan"
  },
  "hljs-method": {
    color: "blue"
  },
  "hljs-constant": {
    color: "blue"
  },
  "hljs-boolean": {
    color: "red"
  },
  "hljs-null": {
    color: "red"
  },
  "hljs-undefined": {
    color: "red"
  },
  "hljs-tag": {
    color: "red"
  },
  "hljs-attribute": {
    color: "blue"
  },
  "hljs-value": {
    color: "green"
  },
  "hljs-selector": {
    color: "magenta"
  },
  "hljs-rule": {
    color: "blue"
  },
  "hljs-important": {
    color: "red"
  },
  "hljs-emphasis": {
    color: "blue"
  },
  "hljs-strong": {
    color: "red"
  },
  "hljs-title": {
    color: "blue"
  },
  "hljs-section": {
    color: "magenta"
  },
  "hljs-quote": {
    color: "gray"
  },
  "hljs-name": {
    color: "cyan"
  },
  "hljs-built_in": {
    color: "magenta"
  },
  "hljs-literal": {
    color: "blue"
  },
  "hljs-params": {
    color: "black"
  },
  "hljs-meta": {
    color: "gray"
  },
  "hljs-link": {
    color: "blue"
  },
  "hljs-symbol": {
    color: "cyan"
  },
  "hljs-bullet": {
    color: "red"
  },
  "hljs-code": {
    color: "green"
  },
  "hljs-formula": {
    color: "blue"
  },
  "hljs-doctag": {
    color: "gray"
  },
  "hljs-deletion": {
    color: "red"
  },
  "hljs-addition": {
    color: "green"
  }
};
var ansiLightTheme = {
  ...ansiTheme,
  type: "light",
  Background: "white",
  Foreground: "black"
};
var ANSILight = new Theme(
  "ANSILight",
  "light",
  ansiLightSyntax,
  ansiLightTheme
);

// packages/cli/src/ui/themes/no-color.ts
var noColorTheme = {
  type: "ansi",
  Background: "",
  Foreground: "",
  LightBlue: "",
  AccentBlue: "",
  AccentPurple: "",
  AccentCyan: "",
  AccentGreen: "",
  AccentYellow: "",
  AccentRed: "",
  Comment: "",
  Gray: ""
};
var noColorSyntax = {
  "hljs": {
    color: ""
  },
  "hljs-keyword": {
    color: ""
  },
  "hljs-string": {
    color: ""
  },
  "hljs-number": {
    color: ""
  },
  "hljs-comment": {
    color: ""
  },
  "hljs-function": {
    color: ""
  },
  "hljs-variable": {
    color: ""
  },
  "hljs-type": {
    color: ""
  },
  "hljs-class": {
    color: ""
  },
  "hljs-operator": {
    color: ""
  },
  "hljs-punctuation": {
    color: ""
  },
  "hljs-property": {
    color: ""
  },
  "hljs-method": {
    color: ""
  },
  "hljs-constant": {
    color: ""
  },
  "hljs-boolean": {
    color: ""
  },
  "hljs-null": {
    color: ""
  },
  "hljs-undefined": {
    color: ""
  },
  "hljs-tag": {
    color: ""
  },
  "hljs-attribute": {
    color: ""
  },
  "hljs-value": {
    color: ""
  },
  "hljs-selector": {
    color: ""
  },
  "hljs-rule": {
    color: ""
  },
  "hljs-important": {
    color: ""
  },
  "hljs-emphasis": {
    color: ""
  },
  "hljs-strong": {
    color: ""
  },
  "hljs-title": {
    color: ""
  },
  "hljs-section": {
    color: ""
  },
  "hljs-quote": {
    color: ""
  },
  "hljs-name": {
    color: ""
  },
  "hljs-built_in": {
    color: ""
  },
  "hljs-literal": {
    color: ""
  },
  "hljs-params": {
    color: ""
  },
  "hljs-meta": {
    color: ""
  },
  "hljs-link": {
    color: ""
  },
  "hljs-symbol": {
    color: ""
  },
  "hljs-bullet": {
    color: ""
  },
  "hljs-code": {
    color: ""
  },
  "hljs-formula": {
    color: ""
  },
  "hljs-doctag": {
    color: ""
  },
  "hljs-deletion": {
    color: ""
  },
  "hljs-addition": {
    color: ""
  }
};
var NoColorTheme = new Theme(
  "NoColor",
  "ansi",
  noColorSyntax,
  noColorTheme
);

// packages/cli/src/ui/themes/theme-manager.ts
import process3 from "node:process";
var DEFAULT_THEME = DefaultDark;
var ThemeManager = class {
  availableThemes;
  activeTheme;
  constructor() {
    this.availableThemes = [
      DefaultDark,
      DefaultLight,
      ANSI,
      ANSILight,
      NoColorTheme
    ];
    this.activeTheme = DEFAULT_THEME;
  }
  /**
   * Returns a list of available theme names.
   */
  getAvailableThemes() {
    const sortedThemes = [...this.availableThemes].sort((a, b) => {
      const typeOrder = (type) => {
        switch (type) {
          case "dark":
            return 1;
          case "light":
            return 2;
          default:
            return 3;
        }
      };
      const typeComparison = typeOrder(a.type) - typeOrder(b.type);
      if (typeComparison !== 0) {
        return typeComparison;
      }
      return a.name.localeCompare(b.name);
    });
    return sortedThemes.map((theme) => ({
      name: theme.name,
      type: theme.type
    }));
  }
  /**
   * Sets the active theme.
   * @param themeName The name of the theme to activate.
   * @returns True if the theme was successfully set, false otherwise.
   */
  setActiveTheme(themeName) {
    const foundTheme = this.findThemeByName(themeName);
    if (foundTheme) {
      this.activeTheme = foundTheme;
      return true;
    } else {
      if (themeName === void 0) {
        this.activeTheme = DEFAULT_THEME;
        return true;
      }
      return false;
    }
  }
  /**
   * Gets the currently active theme.
   */
  getActiveTheme() {
    return this.activeTheme;
  }
  /**
   * Gets the name of the currently active theme.
   */
  getActiveThemeName() {
    return this.activeTheme.name;
  }
  /**
   * Finds a theme by name.
   * @param themeName The name of the theme to find.
   * @returns The theme if found, undefined otherwise.
   */
  findThemeByName(themeName) {
    if (!themeName) {
      return void 0;
    }
    return this.availableThemes.find(
      (theme) => theme.name.toLowerCase() === themeName.toLowerCase()
    );
  }
  /**
   * Determines if NO_COLOR environment variable is set and should use no-color theme.
   */
  shouldUseNoColor() {
    return process3.env.NO_COLOR !== void 0 && process3.env.NO_COLOR !== "";
  }
  /**
   * Auto-detects the appropriate theme based on environment.
   */
  autoDetectTheme() {
    if (this.shouldUseNoColor()) {
      return "NoColor";
    }
    const colorTerm = process3.env.COLORTERM;
    const term = process3.env.TERM;
    if (colorTerm === "truecolor" || term?.includes("256color")) {
      return "DefaultDark";
    } else if (term?.includes("color")) {
      return "ANSI";
    } else {
      return "NoColor";
    }
  }
  /**
   * Initializes the theme manager with user preferences.
   */
  initialize(userTheme) {
    let themeToUse = userTheme;
    if (!themeToUse) {
      themeToUse = this.autoDetectTheme();
    }
    if (!this.setActiveTheme(themeToUse)) {
      console.warn(`Theme "${themeToUse}" not found, using default theme.`);
      this.setActiveTheme(void 0);
    }
  }
  /**
   * Gets theme by type (light/dark/ansi).
   */
  getThemesByType(type) {
    return this.availableThemes.filter((theme) => theme.type === type);
  }
  /**
   * Switches between light and dark variants of the current theme.
   */
  toggleThemeVariant() {
    const currentType = this.activeTheme.type;
    const targetType = currentType === "dark" ? "light" : "dark";
    const variants = this.getThemesByType(targetType);
    if (variants.length > 0) {
      this.activeTheme = variants[0];
      return true;
    }
    return false;
  }
};
var themeManager = new ThemeManager();

// packages/cli/src/utils/startupWarnings.ts
import fs5 from "fs/promises";
import os4 from "os";
import { join as pathJoin } from "node:path";
import { getErrorMessage as getErrorMessage2 } from "@arien/arien-cli-core";
var warningsFilePath = pathJoin(os4.tmpdir(), "arien-cli-warnings.txt");
async function getStartupWarnings() {
  try {
    await fs5.access(warningsFilePath);
    const warningsContent = await fs5.readFile(warningsFilePath, "utf-8");
    const warnings = warningsContent.split("\n").filter((line) => line.trim() !== "");
    try {
      await fs5.unlink(warningsFilePath);
    } catch {
      warnings.push("Warning: Could not delete temporary warnings file.");
    }
    return warnings;
  } catch (err) {
    if (err instanceof Error && "code" in err && err.code === "ENOENT") {
      return [];
    }
    return [`Error checking/reading warnings file: ${getErrorMessage2(err)}`];
  }
}

// packages/cli/src/nonInteractiveCli.ts
import {
  executeToolCall,
  shutdownTelemetry,
  isTelemetrySdkInitialized
} from "@arien/arien-cli-core";

// packages/cli/src/ui/utils/errorParsing.ts
function parseError(error) {
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: error.stack,
      type: "error"
    };
  }
  if (typeof error === "string") {
    return {
      message: error,
      type: "error"
    };
  }
  if (typeof error === "object" && error !== null) {
    const obj = error;
    return {
      message: String(obj.message || obj.toString?.() || "Unknown error"),
      stack: typeof obj.stack === "string" ? obj.stack : void 0,
      code: typeof obj.code === "string" ? obj.code : void 0,
      type: "error",
      file: typeof obj.file === "string" ? obj.file : void 0,
      line: typeof obj.line === "number" ? obj.line : void 0,
      column: typeof obj.column === "number" ? obj.column : void 0
    };
  }
  return {
    message: "Unknown error occurred",
    type: "error"
  };
}
function formatError(error) {
  let formatted = error.message;
  if (error.file) {
    formatted += ` (${error.file}`;
    if (error.line) {
      formatted += `:${error.line}`;
      if (error.column) {
        formatted += `:${error.column}`;
      }
    }
    formatted += ")";
  }
  if (error.code) {
    formatted += ` [${error.code}]`;
  }
  return formatted;
}
function getErrorMessage3(error) {
  const parsed = parseError(error);
  return formatError(parsed);
}
function parseAndFormatApiError(error) {
  return getErrorMessage3(error);
}

// packages/cli/src/nonInteractiveCli.ts
function getResponseText(response) {
  if (response.candidates && response.candidates.length > 0) {
    const candidate = response.candidates[0];
    if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
      const thoughtPart = candidate.content.parts[0];
      if (thoughtPart?.thought) {
        return null;
      }
      return candidate.content.parts.filter((part) => part.text).map((part) => part.text).join("");
    }
  }
  return null;
}
async function runNonInteractive(config2, input) {
  process.stdout.on("error", (err) => {
    if (err.code === "EPIPE") {
      process.exit(0);
    }
  });
  const geminiClient = config2.getGeminiClient();
  const toolRegistry = await config2.getToolRegistry();
  const chat = await geminiClient.getChat();
  const abortController = new AbortController();
  let currentMessages = [{ role: "user", parts: [{ text: input }] }];
  try {
    while (true) {
      const functionCalls = [];
      const responseStream = await chat.sendMessageStream({
        message: currentMessages[0]?.parts || [],
        // Ensure parts are always provided
        config: {
          abortSignal: abortController.signal,
          tools: [
            { functionDeclarations: toolRegistry.getFunctionDeclarations() }
          ]
        }
      });
      for await (const resp of responseStream) {
        if (abortController.signal.aborted) {
          console.error("Operation cancelled.");
          return;
        }
        const textPart = getResponseText(resp);
        if (textPart) {
          process.stdout.write(textPart);
        }
        if (resp.functionCalls) {
          functionCalls.push(...resp.functionCalls);
        }
      }
      if (functionCalls.length > 0) {
        const toolResponseParts = [];
        for (const fc of functionCalls) {
          const callId = fc.id ?? `${fc.name}-${Date.now()}`;
          const requestInfo = {
            callId,
            name: fc.name,
            args: fc.args || {}
          };
          try {
            const result = await executeToolCall(
              requestInfo,
              toolRegistry,
              config2,
              abortController.signal
            );
            toolResponseParts.push({
              functionResponse: {
                name: fc.name,
                response: result
              }
            });
          } catch (error) {
            console.error(`Error executing tool ${fc.name}:`, error);
            toolResponseParts.push({
              functionResponse: {
                name: fc.name,
                response: {
                  error: error instanceof Error ? error.message : String(error)
                }
              }
            });
          }
        }
        currentMessages = [
          {
            role: "model",
            parts: functionCalls.map((fc) => ({
              functionCall: {
                name: fc.name,
                args: fc.args || {}
              }
            }))
          },
          {
            role: "user",
            parts: toolResponseParts
          }
        ];
      } else {
        break;
      }
    }
  } catch (error) {
    const errorMessage = parseAndFormatApiError(error);
    console.error("\nError:", errorMessage);
    process.exit(1);
  } finally {
    if (isTelemetrySdkInitialized()) {
      await shutdownTelemetry();
    }
  }
}

// packages/cli/src/config/extension.ts
import * as fs6 from "fs";
import * as path5 from "path";
import * as os5 from "os";
var EXTENSIONS_DIRECTORY_NAME = path5.join(".arien", "extensions");
var EXTENSIONS_CONFIG_FILENAME = "arien-extension.json";
function loadExtensions(workspaceDir) {
  const allExtensions = [
    ...loadExtensionsFromDir(workspaceDir),
    ...loadExtensionsFromDir(os5.homedir())
  ];
  const uniqueExtensions = [];
  const seenNames = /* @__PURE__ */ new Set();
  for (const extension of allExtensions) {
    if (!seenNames.has(extension.config.name)) {
      console.log(
        `Loading extension: ${extension.config.name} (version: ${extension.config.version})`
      );
      uniqueExtensions.push(extension);
      seenNames.add(extension.config.name);
    }
  }
  return uniqueExtensions;
}
function loadExtensionsFromDir(dir) {
  const extensionsDir = path5.join(dir, EXTENSIONS_DIRECTORY_NAME);
  if (!fs6.existsSync(extensionsDir)) {
    return [];
  }
  const extensions = [];
  for (const subdir of fs6.readdirSync(extensionsDir)) {
    const extensionDir = path5.join(extensionsDir, subdir);
    const extension = loadExtension(extensionDir);
    if (extension != null) {
      extensions.push(extension);
    }
  }
  return extensions;
}
function loadExtension(extensionDir) {
  if (!fs6.statSync(extensionDir).isDirectory()) {
    console.error(
      `Warning: unexpected file ${extensionDir} in extensions directory.`
    );
    return null;
  }
  const configFilePath = path5.join(extensionDir, EXTENSIONS_CONFIG_FILENAME);
  if (!fs6.existsSync(configFilePath)) {
    console.error(
      `Warning: extension directory ${extensionDir} does not contain a config file ${configFilePath}.`
    );
    return null;
  }
  try {
    const configContent = fs6.readFileSync(configFilePath, "utf-8");
    const config2 = JSON.parse(configContent);
    if (!config2.name || !config2.version) {
      console.error(
        `Invalid extension config in ${configFilePath}: missing name or version.`
      );
      return null;
    }
    const contextFiles = getContextFileNames(config2).map((contextFileName) => path5.join(extensionDir, contextFileName)).filter((contextFilePath) => fs6.existsSync(contextFilePath));
    let configure;
    const mainModulePath = path5.join(extensionDir, "index.js");
    if (fs6.existsSync(mainModulePath)) {
      try {
        const extensionModule = __require(mainModulePath);
        if (typeof extensionModule.configure === "function") {
          configure = extensionModule.configure;
        }
      } catch (error) {
        console.error(
          `Warning: failed to load extension module ${mainModulePath}:`,
          error
        );
      }
    }
    return {
      name: config2.name,
      version: config2.version,
      config: config2,
      contextFiles,
      configure
    };
  } catch (error) {
    console.error(`Error loading extension config from ${configFilePath}:`, error);
    return null;
  }
}
function getContextFileNames(config2) {
  if (!config2.contextFileName) {
    return [];
  }
  if (Array.isArray(config2.contextFileName)) {
    return config2.contextFileName;
  }
  return [config2.contextFileName];
}

// packages/cli/src/utils/cleanup.ts
import { promises as fs7 } from "fs";
import { join as join5 } from "path";
import { getProjectTempDir } from "@arien/arien-cli-core";
async function cleanupCheckpoints() {
  const tempDir = getProjectTempDir(process.cwd());
  const checkpointsDir = join5(tempDir, "checkpoints");
  try {
    await fs7.rm(checkpointsDir, { recursive: true, force: true });
  } catch {
  }
}

// packages/cli/src/arien.tsx
import {
  ApprovalMode as ApprovalMode3,
  EditTool,
  ShellTool,
  WriteFileTool,
  sessionId,
  logUserPrompt,
  AuthType as AuthType3
} from "@arien/arien-cli-core";

// packages/cli/src/config/auth.ts
import { AuthType as AuthType2 } from "@arien/arien-cli-core";
var validateAuthMethod = (authMethod) => {
  if (authMethod === AuthType2.LOGIN_WITH_GOOGLE) {
    return null;
  }
  if (authMethod === AuthType2.USE_GEMINI) {
    if (!process.env.GEMINI_API_KEY) {
      return "GEMINI_API_KEY environment variable not found. Add that to your .env and try again, no reload needed!";
    }
    return null;
  }
  if (authMethod === AuthType2.USE_VERTEX_AI) {
    const hasVertexProjectLocationConfig = !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;
    const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
    if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {
      return "Must specify GOOGLE_GENAI_USE_VERTEXAI=true and either:\n\u2022 GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\n\u2022 GOOGLE_API_KEY environment variable (if using express mode).\nUpdate your .env and try again, no reload needed!";
    }
    return null;
  }
  return "Invalid auth method selected.";
};

// packages/cli/src/ui/components/shared/MaxSizedBox.tsx
import React3, { Fragment, useEffect, useId } from "react";
import { Box as Box2, Text as Text2 } from "ink";

// packages/cli/src/ui/contexts/OverflowContext.tsx
import { createContext, useContext, useState as useState2 } from "react";
import { jsx as jsx2 } from "react/jsx-runtime";
var OverflowContext = createContext(void 0);
var OverflowActionsContext = createContext(void 0);

// packages/cli/src/ui/components/shared/MaxSizedBox.tsx
import { jsx as jsx3, jsxs as jsxs2 } from "react/jsx-runtime";
var enableDebugLog = false;
function setMaxSizedBoxDebugging(value) {
  enableDebugLog = value;
}

// packages/cli/src/arien.tsx
import { jsx as jsx4 } from "react/jsx-runtime";
function getNodeMemoryArgs(config2) {
  const totalMemoryMB = os6.totalmem() / (1024 * 1024);
  const heapStats = v8.getHeapStatistics();
  const currentMaxOldSpaceSizeMb = Math.floor(
    heapStats.heap_size_limit / 1024 / 1024
  );
  const targetMaxOldSpaceSizeInMB = Math.floor(totalMemoryMB * 0.5);
  if (config2.getDebugMode()) {
    console.debug(
      `Current heap size ${currentMaxOldSpaceSizeMb.toFixed(2)} MB`
    );
  }
  if (process.env.ARIEN_CLI_NO_RELAUNCH) {
    return [];
  }
  if (targetMaxOldSpaceSizeInMB > currentMaxOldSpaceSizeMb) {
    if (config2.getDebugMode()) {
      console.debug(
        `Need to relaunch with more memory: ${targetMaxOldSpaceSizeInMB.toFixed(2)} MB`
      );
    }
    return [`--max-old-space-size=${targetMaxOldSpaceSizeInMB}`];
  }
  return [];
}
async function relaunchWithAdditionalArgs(additionalArgs) {
  const nodeArgs = [...additionalArgs, ...process.argv.slice(1)];
  const newEnv = { ...process.env, ARIEN_CLI_NO_RELAUNCH: "true" };
  const child = spawn2(process.execPath, nodeArgs, {
    stdio: "inherit",
    env: newEnv
  });
  await new Promise((resolve) => child.on("close", resolve));
  process.exit(0);
}
async function main() {
  const workspaceRoot = process.cwd();
  const settings = loadSettings(workspaceRoot);
  await cleanupCheckpoints();
  if (settings.errors.length > 0) {
    for (const error of settings.errors) {
      let errorMessage = `Error in ${error.path}: ${error.message}`;
      if (!process.env.NO_COLOR) {
        errorMessage = `\x1B[31m${errorMessage}\x1B[0m`;
      }
      console.error(errorMessage);
      console.error(`Please fix ${error.path} and try again.`);
    }
    process.exit(1);
  }
  const extensions = loadExtensions(workspaceRoot);
  const config2 = await loadCliConfig(settings.merged, extensions, sessionId);
  if (!settings.merged.selectedAuthType && process.env.GEMINI_API_KEY) {
    settings.setValue(
      "User" /* User */,
      "selectedAuthType",
      AuthType3.USE_GEMINI
    );
  }
  setMaxSizedBoxDebugging(config2.getDebugMode());
  config2.getFileService();
  if (config2.getCheckpointingEnabled()) {
    try {
      await config2.getGitService();
    } catch {
    }
  }
  if (settings.merged.theme) {
    if (!themeManager.setActiveTheme(settings.merged.theme)) {
      console.warn(`Warning: Theme "${settings.merged.theme}" not found.`);
    }
  }
  const memoryArgs = settings.merged.autoConfigureMaxOldSpaceSize ? getNodeMemoryArgs(config2) : [];
  if (!process.env.SANDBOX) {
    const sandboxConfig = config2.getSandbox();
    if (sandboxConfig) {
      if (settings.merged.selectedAuthType) {
        try {
          const err = validateAuthMethod(settings.merged.selectedAuthType);
          if (err) {
            throw new Error(err);
          }
          await config2.refreshAuth(settings.merged.selectedAuthType);
        } catch (err) {
          console.error("Error authenticating:", err);
          process.exit(1);
        }
      }
      await start_sandbox(sandboxConfig, memoryArgs);
      process.exit(0);
    } else {
      if (memoryArgs.length > 0) {
        await relaunchWithAdditionalArgs(memoryArgs);
        process.exit(0);
      }
    }
  }
  let input = config2.getQuestion();
  const startupWarnings = await getStartupWarnings();
  if (process.stdin.isTTY && input?.length === 0) {
    setWindowTitle(basename(workspaceRoot), settings);
    render(
      /* @__PURE__ */ jsx4(React4.StrictMode, { children: /* @__PURE__ */ jsx4(
        AppWrapper,
        {
          config: config2,
          settings,
          startupWarnings
        }
      ) }),
      { exitOnCtrlC: false }
    );
    return;
  }
  if (!process.stdin.isTTY) {
    input += await readStdin();
  }
  if (!input) {
    console.error("No input provided via stdin.");
    process.exit(1);
  }
  logUserPrompt(config2, {
    "event.name": "user_prompt",
    "event.timestamp": (/* @__PURE__ */ new Date()).toISOString(),
    prompt: input,
    prompt_length: input.length
  });
  const nonInteractiveConfig = await loadNonInteractiveConfig(
    config2,
    extensions,
    settings
  );
  await runNonInteractive(nonInteractiveConfig, input);
  process.exit(0);
}
function setWindowTitle(title, settings) {
  if (!settings.merged.hideWindowTitle) {
    process.stdout.write(`\x1B]2; Arien - ${title} \x07`);
    process.on("exit", () => {
      process.stdout.write(`\x1B]2;\x07`);
    });
  }
}
process.on("unhandledRejection", (reason, _promise) => {
  console.error("=========================================");
  console.error("CRITICAL: Unhandled Promise Rejection!");
  console.error("=========================================");
  console.error("Reason:", reason);
  console.error("Stack trace may follow:");
  if (!(reason instanceof Error)) {
    console.error(reason);
  }
  process.exit(1);
});
async function loadNonInteractiveConfig(config2, extensions, settings) {
  let finalConfig = config2;
  if (config2.getApprovalMode() !== ApprovalMode3.YOLO) {
    const existingExcludeTools = settings.merged.excludeTools || [];
    const interactiveTools = [
      ShellTool.Name,
      EditTool.Name,
      WriteFileTool.Name
    ];
    const newExcludeTools = [
      .../* @__PURE__ */ new Set([...existingExcludeTools, ...interactiveTools])
    ];
    const nonInteractiveSettings = {
      ...settings.merged,
      excludeTools: newExcludeTools
    };
    finalConfig = await loadCliConfig(
      nonInteractiveSettings,
      extensions,
      config2.getSessionId()
    );
  }
  return await validateNonInterActiveAuth(
    settings.merged.selectedAuthType,
    finalConfig
  );
}
async function validateNonInterActiveAuth(selectedAuthType, nonInteractiveConfig) {
  if (!selectedAuthType && !process.env.GEMINI_API_KEY) {
    console.error(
      `Please set an Auth method in your ${USER_SETTINGS_PATH} OR specify GEMINI_API_KEY env variable file before running`
    );
    process.exit(1);
  }
  selectedAuthType = selectedAuthType || AuthType3.USE_GEMINI;
  const err = validateAuthMethod(selectedAuthType);
  if (err != null) {
    console.error(err);
    process.exit(1);
  }
  await nonInteractiveConfig.refreshAuth(selectedAuthType);
  return nonInteractiveConfig;
}

// packages/cli/index.ts
main().catch((error) => {
  console.error("An unexpected critical error occurred:");
  if (error instanceof Error) {
    console.error(error.stack);
  } else {
    console.error(String(error));
  }
  process.exit(1);
});
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
//# sourceMappingURL=arien.js.map
