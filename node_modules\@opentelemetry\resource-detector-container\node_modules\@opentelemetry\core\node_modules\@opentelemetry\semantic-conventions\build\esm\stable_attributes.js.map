{"version": 3, "file": "stable_attributes.js", "sourceRoot": "", "sources": ["../../src/stable_attributes.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,4GAA4G;AAC5G,8GAA8G;AAC9G,4GAA4G;AAE5G;;;;;GAKG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;IAEI;AACJ,MAAM,CAAC,IAAM,8CAA8C,GAAG,UAAmB,CAAC;AAElF;;IAEI;AACJ,MAAM,CAAC,IAAM,sDAAsD,GAAG,kBAA2B,CAAC;AAElG;;IAEI;AACJ,MAAM,CAAC,IAAM,oDAAoD,GAAG,gBAAyB,CAAC;AAE9F;;IAEI;AACJ,MAAM,CAAC,IAAM,sDAAsD,GAAG,kBAA2B,CAAC;AAElG;;GAEG;AACH,MAAM,CAAC,IAAM,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;IAEI;AACJ,MAAM,CAAC,IAAM,gCAAgC,GAAG,KAAc,CAAC;AAE/D;;IAEI;AACJ,MAAM,CAAC,IAAM,mCAAmC,GAAG,QAAiB,CAAC;AAErE;;IAEI;AACJ,MAAM,CAAC,IAAM,mCAAmC,GAAG,QAAiB,CAAC;AAErE;;IAEI;AACJ,MAAM,CAAC,IAAM,+BAA+B,GAAG,IAAa,CAAC;AAE7D;;IAEI;AACJ,MAAM,CAAC,IAAM,iCAAiC,GAAG,MAAe,CAAC;AAEjE;;IAEI;AACJ,MAAM,CAAC,IAAM,mCAAmC,GAAG,QAAiB,CAAC;AAErE;;IAEI;AACJ,MAAM,CAAC,IAAM,gCAAgC,GAAG,KAAc,CAAC;AAE/D;;IAEI;AACJ,MAAM,CAAC,IAAM,mCAAmC,GAAG,QAAiB,CAAC;AAErE;;IAEI;AACJ,MAAM,CAAC,IAAM,iCAAiC,GAAG,MAAe,CAAC;AAEjE;;IAEI;AACJ,MAAM,CAAC,IAAM,iCAAiC,GAAG,MAAe,CAAC;AAEjE;;IAEI;AACJ,MAAM,CAAC,IAAM,kCAAkC,GAAG,OAAgB,CAAC;AAEnE;;IAEI;AACJ,MAAM,CAAC,IAAM,kCAAkC,GAAG,OAAgB,CAAC;AAEnE;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;GAIG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;GAIG;AACH,MAAM,CAAC,IAAM,wCAAwC,GAAG,qCAA8C,CAAC;AAEvG;;;;;GAKG;AACH,MAAM,CAAC,IAAM,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;IAEI;AACJ,MAAM,CAAC,IAAM,qDAAqD,GAAG,SAAkB,CAAC;AAExF;;IAEI;AACJ,MAAM,CAAC,IAAM,qDAAqD,GAAG,SAAkB,CAAC;AAExF;;IAEI;AACJ,MAAM,CAAC,IAAM,qDAAqD,GAAG,SAAkB,CAAC;AAExF;;IAEI;AACJ,MAAM,CAAC,IAAM,uDAAuD,GAAG,WAAoB,CAAC;AAE5F;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;GAIG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;GAIG;AACH,MAAM,CAAC,IAAM,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;GAKG;AACH,MAAM,CAAC,IAAM,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;IAEI;AACJ,MAAM,CAAC,IAAM,6CAA6C,GAAG,SAAkB,CAAC;AAEhF;;IAEI;AACJ,MAAM,CAAC,IAAM,6CAA6C,GAAG,SAAkB,CAAC;AAEhF;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,CAAC,IAAM,eAAe,GAAG,YAAqB,CAAC;AAErD;;IAEI;AACJ,MAAM,CAAC,IAAM,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;GAKG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;GAIG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;GAKG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;GASG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,UAAC,GAAW,IAAK,OAAA,yBAAuB,GAAK,EAA5B,CAA4B,CAAC;AAEtF;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;IAEI;AACJ,MAAM,CAAC,IAAM,+BAA+B,GAAG,QAAiB,CAAC;AAEjE;;IAEI;AACJ,MAAM,CAAC,IAAM,iCAAiC,GAAG,SAAkB,CAAC;AAEpE;;IAEI;AACJ,MAAM,CAAC,IAAM,gCAAgC,GAAG,QAAiB,CAAC;AAElE;;IAEI;AACJ,MAAM,CAAC,IAAM,6BAA6B,GAAG,KAAc,CAAC;AAE5D;;IAEI;AACJ,MAAM,CAAC,IAAM,8BAA8B,GAAG,MAAe,CAAC;AAE9D;;IAEI;AACJ,MAAM,CAAC,IAAM,iCAAiC,GAAG,SAAkB,CAAC;AAEpE;;IAEI;AACJ,MAAM,CAAC,IAAM,+BAA+B,GAAG,OAAgB,CAAC;AAEhE;;IAEI;AACJ,MAAM,CAAC,IAAM,8BAA8B,GAAG,MAAe,CAAC;AAE9D;;IAEI;AACJ,MAAM,CAAC,IAAM,6BAA6B,GAAG,KAAc,CAAC;AAE5D;;IAEI;AACJ,MAAM,CAAC,IAAM,+BAA+B,GAAG,OAAgB,CAAC;AAEhE;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;;GASG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,UAAC,GAAW,IAAK,OAAA,0BAAwB,GAAK,EAA7B,CAA6B,CAAC;AAExF;;;;GAIG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;GAKG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;IAEI;AACJ,MAAM,CAAC,IAAM,0BAA0B,GAAG,MAAe,CAAC;AAE1D;;IAEI;AACJ,MAAM,CAAC,IAAM,8BAA8B,GAAG,UAAmB,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;GAKG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;IAEI;AACJ,MAAM,CAAC,IAAM,8BAA8B,GAAG,SAAkB,CAAC;AAEjE;;IAEI;AACJ,MAAM,CAAC,IAAM,0BAA0B,GAAG,KAAc,CAAC;AAEzD;;IAEI;AACJ,MAAM,CAAC,IAAM,+BAA+B,GAAG,UAAmB,CAAC;AAEnE;;IAEI;AACJ,MAAM,CAAC,IAAM,iCAAiC,GAAG,YAAqB,CAAC;AAEvE;;IAEI;AACJ,MAAM,CAAC,IAAM,oCAAoC,GAAG,eAAwB,CAAC;AAE7E;;IAEI;AACJ,MAAM,CAAC,IAAM,8BAA8B,GAAG,SAAkB,CAAC;AAEjE;;;;;GAKG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;GAKG;AACH,MAAM,CAAC,IAAM,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;IAEI;AACJ,MAAM,CAAC,IAAM,4BAA4B,GAAG,MAAe,CAAC;AAE5D;;IAEI;AACJ,MAAM,CAAC,IAAM,4BAA4B,GAAG,MAAe,CAAC;AAE5D;;IAEI;AACJ,MAAM,CAAC,IAAM,2BAA2B,GAAG,KAAc,CAAC;AAE1D;;IAEI;AACJ,MAAM,CAAC,IAAM,2BAA2B,GAAG,KAAc,CAAC;AAE1D;;IAEI;AACJ,MAAM,CAAC,IAAM,4BAA4B,GAAG,MAAe,CAAC;AAE5D;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;IAEI;AACJ,MAAM,CAAC,IAAM,uBAAuB,GAAG,MAAe,CAAC;AAEvD;;IAEI;AACJ,MAAM,CAAC,IAAM,uBAAuB,GAAG,MAAe,CAAC;AAEvD;;;;GAIG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;GAIG;AACH,MAAM,CAAC,IAAM,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;IAEI;AACJ,MAAM,CAAC,IAAM,4BAA4B,GAAG,OAAgB,CAAC;AAE7D;;IAEI;AACJ,MAAM,CAAC,IAAM,yBAAyB,GAAG,IAAa,CAAC;AAEvD;;;;GAIG;AACH,MAAM,CAAC,IAAM,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;GAKG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;GAKG;AACH,MAAM,CAAC,IAAM,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;IAEI;AACJ,MAAM,CAAC,IAAM,4CAA4C,GAAG,cAAuB,CAAC;AAEpF;;IAEI;AACJ,MAAM,CAAC,IAAM,8CAA8C,GAAG,gBAAyB,CAAC;AAExF;;IAEI;AACJ,MAAM,CAAC,IAAM,uCAAuC,GAAG,SAAkB,CAAC;AAE1E;;;;;GAKG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;IAEI;AACJ,MAAM,CAAC,IAAM,oCAAoC,GAAG,cAAuB,CAAC;AAE5E;;IAEI;AACJ,MAAM,CAAC,IAAM,0CAA0C,GAAG,oBAA6B,CAAC;AAExF;;IAEI;AACJ,MAAM,CAAC,IAAM,mCAAmC,GAAG,aAAsB,CAAC;AAE1E;;;;GAIG;AACH,MAAM,CAAC,IAAM,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;;GASG;AACH,MAAM,CAAC,IAAM,aAAa,GAAG,UAAmB,CAAC;AAEjD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,aAAa,GAAG,UAAmB,CAAC;AAEjD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,wBAAwB,GAAG,qBAA8B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/registry/stable/attributes.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n/**\n * Rate-limiting result, shows whether the lease was acquired or contains a rejection reason\n * \n * @example acquired\n * @example request_canceled\n */\nexport const ATTR_ASPNETCORE_RATE_LIMITING_RESULT = 'aspnetcore.rate_limiting.result' as const;\n\n/**\n  * Enum value \"acquired\" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.\n  */\nexport const ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED = \"acquired\" as const;\n\n/**\n  * Enum value \"endpoint_limiter\" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.\n  */\nexport const ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER = \"endpoint_limiter\" as const;\n\n/**\n  * Enum value \"global_limiter\" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.\n  */\nexport const ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER = \"global_limiter\" as const;\n\n/**\n  * Enum value \"request_canceled\" for attribute {@link ATTR_ASPNETCORE_RATE_LIMITING_RESULT}.\n  */\nexport const ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED = \"request_canceled\" as const;\n\n/**\n * The language of the telemetry SDK.\n */\nexport const ATTR_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language' as const;\n\n/**\n  * Enum value \"cpp\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_CPP = \"cpp\" as const;\n\n/**\n  * Enum value \"dotnet\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET = \"dotnet\" as const;\n\n/**\n  * Enum value \"erlang\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG = \"erlang\" as const;\n\n/**\n  * Enum value \"go\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_GO = \"go\" as const;\n\n/**\n  * Enum value \"java\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_JAVA = \"java\" as const;\n\n/**\n  * Enum value \"nodejs\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS = \"nodejs\" as const;\n\n/**\n  * Enum value \"php\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_PHP = \"php\" as const;\n\n/**\n  * Enum value \"python\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON = \"python\" as const;\n\n/**\n  * Enum value \"ruby\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_RUBY = \"ruby\" as const;\n\n/**\n  * Enum value \"rust\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_RUST = \"rust\" as const;\n\n/**\n  * Enum value \"swift\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT = \"swift\" as const;\n\n/**\n  * Enum value \"webjs\" for attribute {@link ATTR_TELEMETRY_SDK_LANGUAGE}.\n  */\nexport const TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS = \"webjs\" as const;\n\n/**\n * The name of the telemetry SDK as defined above.\n * \n * @example opentelemetry\n * \n * @note The OpenTelemetry SDK **MUST** set the `telemetry.sdk.name` attribute to `opentelemetry`.\n * If another SDK, like a fork or a vendor-provided implementation, is used, this SDK **MUST** set the\n * `telemetry.sdk.name` attribute to the fully-qualified class or module name of this SDK's main entry point\n * or another suitable identifier depending on the language.\n * The identifier `opentelemetry` is reserved and **MUST NOT** be used in this case.\n * All custom identifiers **SHOULD** be stable across different versions of an implementation.\n */\nexport const ATTR_TELEMETRY_SDK_NAME = 'telemetry.sdk.name' as const;\n\n/**\n * The version string of the telemetry SDK.\n * \n * @example 1.2.3\n */\nexport const ATTR_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version' as const;\n\n/**\n * Full type name of the [`IExceptionHandler`](https://learn.microsoft.com/dotnet/api/microsoft.aspnetcore.diagnostics.iexceptionhandler) implementation that handled the exception.\n * \n * @example Contoso.MyHandler\n */\nexport const ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE = 'aspnetcore.diagnostics.handler.type' as const;\n\n/**\n * ASP.NET Core exception middleware handling result\n * \n * @example handled\n * @example unhandled\n */\nexport const ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT = 'aspnetcore.diagnostics.exception.result' as const;\n\n/**\n  * Enum value \"aborted\" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.\n  */\nexport const ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED = \"aborted\" as const;\n\n/**\n  * Enum value \"handled\" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.\n  */\nexport const ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED = \"handled\" as const;\n\n/**\n  * Enum value \"skipped\" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.\n  */\nexport const ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED = \"skipped\" as const;\n\n/**\n  * Enum value \"unhandled\" for attribute {@link ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT}.\n  */\nexport const ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED = \"unhandled\" as const;\n\n/**\n * Rate limiting policy name.\n * \n * @example fixed\n * @example sliding\n * @example token\n */\nexport const ATTR_ASPNETCORE_RATE_LIMITING_POLICY = 'aspnetcore.rate_limiting.policy' as const;\n\n/**\n * Flag indicating if request was handled by the application pipeline.\n * \n * @example true\n */\nexport const ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED = 'aspnetcore.request.is_unhandled' as const;\n\n/**\n * A value that indicates whether the matched route is a fallback route.\n * \n * @example true\n */\nexport const ATTR_ASPNETCORE_ROUTING_IS_FALLBACK = 'aspnetcore.routing.is_fallback' as const;\n\n/**\n * Match result - success or failure\n * \n * @example success\n * @example failure\n */\nexport const ATTR_ASPNETCORE_ROUTING_MATCH_STATUS = 'aspnetcore.routing.match_status' as const;\n\n/**\n  * Enum value \"failure\" for attribute {@link ATTR_ASPNETCORE_ROUTING_MATCH_STATUS}.\n  */\nexport const ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE = \"failure\" as const;\n\n/**\n  * Enum value \"success\" for attribute {@link ATTR_ASPNETCORE_ROUTING_MATCH_STATUS}.\n  */\nexport const ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS = \"success\" as const;\n\n/**\n * Client address - domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n * \n * @example client.example.com\n * @example *********\n * @example /tmp/my.sock\n * \n * @note When observed from the server side, and when communicating through an intermediary, `client.address` **SHOULD** represent the client address behind any intermediaries,  for example proxies, if it's available.\n */\nexport const ATTR_CLIENT_ADDRESS = 'client.address' as const;\n\n/**\n * Client port number.\n * \n * @example 65123\n * \n * @note When observed from the server side, and when communicating through an intermediary, `client.port` **SHOULD** represent the client port behind any intermediaries,  for example proxies, if it's available.\n */\nexport const ATTR_CLIENT_PORT = 'client.port' as const;\n\n/**\n * Describes a class of error the operation ended with.\n * \n * @example timeout\n * @example java.net.UnknownHostException\n * @example server_certificate_invalid\n * @example 500\n * \n * @note The `error.type` **SHOULD** be predictable, and **SHOULD** have low cardinality.\n * \n * When `error.type` is set to a type (e.g., an exception type), its\n * canonical class name identifying the type within the artifact **SHOULD** be used.\n * \n * Instrumentations **SHOULD** document the list of errors they report.\n * \n * The cardinality of `error.type` within one instrumentation library **SHOULD** be low.\n * Telemetry consumers that aggregate data from multiple instrumentation libraries and applications\n * should be prepared for `error.type` to have high cardinality at query time when no\n * additional filters are applied.\n * \n * If the operation has completed successfully, instrumentations **SHOULD NOT** set `error.type`.\n * \n * If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\n * it's **RECOMMENDED** to:\n * \n *   - Use a domain-specific attribute\n *   - Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n */\nexport const ATTR_ERROR_TYPE = 'error.type' as const;\n\n/**\n  * Enum value \"_OTHER\" for attribute {@link ATTR_ERROR_TYPE}.\n  */\nexport const ERROR_TYPE_VALUE_OTHER = \"_OTHER\" as const;\n\n/**\n * **SHOULD** be set to true if the exception event is recorded at a point where it is known that the exception is escaping the scope of the span.\n * \n * @note An exception is considered to have escaped (or left) the scope of a span,\n * if that span is ended while the exception is still logically \"in flight\".\n * This may be actually \"in flight\" in some languages (e.g. if the exception\n * is passed to a Context manager's `__exit__` method in Python) but will\n * usually be caught at the point of recording the exception in most languages.\n * \n * It is usually not possible to determine at the point where an exception is thrown\n * whether it will escape the scope of a span.\n * However, it is trivial to know that an exception\n * will escape, if one checks for an active exception just before ending the span,\n * as done in the [example for recording span exceptions](https://opentelemetry.io/docs/specs/semconv/exceptions/exceptions-spans/#recording-an-exception).\n * \n * It follows that an exception may still escape the scope of the span\n * even if the `exception.escaped` attribute was not set or set to false,\n * since the event might have been recorded at a time where it was not\n * clear whether the exception will escape.\n */\nexport const ATTR_EXCEPTION_ESCAPED = 'exception.escaped' as const;\n\n/**\n * The exception message.\n * \n * @example Division by zero\n * @example Can't convert 'int' object to str implicitly\n */\nexport const ATTR_EXCEPTION_MESSAGE = 'exception.message' as const;\n\n/**\n * A stacktrace as a string in the natural representation for the language runtime. The representation is to be determined and documented by each language SIG.\n * \n * @example \"Exception in thread \"main\" java.lang.RuntimeException: Test exception\\\\n at com.example.GenerateTrace.methodB(GenerateTrace.java:13)\\\\n at com.example.GenerateTrace.methodA(GenerateTrace.java:9)\\\\n at com.example.GenerateTrace.main(GenerateTrace.java:5)\\\\n\"\n */\nexport const ATTR_EXCEPTION_STACKTRACE = 'exception.stacktrace' as const;\n\n/**\n * The type of the exception (its fully-qualified class name, if applicable). The dynamic type of the exception should be preferred over the static type in languages that support it.\n * \n * @example java.net.ConnectException\n * @example OSError\n */\nexport const ATTR_EXCEPTION_TYPE = 'exception.type' as const;\n\n/**\n * HTTP request headers, `<key>` being the normalized HTTP Header name (lowercase), the value being the header values.\n * \n * @example http.request.header.content-type=[\"application/json\"]\n * @example http.request.header.x-forwarded-for=[\"*******\", \"*******\"]\n * \n * @note Instrumentations **SHOULD** require an explicit configuration of which headers are to be captured. Including all request headers can be a security risk - explicit configuration helps avoid leaking sensitive information.\n * The `User-Agent` header is already captured in the `user_agent.original` attribute. Users **MAY** explicitly configure instrumentations to capture them even though it is not recommended.\n * The attribute value **MUST** consist of either multiple header values as an array of strings or a single-item array containing a possibly comma-concatenated string, depending on the way the HTTP library provides access to headers.\n */\nexport const ATTR_HTTP_REQUEST_HEADER = (key: string) => `http.request.header.${key}`;\n\n/**\n * HTTP request method.\n * \n * @example GET\n * @example POST\n * @example HEAD\n * \n * @note HTTP request method value **SHOULD** be \"known\" to the instrumentation.\n * By default, this convention defines \"known\" methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\n * and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n * \n * If the HTTP request method is not known to instrumentation, it **MUST** set the `http.request.method` attribute to `_OTHER`.\n * \n * If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it **MUST** provide a way to override\n * the list of known HTTP methods. If this override is done via environment variable, then the environment variable **MUST** be named\n * OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n * (this list **MUST** be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n * \n * HTTP method names are case-sensitive and `http.request.method` attribute value **MUST** match a known HTTP method name exactly.\n * Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, **SHOULD** populate a canonical equivalent.\n * Tracing instrumentations that do so, **MUST** also set `http.request.method_original` to the original value.\n */\nexport const ATTR_HTTP_REQUEST_METHOD = 'http.request.method' as const;\n\n/**\n  * Enum value \"_OTHER\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_OTHER = \"_OTHER\" as const;\n\n/**\n  * Enum value \"CONNECT\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_CONNECT = \"CONNECT\" as const;\n\n/**\n  * Enum value \"DELETE\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_DELETE = \"DELETE\" as const;\n\n/**\n  * Enum value \"GET\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_GET = \"GET\" as const;\n\n/**\n  * Enum value \"HEAD\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_HEAD = \"HEAD\" as const;\n\n/**\n  * Enum value \"OPTIONS\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_OPTIONS = \"OPTIONS\" as const;\n\n/**\n  * Enum value \"PATCH\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_PATCH = \"PATCH\" as const;\n\n/**\n  * Enum value \"POST\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_POST = \"POST\" as const;\n\n/**\n  * Enum value \"PUT\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_PUT = \"PUT\" as const;\n\n/**\n  * Enum value \"TRACE\" for attribute {@link ATTR_HTTP_REQUEST_METHOD}.\n  */\nexport const HTTP_REQUEST_METHOD_VALUE_TRACE = \"TRACE\" as const;\n\n/**\n * Original HTTP method sent by the client in the request line.\n * \n * @example GeT\n * @example ACL\n * @example foo\n */\nexport const ATTR_HTTP_REQUEST_METHOD_ORIGINAL = 'http.request.method_original' as const;\n\n/**\n * The ordinal number of request resending attempt (for any reason, including redirects).\n * \n * @example 3\n * \n * @note The resend count **SHOULD** be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n */\nexport const ATTR_HTTP_REQUEST_RESEND_COUNT = 'http.request.resend_count' as const;\n\n/**\n * HTTP response headers, `<key>` being the normalized HTTP Header name (lowercase), the value being the header values.\n * \n * @example http.response.header.content-type=[\"application/json\"]\n * @example http.response.header.my-custom-header=[\"abc\", \"def\"]\n * \n * @note Instrumentations **SHOULD** require an explicit configuration of which headers are to be captured. Including all response headers can be a security risk - explicit configuration helps avoid leaking sensitive information.\n * Users **MAY** explicitly configure instrumentations to capture them even though it is not recommended.\n * The attribute value **MUST** consist of either multiple header values as an array of strings or a single-item array containing a possibly comma-concatenated string, depending on the way the HTTP library provides access to headers.\n */\nexport const ATTR_HTTP_RESPONSE_HEADER = (key: string) => `http.response.header.${key}`;\n\n/**\n * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n * \n * @example 200\n */\nexport const ATTR_HTTP_RESPONSE_STATUS_CODE = 'http.response.status_code' as const;\n\n/**\n * The matched route, that is, the path template in the format used by the respective server framework.\n * \n * @example /users/:userID?\n * @example {controller}/{action}/{id?}\n * \n * @note **MUST NOT** be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\n * **SHOULD** include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n */\nexport const ATTR_HTTP_ROUTE = 'http.route' as const;\n\n/**\n * Name of the garbage collector action.\n * \n * @example end of minor GC\n * @example end of major GC\n * \n * @note Garbage collector action is generally obtained via [GarbageCollectionNotificationInfo#getGcAction()](https://docs.oracle.com/en/java/javase/11/docs/api/jdk.management/com/sun/management/GarbageCollectionNotificationInfo.html#getGcAction()).\n */\nexport const ATTR_JVM_GC_ACTION = 'jvm.gc.action' as const;\n\n/**\n * Name of the garbage collector.\n * \n * @example G1 Young Generation\n * @example G1 Old Generation\n * \n * @note Garbage collector name is generally obtained via [GarbageCollectionNotificationInfo#getGcName()](https://docs.oracle.com/en/java/javase/11/docs/api/jdk.management/com/sun/management/GarbageCollectionNotificationInfo.html#getGcName()).\n */\nexport const ATTR_JVM_GC_NAME = 'jvm.gc.name' as const;\n\n/**\n * Name of the memory pool.\n * \n * @example G1 Old Gen\n * @example G1 Eden space\n * @example G1 Survivor Space\n * \n * @note Pool names are generally obtained via [MemoryPoolMXBean#getName()](https://docs.oracle.com/en/java/javase/11/docs/api/java.management/java/lang/management/MemoryPoolMXBean.html#getName()).\n */\nexport const ATTR_JVM_MEMORY_POOL_NAME = 'jvm.memory.pool.name' as const;\n\n/**\n * The type of memory.\n * \n * @example heap\n * @example non_heap\n */\nexport const ATTR_JVM_MEMORY_TYPE = 'jvm.memory.type' as const;\n\n/**\n  * Enum value \"heap\" for attribute {@link ATTR_JVM_MEMORY_TYPE}.\n  */\nexport const JVM_MEMORY_TYPE_VALUE_HEAP = \"heap\" as const;\n\n/**\n  * Enum value \"non_heap\" for attribute {@link ATTR_JVM_MEMORY_TYPE}.\n  */\nexport const JVM_MEMORY_TYPE_VALUE_NON_HEAP = \"non_heap\" as const;\n\n/**\n * Whether the thread is daemon or not.\n */\nexport const ATTR_JVM_THREAD_DAEMON = 'jvm.thread.daemon' as const;\n\n/**\n * State of the thread.\n * \n * @example runnable\n * @example blocked\n */\nexport const ATTR_JVM_THREAD_STATE = 'jvm.thread.state' as const;\n\n/**\n  * Enum value \"blocked\" for attribute {@link ATTR_JVM_THREAD_STATE}.\n  */\nexport const JVM_THREAD_STATE_VALUE_BLOCKED = \"blocked\" as const;\n\n/**\n  * Enum value \"new\" for attribute {@link ATTR_JVM_THREAD_STATE}.\n  */\nexport const JVM_THREAD_STATE_VALUE_NEW = \"new\" as const;\n\n/**\n  * Enum value \"runnable\" for attribute {@link ATTR_JVM_THREAD_STATE}.\n  */\nexport const JVM_THREAD_STATE_VALUE_RUNNABLE = \"runnable\" as const;\n\n/**\n  * Enum value \"terminated\" for attribute {@link ATTR_JVM_THREAD_STATE}.\n  */\nexport const JVM_THREAD_STATE_VALUE_TERMINATED = \"terminated\" as const;\n\n/**\n  * Enum value \"timed_waiting\" for attribute {@link ATTR_JVM_THREAD_STATE}.\n  */\nexport const JVM_THREAD_STATE_VALUE_TIMED_WAITING = \"timed_waiting\" as const;\n\n/**\n  * Enum value \"waiting\" for attribute {@link ATTR_JVM_THREAD_STATE}.\n  */\nexport const JVM_THREAD_STATE_VALUE_WAITING = \"waiting\" as const;\n\n/**\n * Local address of the network connection - IP address or Unix domain socket name.\n * \n * @example *********\n * @example /tmp/my.sock\n */\nexport const ATTR_NETWORK_LOCAL_ADDRESS = 'network.local.address' as const;\n\n/**\n * Local port number of the network connection.\n * \n * @example 65123\n */\nexport const ATTR_NETWORK_LOCAL_PORT = 'network.local.port' as const;\n\n/**\n * Peer address of the network connection - IP address or Unix domain socket name.\n * \n * @example *********\n * @example /tmp/my.sock\n */\nexport const ATTR_NETWORK_PEER_ADDRESS = 'network.peer.address' as const;\n\n/**\n * Peer port number of the network connection.\n * \n * @example 65123\n */\nexport const ATTR_NETWORK_PEER_PORT = 'network.peer.port' as const;\n\n/**\n * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n * \n * @example amqp\n * @example http\n * @example mqtt\n * \n * @note The value **SHOULD** be normalized to lowercase.\n */\nexport const ATTR_NETWORK_PROTOCOL_NAME = 'network.protocol.name' as const;\n\n/**\n * The actual version of the protocol used for network communication.\n * \n * @example 1.1\n * @example 2\n * \n * @note If protocol version is subject to negotiation (for example using [ALPN](https://www.rfc-editor.org/rfc/rfc7301.html)), this attribute **SHOULD** be set to the negotiated version. If the actual protocol version is not known, this attribute **SHOULD NOT** be set.\n */\nexport const ATTR_NETWORK_PROTOCOL_VERSION = 'network.protocol.version' as const;\n\n/**\n * [OSI transport layer](https://osi-model.com/transport-layer/) or [inter-process communication method](https://wikipedia.org/wiki/Inter-process_communication).\n * \n * @example tcp\n * @example udp\n * \n * @note The value **SHOULD** be normalized to lowercase.\n * \n * Consider always setting the transport when setting a port number, since\n * a port number is ambiguous without knowing the transport. For example\n * different processes could be listening on TCP port 12345 and UDP port 12345.\n */\nexport const ATTR_NETWORK_TRANSPORT = 'network.transport' as const;\n\n/**\n  * Enum value \"pipe\" for attribute {@link ATTR_NETWORK_TRANSPORT}.\n  */\nexport const NETWORK_TRANSPORT_VALUE_PIPE = \"pipe\" as const;\n\n/**\n  * Enum value \"quic\" for attribute {@link ATTR_NETWORK_TRANSPORT}.\n  */\nexport const NETWORK_TRANSPORT_VALUE_QUIC = \"quic\" as const;\n\n/**\n  * Enum value \"tcp\" for attribute {@link ATTR_NETWORK_TRANSPORT}.\n  */\nexport const NETWORK_TRANSPORT_VALUE_TCP = \"tcp\" as const;\n\n/**\n  * Enum value \"udp\" for attribute {@link ATTR_NETWORK_TRANSPORT}.\n  */\nexport const NETWORK_TRANSPORT_VALUE_UDP = \"udp\" as const;\n\n/**\n  * Enum value \"unix\" for attribute {@link ATTR_NETWORK_TRANSPORT}.\n  */\nexport const NETWORK_TRANSPORT_VALUE_UNIX = \"unix\" as const;\n\n/**\n * [OSI network layer](https://osi-model.com/network-layer/) or non-OSI equivalent.\n * \n * @example ipv4\n * @example ipv6\n * \n * @note The value **SHOULD** be normalized to lowercase.\n */\nexport const ATTR_NETWORK_TYPE = 'network.type' as const;\n\n/**\n  * Enum value \"ipv4\" for attribute {@link ATTR_NETWORK_TYPE}.\n  */\nexport const NETWORK_TYPE_VALUE_IPV4 = \"ipv4\" as const;\n\n/**\n  * Enum value \"ipv6\" for attribute {@link ATTR_NETWORK_TYPE}.\n  */\nexport const NETWORK_TYPE_VALUE_IPV6 = \"ipv6\" as const;\n\n/**\n * The name of the instrumentation scope - (`InstrumentationScope.Name` in OTLP).\n * \n * @example io.opentelemetry.contrib.mongodb\n */\nexport const ATTR_OTEL_SCOPE_NAME = 'otel.scope.name' as const;\n\n/**\n * The version of the instrumentation scope - (`InstrumentationScope.Version` in OTLP).\n * \n * @example 1.0.0\n */\nexport const ATTR_OTEL_SCOPE_VERSION = 'otel.scope.version' as const;\n\n/**\n * Name of the code, either \"OK\" or \"ERROR\". **MUST NOT** be set if the status code is UNSET.\n */\nexport const ATTR_OTEL_STATUS_CODE = 'otel.status_code' as const;\n\n/**\n  * Enum value \"ERROR\" for attribute {@link ATTR_OTEL_STATUS_CODE}.\n  */\nexport const OTEL_STATUS_CODE_VALUE_ERROR = \"ERROR\" as const;\n\n/**\n  * Enum value \"OK\" for attribute {@link ATTR_OTEL_STATUS_CODE}.\n  */\nexport const OTEL_STATUS_CODE_VALUE_OK = \"OK\" as const;\n\n/**\n * Description of the Status if it has a value, otherwise not set.\n * \n * @example resource not found\n */\nexport const ATTR_OTEL_STATUS_DESCRIPTION = 'otel.status_description' as const;\n\n/**\n * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n * \n * @example example.com\n * @example *********\n * @example /tmp/my.sock\n * \n * @note When observed from the client side, and when communicating through an intermediary, `server.address` **SHOULD** represent the server address behind any intermediaries, for example proxies, if it's available.\n */\nexport const ATTR_SERVER_ADDRESS = 'server.address' as const;\n\n/**\n * Server port number.\n * \n * @example 80\n * @example 8080\n * @example 443\n * \n * @note When observed from the client side, and when communicating through an intermediary, `server.port` **SHOULD** represent the server port behind any intermediaries, for example proxies, if it's available.\n */\nexport const ATTR_SERVER_PORT = 'server.port' as const;\n\n/**\n * Logical name of the service.\n * \n * @example shoppingcart\n * \n * @note **MUST** be the same for all instances of horizontally scaled services. If the value was not specified, SDKs **MUST** fallback to `unknown_service:` concatenated with [`process.executable.name`](process.md), e.g. `unknown_service:bash`. If `process.executable.name` is not available, the value **MUST** be set to `unknown_service`.\n */\nexport const ATTR_SERVICE_NAME = 'service.name' as const;\n\n/**\n * The version string of the service API or implementation. The format is not defined by these conventions.\n * \n * @example 2.0.0\n * @example a01dbef8a\n */\nexport const ATTR_SERVICE_VERSION = 'service.version' as const;\n\n/**\n * SignalR HTTP connection closure status.\n * \n * @example app_shutdown\n * @example timeout\n */\nexport const ATTR_SIGNALR_CONNECTION_STATUS = 'signalr.connection.status' as const;\n\n/**\n  * Enum value \"app_shutdown\" for attribute {@link ATTR_SIGNALR_CONNECTION_STATUS}.\n  */\nexport const SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN = \"app_shutdown\" as const;\n\n/**\n  * Enum value \"normal_closure\" for attribute {@link ATTR_SIGNALR_CONNECTION_STATUS}.\n  */\nexport const SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE = \"normal_closure\" as const;\n\n/**\n  * Enum value \"timeout\" for attribute {@link ATTR_SIGNALR_CONNECTION_STATUS}.\n  */\nexport const SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT = \"timeout\" as const;\n\n/**\n * [SignalR transport type](https://github.com/dotnet/aspnetcore/blob/main/src/SignalR/docs/specs/TransportProtocols.md)\n * \n * @example web_sockets\n * @example long_polling\n */\nexport const ATTR_SIGNALR_TRANSPORT = 'signalr.transport' as const;\n\n/**\n  * Enum value \"long_polling\" for attribute {@link ATTR_SIGNALR_TRANSPORT}.\n  */\nexport const SIGNALR_TRANSPORT_VALUE_LONG_POLLING = \"long_polling\" as const;\n\n/**\n  * Enum value \"server_sent_events\" for attribute {@link ATTR_SIGNALR_TRANSPORT}.\n  */\nexport const SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS = \"server_sent_events\" as const;\n\n/**\n  * Enum value \"web_sockets\" for attribute {@link ATTR_SIGNALR_TRANSPORT}.\n  */\nexport const SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS = \"web_sockets\" as const;\n\n/**\n * The [URI fragment](https://www.rfc-editor.org/rfc/rfc3986#section-3.5) component\n * \n * @example SemConv\n */\nexport const ATTR_URL_FRAGMENT = 'url.fragment' as const;\n\n/**\n * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986)\n * \n * @example https://www.foo.bar/search?q=OpenTelemetry#SemConv\n * @example //localhost\n * \n * @note For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it **SHOULD** be included nevertheless.\n * `url.full` **MUST NOT** contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password **SHOULD** be redacted and attribute's value **SHOULD** be `https://REDACTED:<EMAIL>/`.\n * `url.full` **SHOULD** capture the absolute URL when it is available (or can be reconstructed). Sensitive content provided in `url.full` **SHOULD** be scrubbed when instrumentations can identify it.\n */\nexport const ATTR_URL_FULL = 'url.full' as const;\n\n/**\n * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component\n * \n * @example /search\n * \n * @note Sensitive content provided in `url.path` **SHOULD** be scrubbed when instrumentations can identify it.\n */\nexport const ATTR_URL_PATH = 'url.path' as const;\n\n/**\n * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component\n * \n * @example q=OpenTelemetry\n * \n * @note Sensitive content provided in `url.query` **SHOULD** be scrubbed when instrumentations can identify it.\n */\nexport const ATTR_URL_QUERY = 'url.query' as const;\n\n/**\n * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n * \n * @example https\n * @example ftp\n * @example telnet\n */\nexport const ATTR_URL_SCHEME = 'url.scheme' as const;\n\n/**\n * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n * \n * @example CERN-LineMode/2.15 libwww/2.17b3\n * @example Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1\n * @example YourApp/1.0.0 grpc-java-okhttp/1.27.2\n */\nexport const ATTR_USER_AGENT_ORIGINAL = 'user_agent.original' as const;\n\n"]}