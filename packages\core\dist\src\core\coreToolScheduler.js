/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
class CoreToolScheduler {
  toolCalls = /* @__PURE__ */ new Map();
  callbacks;
  config;
  toolRegistry;
  constructor(config, toolRegistry, callbacks = {}) {
    this.config = config;
    this.toolRegistry = toolRegistry;
    this.callbacks = callbacks;
  }
  /**
   * Schedule tool calls for execution
   */
  async scheduleToolCalls(toolCallRequests) {
    const responses = [];
    for (const request of toolCallRequests) {
      try {
        const tool = await this.toolRegistry.getTool(request.name);
        if (!tool) {
          const errorResponse = {
            name: request.name,
            response: {
              error: `Tool '${request.name}' not found`
            }
          };
          responses.push(errorResponse);
          continue;
        }
        const toolCall = {
          status: "scheduled",
          request,
          tool,
          startTime: Date.now()
        };
        this.toolCalls.set(request.name, toolCall);
        this.callbacks.onToolCallUpdate?.(toolCall);
        const response = await this.executeToolCall(toolCall);
        responses.push(response);
      } catch (error) {
        const errorResponse = {
          name: request.name,
          response: {
            error: error instanceof Error ? error.message : String(error)
          }
        };
        responses.push(errorResponse);
      }
    }
    return responses;
  }
  /**
   * Execute a single tool call
   */
  async executeToolCall(toolCall) {
    const executingToolCall = {
      ...toolCall,
      status: "executing"
    };
    this.toolCalls.set(toolCall.request.name, executingToolCall);
    this.callbacks.onToolCallUpdate?.(executingToolCall);
    try {
      const result = await toolCall.tool.execute(
        toolCall.request.args,
        this.config
      );
      const successfulToolCall = {
        ...executingToolCall,
        status: "success",
        response: {
          name: toolCall.request.name,
          response: result
        },
        durationMs: Date.now() - (toolCall.startTime || 0)
      };
      this.toolCalls.set(toolCall.request.name, successfulToolCall);
      this.callbacks.onToolCallUpdate?.(successfulToolCall);
      this.callbacks.onToolCallComplete?.(successfulToolCall);
      return successfulToolCall.response;
    } catch (error) {
      const erroredToolCall = {
        ...executingToolCall,
        status: "error",
        response: {
          name: toolCall.request.name,
          response: {
            error: error instanceof Error ? error.message : String(error)
          }
        },
        durationMs: Date.now() - (toolCall.startTime || 0)
      };
      this.toolCalls.set(toolCall.request.name, erroredToolCall);
      this.callbacks.onToolCallUpdate?.(erroredToolCall);
      this.callbacks.onToolCallComplete?.(erroredToolCall);
      return erroredToolCall.response;
    }
  }
  /**
   * Get all tool calls
   */
  getToolCalls() {
    return Array.from(this.toolCalls.values());
  }
  /**
   * Get a specific tool call by name
   */
  getToolCall(name) {
    return this.toolCalls.get(name);
  }
  /**
   * Clear all tool calls
   */
  clear() {
    this.toolCalls.clear();
  }
}
export {
  CoreToolScheduler
};
//# sourceMappingURL=coreToolScheduler.js.map
