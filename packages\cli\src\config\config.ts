/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import yargs from 'yargs/yargs';
import { hideBin } from 'yargs/helpers';
import process from 'node:process';
import {
  Config,
  loadServerHierarchicalMemory,
  getCurrentGeminiMdFilename,
  ApprovalMode,
  GEMINI_CONFIG_DIR as GEMINI_DIR,
  DEFAULT_GEMINI_MODEL,
  DEFAULT_GEMINI_EMBEDDING_MODEL,
  FileDiscoveryService,
  TelemetryTarget,
} from '@arien/arien-cli-core';
import { Settings } from './settings.js';

import { Extension } from './extension.js';
import { getCliVersion } from '../utils/version.js';
import * as dotenv from 'dotenv';
import * as fs from 'node:fs';
import * as path from 'node:path';
import * as os from 'node:os';
import { loadSandboxConfig } from './sandboxConfig.js';

// Simple console logger for now - replace with actual logger if available
const logger = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  debug: (...args: any[]) => console.debug('[DEBUG]', ...args),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  warn: (...args: any[]) => console.warn('[WARN]', ...args),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: (...args: any[]) => console.error('[ERROR]', ...args),
};

interface CliArgs {
  model: string | undefined;
  sandbox: boolean | string | undefined;
  'sandbox-image': string | undefined;
  debug: boolean | undefined;
  prompt: string | undefined;
  all_files: boolean | undefined;
  show_memory_usage: boolean | undefined;
  yolo: boolean | undefined;
  telemetry: boolean | undefined;
  checkpointing: boolean | undefined;
  telemetryTarget: string | undefined;
  telemetryOtlpEndpoint: string | undefined;
  telemetryLogPrompts: boolean | undefined;
}

async function parseArguments(): Promise<CliArgs> {
  const argv = await yargs(hideBin(process.argv))
    .option('model', {
      alias: 'm',
      type: 'string',
      description: `Model`,
      default: process.env.GEMINI_MODEL || DEFAULT_GEMINI_MODEL,
    })
    .option('prompt', {
      alias: 'p',
      type: 'string',
      description: 'Prompt. Appended to input on stdin (if any).',
    })
    .option('sandbox', {
      alias: 's',
      type: 'boolean',
      description: 'Run in sandbox?',
    })
    .option('sandbox-image', {
      type: 'string',
      description: 'Sandbox image URI.',
    })
    .option('debug', {
      alias: 'd',
      type: 'boolean',
      description: 'Run in debug mode?',
      default: false,
    })
    .option('all_files', {
      alias: 'a',
      type: 'boolean',
      description: 'Include ALL files in context?',
      default: false,
    })
    .option('show_memory_usage', {
      type: 'boolean',
      description: 'Show memory usage in status bar',
      default: false,
    })
    .option('yolo', {
      alias: 'y',
      type: 'boolean',
      description: 'Auto-approve all tool calls (dangerous!)',
      default: false,
    })
    .option('telemetry', {
      type: 'boolean',
      description: 'Enable telemetry',
    })
    .option('checkpointing', {
      type: 'boolean',
      description: 'Enable checkpointing',
    })
    .option('telemetryTarget', {
      type: 'string',
      description: 'Telemetry target',
    })
    .option('telemetryOtlpEndpoint', {
      type: 'string',
      description: 'OTLP endpoint for telemetry',
    })
    .option('telemetryLogPrompts', {
      type: 'boolean',
      description: 'Log prompts in telemetry',
    })
    .help()
    .alias('help', 'h')
    .version(getCliVersion())
    .alias('version', 'v')
    .parse();

  return argv as CliArgs;
}

export async function loadCliConfig(
  settings: Settings,
  extensions: Extension[],
  sessionId: string,
): Promise<Config> {
  const args = await parseArguments();

  // Load environment variables from .env files
  loadEnvFiles();

  // Create config with hierarchical settings loading
  // Convert Settings to ConfigOptions for initial configuration
  const initialOptions = convertSettingsToConfigOptions(settings, sessionId);
  const config = await Config.createWithSettings(process.cwd(), initialOptions);

  // Apply CLI arguments (these override settings)
  if (args.model) {
    config.setModel(args.model);
  }

  if (args.debug !== undefined) {
    config.setDebugMode(args.debug);
  }

  if (args.prompt) {
    config.setQuestion(args.prompt);
  }

  if (args.all_files !== undefined) {
    config.setIncludeAllFiles(args.all_files);
  }

  if (args.show_memory_usage !== undefined) {
    config.setShowMemoryUsage(args.show_memory_usage);
  }

  if (args.yolo !== undefined) {
    config.setApprovalMode(args.yolo ? ApprovalMode.YOLO : ApprovalMode.MANUAL);
  }

  if (args.telemetry !== undefined) {
    config.setTelemetryEnabled(args.telemetry);
  }

  if (args.checkpointing !== undefined) {
    config.setCheckpointingEnabled(args.checkpointing);
  }

  if (args.telemetryTarget) {
    config.setTelemetryTarget(args.telemetryTarget as TelemetryTarget);
  }

  if (args.telemetryOtlpEndpoint) {
    config.setTelemetryOtlpEndpoint(args.telemetryOtlpEndpoint);
  }

  if (args.telemetryLogPrompts !== undefined) {
    config.setTelemetryLogPrompts(args.telemetryLogPrompts);
  }

  // Apply extensions
  applyExtensions(config, extensions);

  // Load sandbox configuration
  if (args.sandbox !== undefined || args['sandbox-image']) {
    const sandboxConfig = await loadSandboxConfig(
      args.sandbox,
      args['sandbox-image'],
    );
    if (sandboxConfig) {
      config.setSandbox(sandboxConfig);
    }
  }

  // Load hierarchical memory
  try {
    const memory = await loadServerHierarchicalMemory(
      config.getWorkspaceRoot(),
      getCurrentGeminiMdFilename(),
    );
    config.setMemory(memory);
  } catch (error) {
    logger.warn('Failed to load hierarchical memory:', error);
  }

  return config;
}

function loadEnvFiles() {
  const envFiles = ['.env', '.env.local'];
  
  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      dotenv.config({ path: envPath });
      logger.debug(`Loaded environment variables from ${envFile}`);
    }
  }

  // Also check home directory
  const homeEnvPath = path.join(os.homedir(), '.arien', '.env');
  if (fs.existsSync(homeEnvPath)) {
    dotenv.config({ path: homeEnvPath });
    logger.debug('Loaded environment variables from ~/.arien/.env');
  }
}



function applyExtensions(config: Config, extensions: Extension[]) {
  for (const extension of extensions) {
    try {
      extension.configure?.(config);
      logger.debug(`Applied extension: ${extension.name}`);
    } catch (error) {
      logger.error(`Failed to apply extension ${extension.name}:`, error);
    }
  }
}

/**
 * Convert Settings object to ConfigOptions for unified configuration
 */
function convertSettingsToConfigOptions(settings: Settings, sessionId: string): any {
  return {
    sessionId,
    model: settings.model,
    embeddingModel: settings.embeddingModel,
    debugMode: settings.debugMode,
    includeAllFiles: settings.includeAllFiles,
    showMemoryUsage: settings.showMemoryUsage,
    approvalMode: settings.approvalMode,
    telemetryEnabled: settings.telemetryEnabled,
    checkpointingEnabled: settings.checkpointingEnabled,
    telemetryTarget: settings.telemetryTarget,
    telemetryOtlpEndpoint: settings.telemetryOtlpEndpoint,
    telemetryLogPrompts: settings.telemetryLogPrompts,
    excludeTools: settings.excludeTools,
    includeTools: settings.includeTools,
    geminiMdFilename: settings.geminiMdFilename,
    theme: settings.theme,
    selectedAuthType: settings.selectedAuthType,
    usageStatisticsEnabled: settings.usageStatisticsEnabled,
    preferredEditor: settings.preferredEditor,
    accessibility: settings.accessibility,
    telemetry: settings.telemetry,
    bugCommand: settings.bugCommand,
    checkpointing: settings.checkpointing,
    autoConfigureMaxOldSpaceSize: settings.autoConfigureMaxOldSpaceSize,
    fileFiltering: settings.fileFiltering,
    hideWindowTitle: settings.hideWindowTitle,
    hideTips: settings.hideTips,
  };
}
