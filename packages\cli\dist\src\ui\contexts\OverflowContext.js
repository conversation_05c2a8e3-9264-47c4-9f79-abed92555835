import { jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { createContext, useContext, useState } from "react";
const OverflowContext = createContext(void 0);
const OverflowActionsContext = createContext(void 0);
const OverflowProvider = ({
  children,
  defaultMaxLines = 20
}) => {
  const [overflow, setOverflowState] = useState({
    isOverflowing: false,
    maxLines: defaultMaxLines,
    currentLines: 0,
    showMoreAvailable: false
  });
  const [overflowingIds, setOverflowingIds] = useState(/* @__PURE__ */ new Set());
  const setOverflow = (state) => {
    setOverflowState(state);
  };
  const setMaxLines = (maxLines) => {
    setOverflowState((prev) => ({
      ...prev,
      maxLines,
      isOverflowing: prev.currentLines > maxLines
    }));
  };
  const setCurrentLines = (currentLines) => {
    setOverflowState((prev) => ({
      ...prev,
      currentLines,
      isOverflowing: currentLines > prev.maxLines,
      showMoreAvailable: currentLines > prev.maxLines
    }));
  };
  const toggleShowMore = () => {
    setOverflowState((prev) => ({
      ...prev,
      maxLines: prev.isOverflowing ? prev.currentLines : Math.min(prev.maxLines, 20),
      isOverflowing: !prev.isOverflowing
    }));
  };
  const resetOverflow = () => {
    setOverflowState({
      isOverflowing: false,
      maxLines: defaultMaxLines,
      currentLines: 0,
      showMoreAvailable: false
    });
  };
  const addOverflowingId = (id) => {
    setOverflowingIds((prev) => /* @__PURE__ */ new Set([...prev, id]));
  };
  const removeOverflowingId = (id) => {
    setOverflowingIds((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };
  const getOverflowingIds = () => {
    return Array.from(overflowingIds);
  };
  const value = {
    overflow,
    setOverflow,
    setMaxLines,
    setCurrentLines,
    toggleShowMore,
    resetOverflow
  };
  const actionsValue = {
    addOverflowingId,
    removeOverflowingId,
    getOverflowingIds
  };
  return /* @__PURE__ */ jsx(OverflowContext.Provider, { value, children: /* @__PURE__ */ jsx(OverflowActionsContext.Provider, { value: actionsValue, children }) });
};
const useOverflow = () => {
  const context = useContext(OverflowContext);
  if (!context) {
    throw new Error("useOverflow must be used within an OverflowProvider");
  }
  return context;
};
const useOverflowActions = () => {
  const context = useContext(OverflowActionsContext);
  if (!context) {
    throw new Error("useOverflowActions must be used within an OverflowProvider");
  }
  return context;
};
export {
  OverflowProvider,
  useOverflow,
  useOverflowActions
};
//# sourceMappingURL=OverflowContext.js.map
