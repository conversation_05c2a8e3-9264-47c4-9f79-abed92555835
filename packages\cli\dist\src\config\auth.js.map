{"version": 3, "sources": ["../../../src/config/auth.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { AuthType } from '@arien/arien-cli-core';\nimport { AuthSettings } from '../ui/types.js';\n\n/**\n * Maps UI-friendly auth method keys to system AuthType enum values\n */\nexport const mapAuthMethodToAuthType = (method: AuthSettings['method']): AuthType => {\n  switch (method) {\n    case 'google':\n      return AuthType.LOGIN_WITH_GOOGLE;\n    case 'api_key':\n      return AuthType.USE_GEMINI;\n    case 'vertex':\n      return AuthType.USE_VERTEX_AI;\n    default:\n      return AuthType.USE_GEMINI;\n  }\n};\n\n/**\n * Maps system AuthType enum values to UI-friendly auth method keys\n */\nexport const mapAuthTypeToAuthMethod = (authType: AuthType): AuthSettings['method'] => {\n  switch (authType) {\n    case AuthType.LOGIN_WITH_GOOGLE:\n      return 'google';\n    case AuthType.USE_GEMINI:\n      return 'api_key';\n    case AuthType.USE_VERTEX_AI:\n      return 'vertex';\n    default:\n      return 'api_key';\n  }\n};\n\nexport const validateAuthMethod = (authMethod: string): string | null => {\n  if (authMethod === AuthType.LOGIN_WITH_GOOGLE) {\n    return null;\n  }\n\n  if (authMethod === AuthType.USE_GEMINI) {\n    if (!process.env.GEMINI_API_KEY) {\n      return 'GEMINI_API_KEY environment variable not found. Add that to your .env and try again, no reload needed!';\n    }\n    return null;\n  }\n\n  if (authMethod === AuthType.USE_VERTEX_AI) {\n    const hasVertexProjectLocationConfig =\n      !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;\n    const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;\n    if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {\n      return (\n        'Must specify GOOGLE_GENAI_USE_VERTEXAI=true and either:\\n' +\n        '• GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\\n' +\n        '• GOOGLE_API_KEY environment variable (if using express mode).\\n' +\n        'Update your .env and try again, no reload needed!'\n      );\n    }\n    return null;\n  }\n\n  return 'Invalid auth method selected.';\n};\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,gBAAgB;AAMlB,MAAM,0BAA0B,CAAC,WAA6C;AACnF,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO,SAAS;AAAA,IAClB;AACE,aAAO,SAAS;AAAA,EACpB;AACF;AAKO,MAAM,0BAA0B,CAAC,aAA+C;AACrF,UAAQ,UAAU;AAAA,IAChB,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT,KAAK,SAAS;AACZ,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAEO,MAAM,qBAAqB,CAAC,eAAsC;AACvE,MAAI,eAAe,SAAS,mBAAmB;AAC7C,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,SAAS,YAAY;AACtC,QAAI,CAAC,QAAQ,IAAI,gBAAgB;AAC/B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,SAAS,eAAe;AACzC,UAAM,iCACJ,CAAC,CAAC,QAAQ,IAAI,wBAAwB,CAAC,CAAC,QAAQ,IAAI;AACtD,UAAM,kBAAkB,CAAC,CAAC,QAAQ,IAAI;AACtC,QAAI,CAAC,kCAAkC,CAAC,iBAAiB;AACvD,aACE;AAAA,IAKJ;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": []}