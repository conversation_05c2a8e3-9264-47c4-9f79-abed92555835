{"version": 3, "sources": ["../../../src/config/config.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { homedir } from 'os';\nimport { GoogleAuth } from 'google-auth-library';\nimport {\n  ContentGenerator,\n  createContentGenerator,\n  createContentGeneratorConfig,\n} from '../core/contentGenerator.js';\nimport { FileDiscoveryService } from '../services/fileDiscoveryService.js';\nimport { GitService } from '../services/gitService.js';\nimport { ToolRegistry } from '../tools/tool-registry.js';\nimport { DEFAULT_GEMINI_MODEL } from './models.js';\nimport stripJsonComments from 'strip-json-comments';\n\n// Settings constants\nexport const SETTINGS_DIRECTORY_NAME = '.arien';\nexport const USER_SETTINGS_DIR = path.join(homedir(), SETTINGS_DIRECTORY_NAME);\nexport const USER_SETTINGS_PATH = path.join(USER_SETTINGS_DIR, 'settings.json');\n\nexport enum AuthType {\n  USE_GEMINI = 'USE_GEMINI',\n  USE_OAUTH = 'USE_OAUTH',\n  USE_SERVICE_ACCOUNT = 'USE_SERVICE_ACCOUNT',\n  USE_VERTEX = 'USE_VERTEX',\n}\n\nexport enum ApprovalMode {\n  ALWAYS = 'always',\n  NEVER = 'never',\n  ONCE = 'once',\n}\n\nexport enum TelemetryTarget {\n  CONSOLE = 'console',\n  OTLP = 'otlp',\n}\n\nexport enum SettingScope {\n  User = 'User',\n  Workspace = 'Workspace',\n}\n\nexport interface MCPServerConfig {\n  command: string;\n  args?: string[];\n  env?: Record<string, string>;\n}\n\nexport interface BugCommandSettings {\n  enabled?: boolean;\n  command?: string;\n}\n\nexport interface TelemetrySettings {\n  enabled?: boolean;\n  endpoint?: string;\n  target?: TelemetryTarget;\n  otlpEndpoint?: string;\n  logPrompts?: boolean;\n}\n\nexport interface AccessibilitySettings {\n  disableLoadingPhrases?: boolean;\n}\n\nexport interface CheckpointingSettings {\n  enabled?: boolean;\n}\n\nexport interface SettingsError {\n  message: string;\n  path: string;\n}\n\nexport interface SettingsFile {\n  settings: ConfigOptions;\n  path: string;\n}\n\nexport interface ContentGeneratorConfig {\n  authType?: AuthType;\n  apiKey?: string;\n  projectId?: string;\n  location?: string;\n  credentials?: any;\n}\n\nexport interface ConfigOptions {\n  // Core configuration\n  model?: string;\n  debug?: boolean;\n  allFiles?: boolean;\n  question?: string;\n  workspaceRoot?: string;\n  approvalMode?: ApprovalMode;\n  sandbox?: boolean | string;\n  coreTools?: string[];\n  excludeTools?: string[];\n  toolDiscoveryCommand?: string;\n  toolCallCommand?: string;\n  mcpServerCommand?: string;\n  mcpServers?: Record<string, MCPServerConfig>;\n  contextFileName?: string | string[];\n  checkpointingEnabled?: boolean;\n  userMemory?: string;\n  contentGeneratorConfig?: ContentGeneratorConfig;\n  telemetrySettings?: TelemetrySettings;\n  bugCommandSettings?: BugCommandSettings;\n\n  // Extended settings from Settings interface\n  theme?: string;\n  selectedAuthType?: AuthType;\n  embeddingModel?: string;\n  debugMode?: boolean; // Alias for debug\n  includeAllFiles?: boolean; // Alias for allFiles\n  showMemoryUsage?: boolean;\n  telemetryEnabled?: boolean;\n  telemetryTarget?: TelemetryTarget;\n  telemetryOtlpEndpoint?: string;\n  telemetryLogPrompts?: boolean;\n  includeTools?: string[];\n  geminiMdFilename?: string;\n  accessibility?: AccessibilitySettings;\n  telemetry?: TelemetrySettings;\n  usageStatisticsEnabled?: boolean;\n  preferredEditor?: string;\n  bugCommand?: BugCommandSettings;\n  checkpointing?: CheckpointingSettings;\n  autoConfigureMaxOldSpaceSize?: boolean;\n  fileFiltering?: {\n    respectGitIgnore?: boolean;\n    enableRecursiveFileSearch?: boolean;\n  };\n  hideWindowTitle?: boolean;\n  hideTips?: boolean;\n\n  // Session-specific options\n  sessionId?: string;\n}\n\nexport class Config {\n  private options: ConfigOptions;\n  private contentGenerator?: ContentGenerator;\n  private fileService?: FileDiscoveryService;\n  private gitService?: GitService;\n  private toolRegistry?: ToolRegistry;\n  private userSettingsFile?: SettingsFile;\n  private workspaceSettingsFile?: SettingsFile;\n  private settingsErrors: SettingsError[] = [];\n\n  constructor(options: ConfigOptions = {}) {\n    this.options = {\n      model: DEFAULT_GEMINI_MODEL,\n      debug: false,\n      allFiles: false,\n      workspaceRoot: process.cwd(),\n      approvalMode: ApprovalMode.ONCE,\n      sandbox: false,\n      coreTools: [],\n      excludeTools: [],\n      contextFileName: 'ARIEN.md',\n      checkpointingEnabled: true,\n      ...options,\n    };\n  }\n\n  /**\n   * Create a Config instance with hierarchical settings loading\n   */\n  static async createWithSettings(workspaceRoot?: string, additionalOptions: ConfigOptions = {}): Promise<Config> {\n    const config = new Config({ workspaceRoot: workspaceRoot || process.cwd(), ...additionalOptions });\n    await config.loadSettings();\n    return config;\n  }\n\n  /**\n   * Load settings from user and workspace settings files\n   */\n  private async loadSettings(): Promise<void> {\n    const errors: SettingsError[] = [];\n\n    // Load user settings\n    this.userSettingsFile = this.loadSettingsFile(USER_SETTINGS_PATH, errors);\n\n    // Load workspace settings\n    const workspaceSettingsPath = path.join(this.getWorkspaceRoot(), SETTINGS_DIRECTORY_NAME, 'settings.json');\n    this.workspaceSettingsFile = this.loadSettingsFile(workspaceSettingsPath, errors);\n\n    this.settingsErrors = errors;\n\n    // Merge settings into options (workspace overrides user)\n    const mergedSettings = {\n      ...this.userSettingsFile.settings,\n      ...this.workspaceSettingsFile.settings,\n    };\n\n    // Apply merged settings to options\n    this.options = { ...this.options, ...mergedSettings };\n  }\n\n  /**\n   * Load a settings file\n   */\n  private loadSettingsFile(filePath: string, errors: SettingsError[]): SettingsFile {\n    const defaultSettings: ConfigOptions = {};\n\n    if (!fs.existsSync(filePath)) {\n      return { settings: defaultSettings, path: filePath };\n    }\n\n    try {\n      const content = fs.readFileSync(filePath, 'utf8');\n      const cleanedContent = stripJsonComments(content);\n      const settings = JSON.parse(cleanedContent) as ConfigOptions;\n      return { settings, path: filePath };\n    } catch (error) {\n      errors.push({\n        message: `Failed to parse settings file: ${error instanceof Error ? error.message : String(error)}`,\n        path: filePath,\n      });\n      return { settings: defaultSettings, path: filePath };\n    }\n  }\n\n  /**\n   * Set a configuration value and optionally persist it to settings\n   */\n  setValue(scope: SettingScope, key: keyof ConfigOptions, value: any): void {\n    // Update runtime options\n    (this.options as any)[key] = value;\n\n    // Update appropriate settings file\n    const targetFile = scope === SettingScope.User ? this.userSettingsFile : this.workspaceSettingsFile;\n    if (targetFile) {\n      (targetFile.settings as any)[key] = value;\n\n      // Write to file\n      try {\n        fs.mkdirSync(path.dirname(targetFile.path), { recursive: true });\n        fs.writeFileSync(\n          targetFile.path,\n          JSON.stringify(targetFile.settings, null, 2),\n        );\n      } catch (error) {\n        console.error(`Failed to save settings to ${targetFile.path}:`, error);\n      }\n    }\n  }\n\n  /**\n   * Get a configuration value\n   */\n  getValue(key: keyof ConfigOptions): any {\n    return (this.options as any)[key];\n  }\n\n  /**\n   * Get settings errors from loading\n   */\n  getSettingsErrors(): SettingsError[] {\n    return this.settingsErrors;\n  }\n\n  /**\n   * Create default user settings if they don't exist\n   */\n  static createUserSettingsIfNotExists(): void {\n    if (!fs.existsSync(USER_SETTINGS_PATH)) {\n      const defaultSettings: ConfigOptions = {\n        theme: 'default-dark',\n        autoConfigureMaxOldSpaceSize: true,\n        fileFiltering: {\n          respectGitIgnore: true,\n          enableRecursiveFileSearch: true,\n        },\n        telemetry: {\n          enabled: true,\n        },\n        checkpointing: {\n          enabled: true,\n        },\n        accessibility: {\n          disableLoadingPhrases: false,\n        },\n      };\n\n      try {\n        fs.mkdirSync(path.dirname(USER_SETTINGS_PATH), { recursive: true });\n        fs.writeFileSync(\n          USER_SETTINGS_PATH,\n          JSON.stringify(defaultSettings, null, 2),\n        );\n      } catch (error) {\n        console.error('Failed to create default user settings:', error);\n      }\n    }\n  }\n\n  // Getters\n  getModel(): string {\n    return this.options.model || DEFAULT_GEMINI_MODEL;\n  }\n\n  getDebugMode(): boolean {\n    // Handle both debug and debugMode properties (Settings compatibility)\n    return this.options.debug || this.options.debugMode || false;\n  }\n\n  getDebug(): boolean {\n    return this.getDebugMode();\n  }\n\n  getAllFiles(): boolean {\n    // Handle both allFiles and includeAllFiles properties (Settings compatibility)\n    return this.options.allFiles || this.options.includeAllFiles || false;\n  }\n\n  getQuestion(): string {\n    return this.options.question || '';\n  }\n\n  getWorkspaceRoot(): string {\n    return this.options.workspaceRoot || process.cwd();\n  }\n\n  getApprovalMode(): ApprovalMode {\n    return this.options.approvalMode || ApprovalMode.ONCE;\n  }\n\n  getSandbox(): boolean | string {\n    return this.options.sandbox || false;\n  }\n\n  getCoreTools(): string[] {\n    return this.options.coreTools || [];\n  }\n\n  getExcludeTools(): string[] {\n    return this.options.excludeTools || [];\n  }\n\n  getToolDiscoveryCommand(): string | undefined {\n    return this.options.toolDiscoveryCommand;\n  }\n\n  getToolCallCommand(): string | undefined {\n    return this.options.toolCallCommand;\n  }\n\n  getMcpServerCommand(): string | undefined {\n    return this.options.mcpServerCommand;\n  }\n\n  getMcpServers(): Record<string, MCPServerConfig> {\n    return this.options.mcpServers || {};\n  }\n\n  getContextFileName(): string | string[] {\n    return this.options.contextFileName || 'ARIEN.md';\n  }\n\n  getCheckpointingEnabled(): boolean {\n    return this.options.checkpointingEnabled !== false;\n  }\n\n  getUserMemory(): string {\n    return this.options.userMemory || '';\n  }\n\n  getContentGeneratorConfig(): ContentGeneratorConfig | undefined {\n    return this.options.contentGeneratorConfig;\n  }\n\n  getTelemetrySettings(): TelemetrySettings {\n    // Merge telemetrySettings with individual telemetry properties for Settings compatibility\n    const base = this.options.telemetrySettings || { enabled: false };\n    return {\n      ...base,\n      enabled: base.enabled ?? this.options.telemetryEnabled ?? false,\n      target: base.target ?? this.options.telemetryTarget,\n      otlpEndpoint: base.otlpEndpoint ?? this.options.telemetryOtlpEndpoint,\n      logPrompts: base.logPrompts ?? this.options.telemetryLogPrompts,\n    };\n  }\n\n  getBugCommandSettings(): BugCommandSettings {\n    return this.options.bugCommandSettings || this.options.bugCommand || { enabled: false };\n  }\n\n  // Additional getters for Settings properties\n  getTheme(): string | undefined {\n    return this.options.theme;\n  }\n\n  getSelectedAuthType(): AuthType | undefined {\n    return this.options.selectedAuthType;\n  }\n\n  getEmbeddingModel(): string | undefined {\n    return this.options.embeddingModel;\n  }\n\n  getShowMemoryUsage(): boolean {\n    return this.options.showMemoryUsage || false;\n  }\n\n  getIncludeTools(): string[] {\n    return this.options.includeTools || [];\n  }\n\n  getGeminiMdFilename(): string | undefined {\n    return this.options.geminiMdFilename;\n  }\n\n  getAccessibilitySettings(): AccessibilitySettings {\n    return this.options.accessibility || { disableLoadingPhrases: false };\n  }\n\n  getUsageStatisticsEnabled(): boolean {\n    return this.options.usageStatisticsEnabled || false;\n  }\n\n  getPreferredEditor(): string | undefined {\n    return this.options.preferredEditor;\n  }\n\n  getCheckpointingSettings(): CheckpointingSettings {\n    return this.options.checkpointing || { enabled: true };\n  }\n\n  getAutoConfigureMaxOldSpaceSize(): boolean {\n    return this.options.autoConfigureMaxOldSpaceSize || false;\n  }\n\n  getFileFiltering(): { respectGitIgnore?: boolean; enableRecursiveFileSearch?: boolean } {\n    return this.options.fileFiltering || { respectGitIgnore: true, enableRecursiveFileSearch: true };\n  }\n\n  getHideWindowTitle(): boolean {\n    return this.options.hideWindowTitle || false;\n  }\n\n  getHideTips(): boolean {\n    return this.options.hideTips || false;\n  }\n\n  getSessionId(): string | undefined {\n    return this.options.sessionId;\n  }\n\n  // Service getters with lazy initialization\n  async getContentGenerator(): Promise<ContentGenerator> {\n    if (!this.contentGenerator) {\n      const config = this.getContentGeneratorConfig();\n      if (!config) {\n        throw new Error('No content generator configuration found');\n      }\n\n      const generatorConfig = await createContentGeneratorConfig(\n        this.getModel(),\n        config.apiKey,\n        config.authType as any, // Type conversion needed due to enum mismatch\n      );\n\n      this.contentGenerator = await createContentGenerator(generatorConfig);\n    }\n    return this.contentGenerator;\n  }\n\n  getFileService(): FileDiscoveryService {\n    if (!this.fileService) {\n      this.fileService = new FileDiscoveryService(this);\n    }\n    return this.fileService;\n  }\n\n  getGitService(): GitService {\n    if (!this.gitService) {\n      this.gitService = new GitService(this);\n    }\n    return this.gitService;\n  }\n\n  getToolRegistry(): ToolRegistry {\n    if (!this.toolRegistry) {\n      this.toolRegistry = new ToolRegistry(this);\n    }\n    return this.toolRegistry;\n  }\n\n  // Setters\n  setModel(model: string): void {\n    this.options.model = model;\n  }\n\n  setDebugMode(debug: boolean): void {\n    this.options.debug = debug;\n  }\n\n  setApprovalMode(mode: ApprovalMode): void {\n    this.options.approvalMode = mode;\n  }\n\n  setUserMemory(memory: string): void {\n    this.options.userMemory = memory;\n  }\n\n  setContentGeneratorConfig(config: ContentGeneratorConfig): void {\n    this.options.contentGeneratorConfig = config;\n    // Reset content generator to pick up new config\n    this.contentGenerator = undefined;\n  }\n\n  setTelemetrySettings(settings: TelemetrySettings): void {\n    this.options.telemetrySettings = settings;\n  }\n\n  setBugCommandSettings(settings: BugCommandSettings): void {\n    this.options.bugCommandSettings = settings;\n  }\n\n  setMcpServers(servers: Record<string, MCPServerConfig>): void {\n    this.options.mcpServers = servers;\n  }\n\n  setContextFileName(fileName: string | string[]): void {\n    this.options.contextFileName = fileName;\n  }\n\n  setCheckpointingEnabled(enabled: boolean): void {\n    this.options.checkpointingEnabled = enabled;\n  }\n\n  // Additional setters for Settings properties\n  setTheme(theme: string): void {\n    this.options.theme = theme;\n  }\n\n  setSelectedAuthType(authType: AuthType): void {\n    this.options.selectedAuthType = authType;\n  }\n\n  setEmbeddingModel(model: string): void {\n    this.options.embeddingModel = model;\n  }\n\n  setIncludeAllFiles(include: boolean): void {\n    this.options.includeAllFiles = include;\n    // Also set allFiles for backward compatibility\n    this.options.allFiles = include;\n  }\n\n  setShowMemoryUsage(show: boolean): void {\n    this.options.showMemoryUsage = show;\n  }\n\n  setTelemetryEnabled(enabled: boolean): void {\n    this.options.telemetryEnabled = enabled;\n    // Also update telemetrySettings for consistency\n    if (!this.options.telemetrySettings) {\n      this.options.telemetrySettings = {};\n    }\n    this.options.telemetrySettings.enabled = enabled;\n  }\n\n  setTelemetryTarget(target: TelemetryTarget): void {\n    this.options.telemetryTarget = target;\n  }\n\n  setTelemetryOtlpEndpoint(endpoint: string): void {\n    this.options.telemetryOtlpEndpoint = endpoint;\n  }\n\n  setTelemetryLogPrompts(log: boolean): void {\n    this.options.telemetryLogPrompts = log;\n  }\n\n  setIncludeTools(tools: string[]): void {\n    this.options.includeTools = tools;\n  }\n\n  setGeminiMdFilename(filename: string): void {\n    this.options.geminiMdFilename = filename;\n  }\n\n  setAccessibilitySettings(settings: AccessibilitySettings): void {\n    this.options.accessibility = settings;\n  }\n\n  setUsageStatisticsEnabled(enabled: boolean): void {\n    this.options.usageStatisticsEnabled = enabled;\n  }\n\n  setPreferredEditor(editor: string): void {\n    this.options.preferredEditor = editor;\n  }\n\n  setCheckpointingSettings(settings: CheckpointingSettings): void {\n    this.options.checkpointing = settings;\n  }\n\n  setAutoConfigureMaxOldSpaceSize(configure: boolean): void {\n    this.options.autoConfigureMaxOldSpaceSize = configure;\n  }\n\n  setFileFiltering(settings: { respectGitIgnore?: boolean; enableRecursiveFileSearch?: boolean }): void {\n    this.options.fileFiltering = settings;\n  }\n\n  setHideWindowTitle(hide: boolean): void {\n    this.options.hideWindowTitle = hide;\n  }\n\n  setHideTips(hide: boolean): void {\n    this.options.hideTips = hide;\n  }\n\n  setSessionId(sessionId: string): void {\n    this.options.sessionId = sessionId;\n  }\n\n  // Convenience setters that handle both old and new property names\n  setQuestion(question: string): void {\n    this.options.question = question;\n  }\n\n  setSandbox(sandbox: boolean | string): void {\n    this.options.sandbox = sandbox;\n  }\n\n  setMemory(memory: string): void {\n    this.options.userMemory = memory;\n  }\n\n  // Utility methods\n  isInteractive(): boolean {\n    return process.stdin.isTTY && !this.getQuestion();\n  }\n\n  shouldUseAllFiles(): boolean {\n    return this.getAllFiles();\n  }\n\n  shouldUseSandbox(): boolean {\n    return Boolean(this.getSandbox());\n  }\n\n  clone(): Config {\n    return new Config({ ...this.options });\n  }\n}\n\n// Global session ID for telemetry and logging\nimport { sessionId } from '../utils/session.js';\nexport { sessionId };\n\n// Logging function for user prompts\nexport function logUserPrompt(prompt: string): void {\n  if (process.env.DEBUG) {\n    console.log(`[${sessionId}] User prompt: ${prompt}`);\n  }\n}\n\n// Error message utility\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return String(error);\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,YAAY,QAAQ;AACpB,YAAY,UAAU;AACtB,SAAS,eAAe;AAExB;AAAA,EAEE;AAAA,EACA;AAAA,OACK;AACP,SAAS,4BAA4B;AACrC,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAC7B,SAAS,4BAA4B;AACrC,OAAO,uBAAuB;AAGvB,MAAM,0BAA0B;AAChC,MAAM,oBAAoB,KAAK,KAAK,QAAQ,GAAG,uBAAuB;AACtE,MAAM,qBAAqB,KAAK,KAAK,mBAAmB,eAAe;AAEvE,IAAK,WAAL,kBAAKA,cAAL;AACL,EAAAA,UAAA,gBAAa;AACb,EAAAA,UAAA,eAAY;AACZ,EAAAA,UAAA,yBAAsB;AACtB,EAAAA,UAAA,gBAAa;AAJH,SAAAA;AAAA,GAAA;AAOL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,WAAQ;AACR,EAAAA,cAAA,UAAO;AAHG,SAAAA;AAAA,GAAA;AAML,IAAK,kBAAL,kBAAKC,qBAAL;AACL,EAAAA,iBAAA,aAAU;AACV,EAAAA,iBAAA,UAAO;AAFG,SAAAA;AAAA,GAAA;AAKL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,UAAO;AACP,EAAAA,cAAA,eAAY;AAFF,SAAAA;AAAA,GAAA;AAuGL,MAAM,OAAO;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAkC,CAAC;AAAA,EAE3C,YAAY,UAAyB,CAAC,GAAG;AACvC,SAAK,UAAU;AAAA,MACb,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,eAAe,QAAQ,IAAI;AAAA,MAC3B,cAAc;AAAA,MACd,SAAS;AAAA,MACT,WAAW,CAAC;AAAA,MACZ,cAAc,CAAC;AAAA,MACf,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,MACtB,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,mBAAmB,eAAwB,oBAAmC,CAAC,GAAoB;AAC9G,UAAM,SAAS,IAAI,OAAO,EAAE,eAAe,iBAAiB,QAAQ,IAAI,GAAG,GAAG,kBAAkB,CAAC;AACjG,UAAM,OAAO,aAAa;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,eAA8B;AAC1C,UAAM,SAA0B,CAAC;AAGjC,SAAK,mBAAmB,KAAK,iBAAiB,oBAAoB,MAAM;AAGxE,UAAM,wBAAwB,KAAK,KAAK,KAAK,iBAAiB,GAAG,yBAAyB,eAAe;AACzG,SAAK,wBAAwB,KAAK,iBAAiB,uBAAuB,MAAM;AAEhF,SAAK,iBAAiB;AAGtB,UAAM,iBAAiB;AAAA,MACrB,GAAG,KAAK,iBAAiB;AAAA,MACzB,GAAG,KAAK,sBAAsB;AAAA,IAChC;AAGA,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAG,eAAe;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKQ,iBAAiB,UAAkB,QAAuC;AAChF,UAAM,kBAAiC,CAAC;AAExC,QAAI,CAAC,GAAG,WAAW,QAAQ,GAAG;AAC5B,aAAO,EAAE,UAAU,iBAAiB,MAAM,SAAS;AAAA,IACrD;AAEA,QAAI;AACF,YAAM,UAAU,GAAG,aAAa,UAAU,MAAM;AAChD,YAAM,iBAAiB,kBAAkB,OAAO;AAChD,YAAM,WAAW,KAAK,MAAM,cAAc;AAC1C,aAAO,EAAE,UAAU,MAAM,SAAS;AAAA,IACpC,SAAS,OAAO;AACd,aAAO,KAAK;AAAA,QACV,SAAS,kCAAkC,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK,CAAC;AAAA,QACjG,MAAM;AAAA,MACR,CAAC;AACD,aAAO,EAAE,UAAU,iBAAiB,MAAM,SAAS;AAAA,IACrD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAqB,KAA0B,OAAkB;AAExE,IAAC,KAAK,QAAgB,GAAG,IAAI;AAG7B,UAAM,aAAa,UAAU,oBAAoB,KAAK,mBAAmB,KAAK;AAC9E,QAAI,YAAY;AACd,MAAC,WAAW,SAAiB,GAAG,IAAI;AAGpC,UAAI;AACF,WAAG,UAAU,KAAK,QAAQ,WAAW,IAAI,GAAG,EAAE,WAAW,KAAK,CAAC;AAC/D,WAAG;AAAA,UACD,WAAW;AAAA,UACX,KAAK,UAAU,WAAW,UAAU,MAAM,CAAC;AAAA,QAC7C;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,8BAA8B,WAAW,IAAI,KAAK,KAAK;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,KAA+B;AACtC,WAAQ,KAAK,QAAgB,GAAG;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAqC;AACnC,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,gCAAsC;AAC3C,QAAI,CAAC,GAAG,WAAW,kBAAkB,GAAG;AACtC,YAAM,kBAAiC;AAAA,QACrC,OAAO;AAAA,QACP,8BAA8B;AAAA,QAC9B,eAAe;AAAA,UACb,kBAAkB;AAAA,UAClB,2BAA2B;AAAA,QAC7B;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,QACX;AAAA,QACA,eAAe;AAAA,UACb,SAAS;AAAA,QACX;AAAA,QACA,eAAe;AAAA,UACb,uBAAuB;AAAA,QACzB;AAAA,MACF;AAEA,UAAI;AACF,WAAG,UAAU,KAAK,QAAQ,kBAAkB,GAAG,EAAE,WAAW,KAAK,CAAC;AAClE,WAAG;AAAA,UACD;AAAA,UACA,KAAK,UAAU,iBAAiB,MAAM,CAAC;AAAA,QACzC;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,2CAA2C,KAAK;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,WAAmB;AACjB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AAAA,EAEA,eAAwB;AAEtB,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,aAAa;AAAA,EACzD;AAAA,EAEA,WAAoB;AAClB,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EAEA,cAAuB;AAErB,WAAO,KAAK,QAAQ,YAAY,KAAK,QAAQ,mBAAmB;AAAA,EAClE;AAAA,EAEA,cAAsB;AACpB,WAAO,KAAK,QAAQ,YAAY;AAAA,EAClC;AAAA,EAEA,mBAA2B;AACzB,WAAO,KAAK,QAAQ,iBAAiB,QAAQ,IAAI;AAAA,EACnD;AAAA,EAEA,kBAAgC;AAC9B,WAAO,KAAK,QAAQ,gBAAgB;AAAA,EACtC;AAAA,EAEA,aAA+B;AAC7B,WAAO,KAAK,QAAQ,WAAW;AAAA,EACjC;AAAA,EAEA,eAAyB;AACvB,WAAO,KAAK,QAAQ,aAAa,CAAC;AAAA,EACpC;AAAA,EAEA,kBAA4B;AAC1B,WAAO,KAAK,QAAQ,gBAAgB,CAAC;AAAA,EACvC;AAAA,EAEA,0BAA8C;AAC5C,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,qBAAyC;AACvC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,sBAA0C;AACxC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,gBAAiD;AAC/C,WAAO,KAAK,QAAQ,cAAc,CAAC;AAAA,EACrC;AAAA,EAEA,qBAAwC;AACtC,WAAO,KAAK,QAAQ,mBAAmB;AAAA,EACzC;AAAA,EAEA,0BAAmC;AACjC,WAAO,KAAK,QAAQ,yBAAyB;AAAA,EAC/C;AAAA,EAEA,gBAAwB;AACtB,WAAO,KAAK,QAAQ,cAAc;AAAA,EACpC;AAAA,EAEA,4BAAgE;AAC9D,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,uBAA0C;AAExC,UAAM,OAAO,KAAK,QAAQ,qBAAqB,EAAE,SAAS,MAAM;AAChE,WAAO;AAAA,MACL,GAAG;AAAA,MACH,SAAS,KAAK,WAAW,KAAK,QAAQ,oBAAoB;AAAA,MAC1D,QAAQ,KAAK,UAAU,KAAK,QAAQ;AAAA,MACpC,cAAc,KAAK,gBAAgB,KAAK,QAAQ;AAAA,MAChD,YAAY,KAAK,cAAc,KAAK,QAAQ;AAAA,IAC9C;AAAA,EACF;AAAA,EAEA,wBAA4C;AAC1C,WAAO,KAAK,QAAQ,sBAAsB,KAAK,QAAQ,cAAc,EAAE,SAAS,MAAM;AAAA,EACxF;AAAA;AAAA,EAGA,WAA+B;AAC7B,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,sBAA4C;AAC1C,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,oBAAwC;AACtC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,qBAA8B;AAC5B,WAAO,KAAK,QAAQ,mBAAmB;AAAA,EACzC;AAAA,EAEA,kBAA4B;AAC1B,WAAO,KAAK,QAAQ,gBAAgB,CAAC;AAAA,EACvC;AAAA,EAEA,sBAA0C;AACxC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,2BAAkD;AAChD,WAAO,KAAK,QAAQ,iBAAiB,EAAE,uBAAuB,MAAM;AAAA,EACtE;AAAA,EAEA,4BAAqC;AACnC,WAAO,KAAK,QAAQ,0BAA0B;AAAA,EAChD;AAAA,EAEA,qBAAyC;AACvC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,2BAAkD;AAChD,WAAO,KAAK,QAAQ,iBAAiB,EAAE,SAAS,KAAK;AAAA,EACvD;AAAA,EAEA,kCAA2C;AACzC,WAAO,KAAK,QAAQ,gCAAgC;AAAA,EACtD;AAAA,EAEA,mBAAwF;AACtF,WAAO,KAAK,QAAQ,iBAAiB,EAAE,kBAAkB,MAAM,2BAA2B,KAAK;AAAA,EACjG;AAAA,EAEA,qBAA8B;AAC5B,WAAO,KAAK,QAAQ,mBAAmB;AAAA,EACzC;AAAA,EAEA,cAAuB;AACrB,WAAO,KAAK,QAAQ,YAAY;AAAA,EAClC;AAAA,EAEA,eAAmC;AACjC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA,EAGA,MAAM,sBAAiD;AACrD,QAAI,CAAC,KAAK,kBAAkB;AAC1B,YAAM,SAAS,KAAK,0BAA0B;AAC9C,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAEA,YAAM,kBAAkB,MAAM;AAAA,QAC5B,KAAK,SAAS;AAAA,QACd,OAAO;AAAA,QACP,OAAO;AAAA;AAAA,MACT;AAEA,WAAK,mBAAmB,MAAM,uBAAuB,eAAe;AAAA,IACtE;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,iBAAuC;AACrC,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,qBAAqB,IAAI;AAAA,IAClD;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,gBAA4B;AAC1B,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,IAAI,WAAW,IAAI;AAAA,IACvC;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,kBAAgC;AAC9B,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,eAAe,IAAI,aAAa,IAAI;AAAA,IAC3C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAGA,SAAS,OAAqB;AAC5B,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA,EAEA,aAAa,OAAsB;AACjC,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA,EAEA,gBAAgB,MAA0B;AACxC,SAAK,QAAQ,eAAe;AAAA,EAC9B;AAAA,EAEA,cAAc,QAAsB;AAClC,SAAK,QAAQ,aAAa;AAAA,EAC5B;AAAA,EAEA,0BAA0B,QAAsC;AAC9D,SAAK,QAAQ,yBAAyB;AAEtC,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EAEA,qBAAqB,UAAmC;AACtD,SAAK,QAAQ,oBAAoB;AAAA,EACnC;AAAA,EAEA,sBAAsB,UAAoC;AACxD,SAAK,QAAQ,qBAAqB;AAAA,EACpC;AAAA,EAEA,cAAc,SAAgD;AAC5D,SAAK,QAAQ,aAAa;AAAA,EAC5B;AAAA,EAEA,mBAAmB,UAAmC;AACpD,SAAK,QAAQ,kBAAkB;AAAA,EACjC;AAAA,EAEA,wBAAwB,SAAwB;AAC9C,SAAK,QAAQ,uBAAuB;AAAA,EACtC;AAAA;AAAA,EAGA,SAAS,OAAqB;AAC5B,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA,EAEA,oBAAoB,UAA0B;AAC5C,SAAK,QAAQ,mBAAmB;AAAA,EAClC;AAAA,EAEA,kBAAkB,OAAqB;AACrC,SAAK,QAAQ,iBAAiB;AAAA,EAChC;AAAA,EAEA,mBAAmB,SAAwB;AACzC,SAAK,QAAQ,kBAAkB;AAE/B,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAAA,EAEA,mBAAmB,MAAqB;AACtC,SAAK,QAAQ,kBAAkB;AAAA,EACjC;AAAA,EAEA,oBAAoB,SAAwB;AAC1C,SAAK,QAAQ,mBAAmB;AAEhC,QAAI,CAAC,KAAK,QAAQ,mBAAmB;AACnC,WAAK,QAAQ,oBAAoB,CAAC;AAAA,IACpC;AACA,SAAK,QAAQ,kBAAkB,UAAU;AAAA,EAC3C;AAAA,EAEA,mBAAmB,QAA+B;AAChD,SAAK,QAAQ,kBAAkB;AAAA,EACjC;AAAA,EAEA,yBAAyB,UAAwB;AAC/C,SAAK,QAAQ,wBAAwB;AAAA,EACvC;AAAA,EAEA,uBAAuB,KAAoB;AACzC,SAAK,QAAQ,sBAAsB;AAAA,EACrC;AAAA,EAEA,gBAAgB,OAAuB;AACrC,SAAK,QAAQ,eAAe;AAAA,EAC9B;AAAA,EAEA,oBAAoB,UAAwB;AAC1C,SAAK,QAAQ,mBAAmB;AAAA,EAClC;AAAA,EAEA,yBAAyB,UAAuC;AAC9D,SAAK,QAAQ,gBAAgB;AAAA,EAC/B;AAAA,EAEA,0BAA0B,SAAwB;AAChD,SAAK,QAAQ,yBAAyB;AAAA,EACxC;AAAA,EAEA,mBAAmB,QAAsB;AACvC,SAAK,QAAQ,kBAAkB;AAAA,EACjC;AAAA,EAEA,yBAAyB,UAAuC;AAC9D,SAAK,QAAQ,gBAAgB;AAAA,EAC/B;AAAA,EAEA,gCAAgC,WAA0B;AACxD,SAAK,QAAQ,+BAA+B;AAAA,EAC9C;AAAA,EAEA,iBAAiB,UAAqF;AACpG,SAAK,QAAQ,gBAAgB;AAAA,EAC/B;AAAA,EAEA,mBAAmB,MAAqB;AACtC,SAAK,QAAQ,kBAAkB;AAAA,EACjC;AAAA,EAEA,YAAY,MAAqB;AAC/B,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAAA,EAEA,aAAaC,YAAyB;AACpC,SAAK,QAAQ,YAAYA;AAAA,EAC3B;AAAA;AAAA,EAGA,YAAY,UAAwB;AAClC,SAAK,QAAQ,WAAW;AAAA,EAC1B;AAAA,EAEA,WAAW,SAAiC;AAC1C,SAAK,QAAQ,UAAU;AAAA,EACzB;AAAA,EAEA,UAAU,QAAsB;AAC9B,SAAK,QAAQ,aAAa;AAAA,EAC5B;AAAA;AAAA,EAGA,gBAAyB;AACvB,WAAO,QAAQ,MAAM,SAAS,CAAC,KAAK,YAAY;AAAA,EAClD;AAAA,EAEA,oBAA6B;AAC3B,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EAEA,mBAA4B;AAC1B,WAAO,QAAQ,KAAK,WAAW,CAAC;AAAA,EAClC;AAAA,EAEA,QAAgB;AACd,WAAO,IAAI,OAAO,EAAE,GAAG,KAAK,QAAQ,CAAC;AAAA,EACvC;AACF;AAGA,SAAS,iBAAiB;AAInB,SAAS,cAAc,QAAsB;AAClD,MAAI,QAAQ,IAAI,OAAO;AACrB,YAAQ,IAAI,IAAI,SAAS,kBAAkB,MAAM,EAAE;AAAA,EACrD;AACF;AAGO,SAAS,gBAAgB,OAAwB;AACtD,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;AAAA,EACf;AACA,SAAO,OAAO,KAAK;AACrB;", "names": ["AuthType", "ApprovalMode", "TelemetryTarget", "SettingScope", "sessionId"]}