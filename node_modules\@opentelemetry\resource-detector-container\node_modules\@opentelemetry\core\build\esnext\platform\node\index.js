/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export { getEnvWithoutDefaults, getEnv } from './environment';
export { _globalThis } from './globalThis';
export { hexToBase64 } from './hex-to-base64';
export { RandomIdGenerator } from './RandomIdGenerator';
export { otperformance } from './performance';
export { SDK_INFO } from './sdk-info';
export { unrefTimer } from './timer-util';
//# sourceMappingURL=index.js.map