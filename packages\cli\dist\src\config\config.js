/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import yargs from "yargs/yargs";
import { hideBin } from "yargs/helpers";
import process from "node:process";
import {
  Config,
  loadServerHierarchicalMemory,
  getCurrentGeminiMdFilename,
  ApprovalMode,
  DEFAULT_GEMINI_MODEL
} from "@arien/arien-cli-core";
import { getCliVersion } from "../utils/version.js";
import * as dotenv from "dotenv";
import * as fs from "node:fs";
import * as path from "node:path";
import * as os from "node:os";
import { loadSandboxConfig } from "./sandboxConfig.js";
const logger = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  debug: (...args) => console.debug("[DEBUG]", ...args),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  warn: (...args) => console.warn("[WARN]", ...args),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: (...args) => console.error("[ERROR]", ...args)
};
async function parseArguments() {
  const argv = await yargs(hideBin(process.argv)).option("model", {
    alias: "m",
    type: "string",
    description: `Model`,
    default: process.env.GEMINI_MODEL || DEFAULT_GEMINI_MODEL
  }).option("prompt", {
    alias: "p",
    type: "string",
    description: "Prompt. Appended to input on stdin (if any)."
  }).option("sandbox", {
    alias: "s",
    type: "boolean",
    description: "Run in sandbox?"
  }).option("sandbox-image", {
    type: "string",
    description: "Sandbox image URI."
  }).option("debug", {
    alias: "d",
    type: "boolean",
    description: "Run in debug mode?",
    default: false
  }).option("all_files", {
    alias: "a",
    type: "boolean",
    description: "Include ALL files in context?",
    default: false
  }).option("show_memory_usage", {
    type: "boolean",
    description: "Show memory usage in status bar",
    default: false
  }).option("yolo", {
    alias: "y",
    type: "boolean",
    description: "Auto-approve all tool calls (dangerous!)",
    default: false
  }).option("telemetry", {
    type: "boolean",
    description: "Enable telemetry"
  }).option("checkpointing", {
    type: "boolean",
    description: "Enable checkpointing"
  }).option("telemetryTarget", {
    type: "string",
    description: "Telemetry target"
  }).option("telemetryOtlpEndpoint", {
    type: "string",
    description: "OTLP endpoint for telemetry"
  }).option("telemetryLogPrompts", {
    type: "boolean",
    description: "Log prompts in telemetry"
  }).help().alias("help", "h").version(getCliVersion()).alias("version", "v").parse();
  return argv;
}
async function loadCliConfig(settings, extensions, sessionId) {
  const args = await parseArguments();
  loadEnvFiles();
  const initialOptions = convertSettingsToConfigOptions(settings, sessionId);
  const config = await Config.createWithSettings(process.cwd(), initialOptions);
  if (args.model) {
    config.setModel(args.model);
  }
  if (args.debug !== void 0) {
    config.setDebugMode(args.debug);
  }
  if (args.prompt) {
    config.setQuestion(args.prompt);
  }
  if (args.all_files !== void 0) {
    config.setIncludeAllFiles(args.all_files);
  }
  if (args.show_memory_usage !== void 0) {
    config.setShowMemoryUsage(args.show_memory_usage);
  }
  if (args.yolo !== void 0) {
    config.setApprovalMode(args.yolo ? ApprovalMode.YOLO : ApprovalMode.MANUAL);
  }
  if (args.telemetry !== void 0) {
    config.setTelemetryEnabled(args.telemetry);
  }
  if (args.checkpointing !== void 0) {
    config.setCheckpointingEnabled(args.checkpointing);
  }
  if (args.telemetryTarget) {
    config.setTelemetryTarget(args.telemetryTarget);
  }
  if (args.telemetryOtlpEndpoint) {
    config.setTelemetryOtlpEndpoint(args.telemetryOtlpEndpoint);
  }
  if (args.telemetryLogPrompts !== void 0) {
    config.setTelemetryLogPrompts(args.telemetryLogPrompts);
  }
  applyExtensions(config, extensions);
  if (args.sandbox !== void 0 || args["sandbox-image"]) {
    const sandboxConfig = await loadSandboxConfig(
      args.sandbox,
      args["sandbox-image"]
    );
    if (sandboxConfig) {
      config.setSandbox(sandboxConfig);
    }
  }
  try {
    const memory = await loadServerHierarchicalMemory(
      config.getWorkspaceRoot(),
      getCurrentGeminiMdFilename()
    );
    config.setMemory(memory);
  } catch (error) {
    logger.warn("Failed to load hierarchical memory:", error);
  }
  return config;
}
function loadEnvFiles() {
  const envFiles = [".env", ".env.local"];
  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      dotenv.config({ path: envPath });
      logger.debug(`Loaded environment variables from ${envFile}`);
    }
  }
  const homeEnvPath = path.join(os.homedir(), ".arien", ".env");
  if (fs.existsSync(homeEnvPath)) {
    dotenv.config({ path: homeEnvPath });
    logger.debug("Loaded environment variables from ~/.arien/.env");
  }
}
function applyExtensions(config, extensions) {
  for (const extension of extensions) {
    try {
      extension.configure?.(config);
      logger.debug(`Applied extension: ${extension.name}`);
    } catch (error) {
      logger.error(`Failed to apply extension ${extension.name}:`, error);
    }
  }
}
function convertSettingsToConfigOptions(settings, sessionId) {
  return {
    sessionId,
    model: settings.model,
    embeddingModel: settings.embeddingModel,
    debugMode: settings.debugMode,
    includeAllFiles: settings.includeAllFiles,
    showMemoryUsage: settings.showMemoryUsage,
    approvalMode: settings.approvalMode,
    telemetryEnabled: settings.telemetryEnabled,
    checkpointingEnabled: settings.checkpointingEnabled,
    telemetryTarget: settings.telemetryTarget,
    telemetryOtlpEndpoint: settings.telemetryOtlpEndpoint,
    telemetryLogPrompts: settings.telemetryLogPrompts,
    excludeTools: settings.excludeTools,
    includeTools: settings.includeTools,
    geminiMdFilename: settings.geminiMdFilename,
    theme: settings.theme,
    selectedAuthType: settings.selectedAuthType,
    usageStatisticsEnabled: settings.usageStatisticsEnabled,
    preferredEditor: settings.preferredEditor,
    accessibility: settings.accessibility,
    telemetry: settings.telemetry,
    bugCommand: settings.bugCommand,
    checkpointing: settings.checkpointing,
    autoConfigureMaxOldSpaceSize: settings.autoConfigureMaxOldSpaceSize,
    fileFiltering: settings.fileFiltering,
    hideWindowTitle: settings.hideWindowTitle,
    hideTips: settings.hideTips
  };
}
export {
  loadCliConfig
};
//# sourceMappingURL=config.js.map
