/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  Config,
  ContentGenerator,
  Turn,
  createContentGenerator,
  createContentGeneratorConfig,
  AuthType,
} from '@arien/arien-cli-core';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  turn?: Turn;
  error?: string;
}

export interface UseChatReturn {
  messages: ChatMessage[];
  isGenerating: boolean;
  sendMessage: (content: string) => Promise<void>;
  clearChat: () => void;
  error?: string;
}

export const useChat = (config: Config): UseChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const contentGeneratorRef = useRef<ContentGenerator | null>(null);
  const turnCounterRef = useRef(0);

  // Initialize content generator
  useEffect(() => {
    const initializeGenerator = async () => {
      try {
        const configData = config.getContentGeneratorConfig();
        if (!configData) {
          throw new Error('No content generator configuration found');
        }

        // Use the authType directly since it's now using the unified enum values
        const mappedAuthType: AuthType = configData.authType || AuthType.USE_GEMINI;

        const generatorConfig = await createContentGeneratorConfig(
          config.getModel(),
          configData.apiKey,
          mappedAuthType,
        );

        const generator = await createContentGenerator(generatorConfig);
        contentGeneratorRef.current = generator;
        setError(undefined);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to initialize content generator';
        setError(errorMessage);
        console.error('Failed to initialize content generator:', err);
      }
    };

    initializeGenerator();
  }, [config]);

  const generateId = useCallback(() => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }, []);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim() || isGenerating) {
        return;
      }

      if (!contentGeneratorRef.current) {
        setError('Content generator not initialized');
        return;
      }

      // Create user message
      const userMessage: ChatMessage = {
        id: generateId(),
        role: 'user',
        content: content.trim(),
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setIsGenerating(true);
      setError(undefined);

      try {
        // Create a new turn
        const turn = new Turn();
        turnCounterRef.current += 1;

        // Generate response
        const response = await contentGeneratorRef.current.generateContent({
          model: config.getModel(),
          contents: [
            {
              role: 'user',
              parts: [{ text: content.trim() }],
            },
          ],
        });

        if (response.candidates && response.candidates.length > 0) {
          const candidate = response.candidates[0];
          const responseText =
            candidate.content?.parts
              ?.map((part: any) => ('text' in part ? part.text : ''))
              .join('') || 'No response generated';

          // Create assistant message
          const assistantMessage: ChatMessage = {
            id: generateId(),
            role: 'assistant',
            content: responseText,
            timestamp: new Date(),
            turn,
          };

          setMessages(prev => [...prev, assistantMessage]);
        } else {
          throw new Error('No response candidates received');
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to generate response';
        setError(errorMessage);

        // Add error message to chat
        const errorChatMessage: ChatMessage = {
          id: generateId(),
          role: 'assistant',
          content: `❌ Error: ${errorMessage}`,
          timestamp: new Date(),
          error: errorMessage,
        };

        setMessages(prev => [...prev, errorChatMessage]);
      } finally {
        setIsGenerating(false);
      }
    },
    [isGenerating, generateId],
  );

  const clearChat = useCallback(() => {
    setMessages([]);
    setError(undefined);
    turnCounterRef.current = 0;
  }, []);

  return {
    messages,
    isGenerating,
    sendMessage,
    clearChat,
    error,
  };
};
