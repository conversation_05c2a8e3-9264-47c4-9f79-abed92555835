{"version": 3, "sources": ["../../../src/core/nonInteractiveToolExecutor.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport {\n  logTool<PERSON>all,\n  ToolCallRequestInfo,\n  ToolCallResponseInfo,\n  ToolRegistry,\n  ToolResult,\n} from '../index.js';\nimport { Config } from '../config/config.js';\n\n/**\n * Executes a single tool call non-interactively.\n * It does not handle confirmations, multiple calls, or live updates.\n */\nexport async function executeToolCall(\n  config: Config,\n  toolCallRequest: ToolCallRequestInfo,\n  toolRegistry: ToolRegistry,\n  abortSignal?: AbortSignal,\n): Promise<ToolCallResponseInfo> {\n  const tool = await toolRegistry.getTool(toolCallRequest.name);\n\n  const startTime = Date.now();\n  if (!tool) {\n    const error = new Error(\n      `Tool \"${toolCallRequest.name}\" not found in registry.`,\n    );\n    const durationMs = Date.now() - startTime;\n    logToolCall(config, {\n      'event.name': 'tool_call',\n      'event.timestamp': new Date().toISOString(),\n      function_name: toolCallRequest.name,\n      function_args: toolCallRequest.args,\n      duration_ms: durationMs,\n      success: false,\n      error: error.message,\n    });\n    // Ensure the response structure matches what the API expects for an error\n    return {\n      name: toolCallRequest.name,\n      response: { error: error.message },\n    };\n  }\n\n  try {\n    // Directly execute without confirmation or live output handling\n    const effectiveAbortSignal = abortSignal ?? new AbortController().signal;\n    const toolResult: ToolResult = await tool.execute(\n      toolCallRequest.args,\n      config,\n      effectiveAbortSignal,\n    );\n\n    const durationMs = Date.now() - startTime;\n    logToolCall(config, {\n      'event.name': 'tool_call',\n      'event.timestamp': new Date().toISOString(),\n      function_name: toolCallRequest.name,\n      function_args: toolCallRequest.args,\n      duration_ms: durationMs,\n      success: true,\n    });\n\n    return {\n      name: toolCallRequest.name,\n      response: toolResult,\n    };\n  } catch (e) {\n    const error = e instanceof Error ? e : new Error(String(e));\n    const durationMs = Date.now() - startTime;\n    logToolCall(config, {\n      'event.name': 'tool_call',\n      'event.timestamp': new Date().toISOString(),\n      function_name: toolCallRequest.name,\n      function_args: toolCallRequest.args,\n      duration_ms: durationMs,\n      success: false,\n      error: error.message,\n    });\n\n    return {\n      name: toolCallRequest.name,\n      response: { error: error.message },\n    };\n  }\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA,EACE;AAAA,OAKK;AAOP,eAAsB,gBACpB,QACA,iBACA,cACA,aAC+B;AAC/B,QAAM,OAAO,MAAM,aAAa,QAAQ,gBAAgB,IAAI;AAE5D,QAAM,YAAY,KAAK,IAAI;AAC3B,MAAI,CAAC,MAAM;AACT,UAAM,QAAQ,IAAI;AAAA,MAChB,SAAS,gBAAgB,IAAI;AAAA,IAC/B;AACA,UAAM,aAAa,KAAK,IAAI,IAAI;AAChC,gBAAY,QAAQ;AAAA,MAClB,cAAc;AAAA,MACd,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,MAC1C,eAAe,gBAAgB;AAAA,MAC/B,eAAe,gBAAgB;AAAA,MAC/B,aAAa;AAAA,MACb,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACf,CAAC;AAED,WAAO;AAAA,MACL,MAAM,gBAAgB;AAAA,MACtB,UAAU,EAAE,OAAO,MAAM,QAAQ;AAAA,IACnC;AAAA,EACF;AAEA,MAAI;AAEF,UAAM,uBAAuB,eAAe,IAAI,gBAAgB,EAAE;AAClE,UAAM,aAAyB,MAAM,KAAK;AAAA,MACxC,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,IACF;AAEA,UAAM,aAAa,KAAK,IAAI,IAAI;AAChC,gBAAY,QAAQ;AAAA,MAClB,cAAc;AAAA,MACd,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,MAC1C,eAAe,gBAAgB;AAAA,MAC/B,eAAe,gBAAgB;AAAA,MAC/B,aAAa;AAAA,MACb,SAAS;AAAA,IACX,CAAC;AAED,WAAO;AAAA,MACL,MAAM,gBAAgB;AAAA,MACtB,UAAU;AAAA,IACZ;AAAA,EACF,SAAS,GAAG;AACV,UAAM,QAAQ,aAAa,QAAQ,IAAI,IAAI,MAAM,OAAO,CAAC,CAAC;AAC1D,UAAM,aAAa,KAAK,IAAI,IAAI;AAChC,gBAAY,QAAQ;AAAA,MAClB,cAAc;AAAA,MACd,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,MAC1C,eAAe,gBAAgB;AAAA,MAC/B,eAAe,gBAAgB;AAAA,MAC/B,aAAa;AAAA,MACb,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACf,CAAC;AAED,WAAO;AAAA,MACL,MAAM,gBAAgB;AAAA,MACtB,UAAU,EAAE,OAAO,MAAM,QAAQ;AAAA,IACnC;AAAA,EACF;AACF;", "names": []}