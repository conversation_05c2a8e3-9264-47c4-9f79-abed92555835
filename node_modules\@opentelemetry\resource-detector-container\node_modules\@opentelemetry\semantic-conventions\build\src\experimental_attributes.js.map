{"version": 3, "file": "experimental_attributes.js", "sourceRoot": "", "sources": ["../../src/experimental_attributes.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,4GAA4G;AAC5G,8GAA8G;AAC9G,4GAA4G;AAE5G;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,kCAAkC,GAAG,YAAqB,CAAC;AAExE;;GAEG;AACU,QAAA,+BAA+B,GAAG,SAAkB,CAAC;AAElE;;GAEG;AACU,QAAA,kCAAkC,GAAG,YAAqB,CAAC;AAExE;;;;;;;GAOG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;GAEG;AACU,QAAA,2BAA2B,GAAG,SAAkB,CAAC;AAE9D;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;GAOG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;GAOG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;GASG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;;;GAUG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;;;GASG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;;;;;;GAcG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;;;;;;;;GAeG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;GAOG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,uCAAuC,GAAG,oCAA6C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;GAOG;AACU,QAAA,uCAAuC,GAAG,oCAA6C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,gDAAgD,GAAG,6CAAsD,CAAC;AAEvH;;;;;;GAMG;AACU,QAAA,0CAA0C,GAAG,uCAAgD,CAAC;AAE3G;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,yCAAyC,GAAG,sCAA+C,CAAC;AAEzG;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,yCAAyC,GAAG,sCAA+C,CAAC;AAEzG;;;;;;;;GAQG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;GAOG;AACU,QAAA,2CAA2C,GAAG,wCAAiD,CAAC;AAE7G;;;;;;;GAOG;AACU,QAAA,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;GAEG;AACU,QAAA,4BAA4B,GAAG,KAAc,CAAC;AAE3D;;GAEG;AACU,QAAA,gCAAgC,GAAG,SAAkB,CAAC;AAEnE;;;;;;;GAOG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;GAOG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;GAOG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;GAOG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;GAQG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;GASG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;;;;;;GAaG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;;;GAUG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;;;;;;GAWG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;;;;;;;;;GAgBG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,yCAAyC,GAAG,sCAA+C,CAAC;AAEzG;;;;;;;;GAQG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;GAIG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;GAEG;AACU,QAAA,2CAA2C,GAAG,QAAiB,CAAC;AAE7E;;GAEG;AACU,QAAA,4CAA4C,GAAG,SAAkB,CAAC;AAE/E;;;;;;;;;;GAUG;AACU,QAAA,qCAAqC,GAAG,kCAA2C,CAAC;AAEjG;;GAEG;AACU,QAAA,wDAAwD,GAAG,kBAA2B,CAAC;AAEpG;;GAEG;AACU,QAAA,wDAAwD,GAAG,kBAA2B,CAAC;AAEpG;;GAEG;AACU,QAAA,+CAA+C,GAAG,UAAmB,CAAC;AAEnF;;GAEG;AACU,QAAA,8CAA8C,GAAG,SAAkB,CAAC;AAEjF;;GAEG;AACU,QAAA,6CAA6C,GAAG,QAAiB,CAAC;AAE/E;;;;;;;;GAQG;AACU,QAAA,+CAA+C,GAAG,4CAAqD,CAAC;AAErH;;;;;;;GAOG;AACU,QAAA,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;;;GAIG;AACU,QAAA,qCAAqC,GAAG,kCAA2C,CAAC;AAEjG;;;;;;;GAOG;AACU,QAAA,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;;;GAWG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;;;GAWG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;GAEG;AACU,QAAA,qCAAqC,GAAG,KAAc,CAAC;AAEpE;;GAEG;AACU,QAAA,qCAAqC,GAAG,KAAc,CAAC;AAEpE;;GAEG;AACU,QAAA,6CAA6C,GAAG,aAAsB,CAAC;AAEpF;;GAEG;AACU,QAAA,2CAA2C,GAAG,WAAoB,CAAC;AAEhF;;GAEG;AACU,QAAA,8CAA8C,GAAG,cAAuB,CAAC;AAEtF;;GAEG;AACU,QAAA,8CAA8C,GAAG,cAAuB,CAAC;AAEtF;;GAEG;AACU,QAAA,qCAAqC,GAAG,KAAc,CAAC;AAEpE;;GAEG;AACU,QAAA,wCAAwC,GAAG,QAAiB,CAAC;AAE1E;;GAEG;AACU,QAAA,wCAAwC,GAAG,QAAiB,CAAC;AAE1E;;GAEG;AACU,QAAA,uCAAuC,GAAG,OAAgB,CAAC;AAExE;;GAEG;AACU,QAAA,qCAAqC,GAAG,KAAc,CAAC;AAEpE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;GAOG;AACU,QAAA,0CAA0C,GAAG,uCAAgD,CAAC;AAE3G;;;;;;;;GAQG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;GAEG;AACU,QAAA,qCAAqC,GAAG,OAAgB,CAAC;AAEtE;;GAEG;AACU,QAAA,mCAAmC,GAAG,KAAc,CAAC;AAElE;;GAEG;AACU,QAAA,oCAAoC,GAAG,MAAe,CAAC;AAEpE;;;;;;;;;GASG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;;GASG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;GAEG;AACU,QAAA,uCAAuC,GAAG,cAAuB,CAAC;AAE/E;;GAEG;AACU,QAAA,gCAAgC,GAAG,OAAgB,CAAC;AAEjE;;GAEG;AACU,QAAA,kCAAkC,GAAG,SAAkB,CAAC;AAErE;;GAEG;AACU,QAAA,+BAA+B,GAAG,MAAe,CAAC;AAE/D;;GAEG;AACU,QAAA,kCAAkC,GAAG,SAAkB,CAAC;AAErE;;GAEG;AACU,QAAA,kCAAkC,GAAG,SAAkB,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;GAQG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,uCAAuC,GAAG,WAAoB,CAAC;AAE5E;;GAEG;AACU,QAAA,wCAAwC,GAAG,YAAqB,CAAC;AAE9E;;GAEG;AACU,QAAA,qCAAqC,GAAG,SAAkB,CAAC;AAExE;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;;;GASG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;;GASG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;GAEG;AACU,QAAA,gDAAgD,GAAG,cAAuB,CAAC;AAExF;;GAEG;AACU,QAAA,yCAAyC,GAAG,OAAgB,CAAC;AAE1E;;GAEG;AACU,QAAA,2CAA2C,GAAG,SAAkB,CAAC;AAE9E;;GAEG;AACU,QAAA,wCAAwC,GAAG,MAAe,CAAC;AAExE;;GAEG;AACU,QAAA,2CAA2C,GAAG,SAAkB,CAAC;AAE9E;;GAEG;AACU,QAAA,2CAA2C,GAAG,SAAkB,CAAC;AAE9E;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;;;GAQG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,mCAAmC,GAAG,OAAgB,CAAC;AAEpE;;GAEG;AACU,QAAA,oCAAoC,GAAG,QAAiB,CAAC;AAEtE;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;;;;;;;GAQG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,iCAAiC,GAAG,WAAoB,CAAC;AAEtE;;GAEG;AACU,QAAA,4BAA4B,GAAG,MAAe,CAAC;AAE5D;;GAEG;AACU,QAAA,+BAA+B,GAAG,SAAkB,CAAC;AAElE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;GAEG;AACU,QAAA,sCAAsC,GAAG,mBAA4B,CAAC;AAEnF;;GAEG;AACU,QAAA,qCAAqC,GAAG,kBAA2B,CAAC;AAEjF;;GAEG;AACU,QAAA,4CAA4C,GAAG,yBAAkC,CAAC;AAE/F;;GAEG;AACU,QAAA,mCAAmC,GAAG,gBAAyB,CAAC;AAE7E;;GAEG;AACU,QAAA,4BAA4B,GAAG,SAAkB,CAAC;AAE/D;;GAEG;AACU,QAAA,4BAA4B,GAAG,SAAkB,CAAC;AAE/D;;GAEG;AACU,QAAA,4BAA4B,GAAG,SAAkB,CAAC;AAE/D;;GAEG;AACU,QAAA,0CAA0C,GAAG,uBAAgC,CAAC;AAE3F;;GAEG;AACU,QAAA,+BAA+B,GAAG,YAAqB,CAAC;AAErE;;GAEG;AACU,QAAA,kCAAkC,GAAG,eAAwB,CAAC;AAE3E;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,sCAAsC,GAAG,mBAA4B,CAAC;AAEnF;;GAEG;AACU,QAAA,yCAAyC,GAAG,sBAA+B,CAAC;AAEzF;;GAEG;AACU,QAAA,8CAA8C,GAAG,2BAAoC,CAAC;AAEnG;;GAEG;AACU,QAAA,oCAAoC,GAAG,iBAA0B,CAAC;AAE/E;;GAEG;AACU,QAAA,oCAAoC,GAAG,iBAA0B,CAAC;AAE/E;;GAEG;AACU,QAAA,6BAA6B,GAAG,UAAmB,CAAC;AAEjE;;GAEG;AACU,QAAA,mCAAmC,GAAG,gBAAyB,CAAC;AAE7E;;GAEG;AACU,QAAA,4CAA4C,GAAG,yBAAkC,CAAC;AAE/F;;GAEG;AACU,QAAA,wCAAwC,GAAG,qBAA8B,CAAC;AAEvF;;GAEG;AACU,QAAA,kCAAkC,GAAG,eAAwB,CAAC;AAE3E;;GAEG;AACU,QAAA,uCAAuC,GAAG,oBAA6B,CAAC;AAErF;;GAEG;AACU,QAAA,0CAA0C,GAAG,uBAAgC,CAAC;AAE3F;;GAEG;AACU,QAAA,kCAAkC,GAAG,eAAwB,CAAC;AAE3E;;GAEG;AACU,QAAA,wCAAwC,GAAG,qBAA8B,CAAC;AAEvF;;GAEG;AACU,QAAA,yCAAyC,GAAG,sBAA+B,CAAC;AAEzF;;GAEG;AACU,QAAA,qCAAqC,GAAG,kBAA2B,CAAC;AAEjF;;GAEG;AACU,QAAA,sCAAsC,GAAG,mBAA4B,CAAC;AAEnF;;GAEG;AACU,QAAA,sCAAsC,GAAG,mBAA4B,CAAC;AAEnF;;GAEG;AACU,QAAA,sCAAsC,GAAG,mBAA4B,CAAC;AAEnF;;;;GAIG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;GAEG;AACU,QAAA,kCAAkC,GAAG,eAAwB,CAAC;AAE3E;;GAEG;AACU,QAAA,wBAAwB,GAAG,KAAc,CAAC;AAEvD;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;GAEG;AACU,QAAA,wBAAwB,GAAG,KAAc,CAAC;AAEvD;;GAEG;AACU,QAAA,2BAA2B,GAAG,QAAiB,CAAC;AAE7D;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,iCAAiC,GAAG,cAAuB,CAAC;AAEzE;;GAEG;AACU,QAAA,kCAAkC,GAAG,eAAwB,CAAC;AAE3E;;;;;;;;;GASG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;GAOG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;GAQG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;GAOG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;;GAUG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;;;;;;;GAeG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;;;;;GAUG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;GAUG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;;GAUG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;;GAWG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;;GAUG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;;;GAUG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;GAUG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;;;;;;;;GAgBG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;;;;;;GAcG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;GASG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;GAEG;AACU,QAAA,gCAAgC,GAAG,QAAiB,CAAC;AAElE;;GAEG;AACU,QAAA,gCAAgC,GAAG,QAAiB,CAAC;AAElE;;GAEG;AACU,QAAA,8BAA8B,GAAG,MAAe,CAAC;AAE9D;;;;;;;;GAQG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;GAQG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;;;GAUG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;GAQG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;GAQG;AACI,MAAM,oBAAoB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,mBAAmB,GAAG,EAAE,CAAC;AAAjE,QAAA,oBAAoB,wBAA6C;AAE9E;;;;;;;;GAQG;AACI,MAAM,qBAAqB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,oBAAoB,GAAG,EAAE,CAAC;AAAnE,QAAA,qBAAqB,yBAA8C;AAEhF;;;;;;GAMG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;GAOG;AACU,QAAA,aAAa,GAAG,UAAmB,CAAC;AAEjD;;GAEG;AACU,QAAA,mBAAmB,GAAG,MAAe,CAAC;AAEnD;;GAEG;AACU,QAAA,wBAAwB,GAAG,WAAoB,CAAC;AAE7D;;GAEG;AACU,QAAA,qBAAqB,GAAG,QAAiB,CAAC;AAEvD;;GAEG;AACU,QAAA,qBAAqB,GAAG,QAAiB,CAAC;AAEvD;;GAEG;AACU,QAAA,mBAAmB,GAAG,MAAe,CAAC;AAEnD;;GAEG;AACU,QAAA,oBAAoB,GAAG,OAAgB,CAAC;AAErD;;GAEG;AACU,QAAA,qBAAqB,GAAG,QAAiB,CAAC;AAEvD;;GAEG;AACU,QAAA,mBAAmB,GAAG,MAAe,CAAC;AAEnD;;;;;;;;GAQG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;GAEG;AACU,QAAA,wCAAwC,GAAG,CAAU,CAAC;AAEnE;;GAEG;AACU,QAAA,wCAAwC,GAAG,CAAU,CAAC;AAEnE;;GAEG;AACU,QAAA,wCAAwC,GAAG,CAAU,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;GAEG;AACU,QAAA,wCAAwC,GAAG,KAAc,CAAC;AAEvE;;GAEG;AACU,QAAA,wCAAwC,GAAG,KAAc,CAAC;AAEvE;;GAEG;AACU,QAAA,gDAAgD,GAAG,aAAsB,CAAC;AAEvF;;GAEG;AACU,QAAA,8CAA8C,GAAG,WAAoB,CAAC;AAEnF;;GAEG;AACU,QAAA,iDAAiD,GAAG,cAAuB,CAAC;AAEzF;;GAEG;AACU,QAAA,iDAAiD,GAAG,cAAuB,CAAC;AAEzF;;GAEG;AACU,QAAA,wCAAwC,GAAG,KAAc,CAAC;AAEvE;;GAEG;AACU,QAAA,2CAA2C,GAAG,QAAiB,CAAC;AAE7E;;GAEG;AACU,QAAA,2CAA2C,GAAG,QAAiB,CAAC;AAE7E;;GAEG;AACU,QAAA,0CAA0C,GAAG,OAAgB,CAAC;AAE3E;;GAEG;AACU,QAAA,wCAAwC,GAAG,KAAc,CAAC;AAEvE;;;;;;;;GAQG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;GAQG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;;;GAQG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;GASG;AACU,QAAA,6CAA6C,GAAG,0CAAmD,CAAC;AAEjH;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;;;GAQG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;GAEG;AACU,QAAA,sCAAsC,GAAG,MAAe,CAAC;AAEtE;;GAEG;AACU,QAAA,sCAAsC,GAAG,MAAe,CAAC;AAEtE;;;;;;;;GAQG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;GAQG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;GAEG;AACU,QAAA,wCAAwC,GAAG,QAAiB,CAAC;AAE1E;;GAEG;AACU,QAAA,yCAAyC,GAAG,SAAkB,CAAC;AAE5E;;;;;;;;;;;;GAYG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;GAEG;AACU,QAAA,qDAAqD,GAAG,kBAA2B,CAAC;AAEjG;;GAEG;AACU,QAAA,qDAAqD,GAAG,kBAA2B,CAAC;AAEjG;;GAEG;AACU,QAAA,4CAA4C,GAAG,UAAmB,CAAC;AAEhF;;GAEG;AACU,QAAA,2CAA2C,GAAG,SAAkB,CAAC;AAE9E;;GAEG;AACU,QAAA,0CAA0C,GAAG,QAAiB,CAAC;AAE5E;;;;;;;;GAQG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,wCAAwC,GAAG,SAAkB,CAAC;AAE3E;;GAEG;AACU,QAAA,mDAAmD,GAAG,oBAA6B,CAAC;AAEjG;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,0CAA0C,GAAG,WAAoB,CAAC;AAE/E;;GAEG;AACU,QAAA,wCAAwC,GAAG,SAAkB,CAAC;AAE3E;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,2CAA2C,GAAG,YAAqB,CAAC;AAEjF;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,0CAA0C,GAAG,WAAoB,CAAC;AAE/E;;GAEG;AACU,QAAA,wCAAwC,GAAG,SAAkB,CAAC;AAE3E;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;;;;;;;GAQG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;;;;GASG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;GAMG;AACU,QAAA,uCAAuC,GAAG,oCAA6C,CAAC;AAErG;;;;;;;;;GASG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;GASG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;GAQG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;;;GAQG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;;;GASG;AACI,MAAM,gCAAgC,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,+BAA+B,GAAG,EAAE,CAAC;AAAzF,QAAA,gCAAgC,oCAAyD;AAEtG;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;GASG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;;;GAQG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;GAQG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;GASG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;;;;;GAUG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;;;;;;GAaG;AACI,MAAM,2BAA2B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,0BAA0B,GAAG,EAAE,CAAC;AAA/E,QAAA,2BAA2B,+BAAoD;AAE5F;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACI,MAAM,uBAAuB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,sBAAsB,GAAG,EAAE,CAAC;AAAvE,QAAA,uBAAuB,2BAAgD;AAEpF;;;;;;;;;;GAUG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;GAQG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;GAQG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;;GASG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,0BAA0B,GAAG,YAAqB,CAAC;AAEhE;;GAEG;AACU,QAAA,0BAA0B,GAAG,YAAqB,CAAC;AAEhE;;GAEG;AACU,QAAA,2BAA2B,GAAG,aAAsB,CAAC;AAElE;;GAEG;AACU,QAAA,0BAA0B,GAAG,YAAqB,CAAC;AAEhE;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,uBAAuB,GAAG,SAAkB,CAAC;AAE1D;;GAEG;AACU,QAAA,mBAAmB,GAAG,KAAc,CAAC;AAElD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,mBAAmB,GAAG,KAAc,CAAC;AAElD;;GAEG;AACU,QAAA,6BAA6B,GAAG,eAAwB,CAAC;AAEtE;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,kBAAkB,GAAG,IAAa,CAAC;AAEhD;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,oBAAoB,GAAG,MAAe,CAAC;AAEpD;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,kCAAkC,GAAG,oBAA6B,CAAC;AAEhF;;GAEG;AACU,QAAA,uBAAuB,GAAG,SAAkB,CAAC;AAE1D;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,uBAAuB,GAAG,SAAkB,CAAC;AAE1D;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,4BAA4B,GAAG,cAAuB,CAAC;AAEpE;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,uBAAuB,GAAG,SAAkB,CAAC;AAE1D;;GAEG;AACU,QAAA,0BAA0B,GAAG,YAAqB,CAAC;AAEhE;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;GAEG;AACU,QAAA,0BAA0B,GAAG,YAAqB,CAAC;AAEhE;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,uBAAuB,GAAG,SAAkB,CAAC;AAE1D;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,uBAAuB,GAAG,SAAkB,CAAC;AAE1D;;GAEG;AACU,QAAA,kCAAkC,GAAG,eAAwB,CAAC;AAE3E;;GAEG;AACU,QAAA,iCAAiC,GAAG,cAAuB,CAAC;AAEzE;;GAEG;AACU,QAAA,iCAAiC,GAAG,cAAuB,CAAC;AAEzE;;GAEG;AACU,QAAA,mCAAmC,GAAG,gBAAyB,CAAC;AAE7E;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,+BAA+B,GAAG,YAAqB,CAAC;AAErE;;GAEG;AACU,QAAA,gCAAgC,GAAG,aAAsB,CAAC;AAEvE;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,4BAA4B,GAAG,SAAkB,CAAC;AAE/D;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;GAEG;AACU,QAAA,kCAAkC,GAAG,eAAwB,CAAC;AAE3E;;GAEG;AACU,QAAA,gCAAgC,GAAG,aAAsB,CAAC;AAEvE;;GAEG;AACU,QAAA,gCAAgC,GAAG,aAAsB,CAAC;AAEvE;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;GAEG;AACU,QAAA,+BAA+B,GAAG,YAAqB,CAAC;AAErE;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;GAEG;AACU,QAAA,yBAAyB,GAAG,MAAe,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,QAAiB,CAAC;AAE7D;;GAEG;AACU,QAAA,4BAA4B,GAAG,SAAkB,CAAC;AAE/D;;GAEG;AACU,QAAA,iCAAiC,GAAG,cAAuB,CAAC;AAEzE;;GAEG;AACU,QAAA,gCAAgC,GAAG,aAAsB,CAAC;AAEvE;;GAEG;AACU,QAAA,6BAA6B,GAAG,UAAmB,CAAC;AAEjE;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,uCAAuC,GAAG,oBAA6B,CAAC;AAErF;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,4BAA4B,GAAG,SAAkB,CAAC;AAE/D;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;GAEG;AACU,QAAA,+BAA+B,GAAG,YAAqB,CAAC;AAErE;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;GAEG;AACU,QAAA,6BAA6B,GAAG,UAAmB,CAAC;AAEjE;;GAEG;AACU,QAAA,8BAA8B,GAAG,WAAoB,CAAC;AAEnE;;GAEG;AACU,QAAA,sCAAsC,GAAG,mBAA4B,CAAC;AAEnF;;GAEG;AACU,QAAA,2BAA2B,GAAG,QAAiB,CAAC;AAE7D;;GAEG;AACU,QAAA,6BAA6B,GAAG,UAAmB,CAAC;AAEjE;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;;;;;;;;GASG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;;;;GASG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;;;;;;;GAeG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;GAOG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;GAEG;AACU,QAAA,iCAAiC,GAAG,WAAoB,CAAC;AAEtE;;;;;;;;;;GAUG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;GAOG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;;;;;;;;;;;GAkBG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;;;;GASG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;GASG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;GASG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,4BAA4B,GAAG,MAAe,CAAC;AAE5D;;GAEG;AACU,QAAA,6BAA6B,GAAG,OAAgB,CAAC;AAE9D;;;;;;;;;GASG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;;;GAWG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;;;;;;GAWG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;;;;;;GAaG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;;GASG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;GAIG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;;GAOG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;;GAOG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;GAIG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,oCAAoC,GAAG,QAAiB,CAAC;AAEtE;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;GAEG;AACU,QAAA,oCAAoC,GAAG,QAAiB,CAAC;AAEtE;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;GAEG;AACU,QAAA,yCAAyC,GAAG,eAAwB,CAAC;AAElF;;GAEG;AACU,QAAA,+BAA+B,GAAG,KAAc,CAAC;AAE9D;;GAEG;AACU,QAAA,iCAAiC,GAAG,OAAgB,CAAC;AAElE;;GAEG;AACU,QAAA,+BAA+B,GAAG,KAAc,CAAC;AAE9D;;GAEG;AACU,QAAA,yCAAyC,GAAG,eAAwB,CAAC;AAElF;;;;;;;;GAQG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;GAQG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;GAIG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;GAEG;AACU,QAAA,6BAA6B,GAAG,YAAqB,CAAC;AAEnE;;GAEG;AACU,QAAA,uBAAuB,GAAG,MAAe,CAAC;AAEvD;;GAEG;AACU,QAAA,wBAAwB,GAAG,OAAgB,CAAC;AAEzD;;GAEG;AACU,QAAA,yBAAyB,GAAG,QAAiB,CAAC;AAE3D;;GAEG;AACU,QAAA,wBAAwB,GAAG,OAAgB,CAAC;AAEzD;;;;;;;;;;;;;;;;;GAiBG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;GAQG;AACU,QAAA,0CAA0C,GAAG,uCAAgD,CAAC;AAE3G;;;;;;;;;;;GAWG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;GAEG;AACU,QAAA,2CAA2C,GAAG,QAAiB,CAAC;AAE7E;;GAEG;AACU,QAAA,4CAA4C,GAAG,SAAkB,CAAC;AAE/E;;GAEG;AACU,QAAA,6CAA6C,GAAG,UAAmB,CAAC;AAEjF;;GAEG;AACU,QAAA,0CAA0C,GAAG,OAAgB,CAAC;AAE3E;;GAEG;AACU,QAAA,0CAA0C,GAAG,OAAgB,CAAC;AAE3E;;GAEG;AACU,QAAA,0CAA0C,GAAG,OAAgB,CAAC;AAE3E;;GAEG;AACU,QAAA,2CAA2C,GAAG,QAAiB,CAAC;AAE7E;;GAEG;AACU,QAAA,oDAAoD,GAAG,iBAA0B,CAAC;AAE/F;;GAEG;AACU,QAAA,4CAA4C,GAAG,SAAkB,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;;;GASG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,wCAAwC,GAAG,SAAkB,CAAC;AAE3E;;GAEG;AACU,QAAA,yCAAyC,GAAG,UAAmB,CAAC;AAE7E;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,gDAAgD,GAAG,iBAA0B,CAAC;AAE3F;;GAEG;AACU,QAAA,wCAAwC,GAAG,SAAkB,CAAC;AAE3E;;;;;;;;;;;;;;GAcG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;;;;;GAYG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;GAQG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;;GAUG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;;GAQG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;GAQG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;GAOG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;GASG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;GASG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;GAOG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;GAIG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,kCAA2C,CAAC;AAEjG;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,wCAAwC,GAAG,qCAA8C,CAAC;AAEvG;;GAEG;AACU,QAAA,8CAA8C,GAAG,MAAe,CAAC;AAE9E;;GAEG;AACU,QAAA,6CAA6C,GAAG,KAAc,CAAC;AAE5E;;GAEG;AACU,QAAA,gDAAgD,GAAG,QAAiB,CAAC;AAElF;;GAEG;AACU,QAAA,0DAA0D,GAAG,kBAA2B,CAAC;AAEtG;;;;;;GAMG;AACU,QAAA,wCAAwC,GAAG,qCAA8C,CAAC;AAEvG;;GAEG;AACU,QAAA,qDAAqD,GAAG,aAAsB,CAAC;AAE5F;;GAEG;AACU,QAAA,oDAAoD,GAAG,YAAqB,CAAC;AAE1F;;GAEG;AACU,QAAA,iDAAiD,GAAG,SAAkB,CAAC;AAEpF;;GAEG;AACU,QAAA,8CAA8C,GAAG,MAAe,CAAC;AAE9E;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,yCAAyC,GAAG,sCAA+C,CAAC;AAEzG;;GAEG;AACU,QAAA,+CAA+C,GAAG,MAAe,CAAC;AAE/E;;GAEG;AACU,QAAA,8CAA8C,GAAG,KAAc,CAAC;AAE7E;;GAEG;AACU,QAAA,iDAAiD,GAAG,QAAiB,CAAC;AAEnF;;GAEG;AACU,QAAA,2DAA2D,GAAG,kBAA2B,CAAC;AAEvG;;;;;;GAMG;AACU,QAAA,yCAAyC,GAAG,sCAA+C,CAAC;AAEzG;;GAEG;AACU,QAAA,sDAAsD,GAAG,aAAsB,CAAC;AAE7F;;GAEG;AACU,QAAA,qDAAqD,GAAG,YAAqB,CAAC;AAE3F;;GAEG;AACU,QAAA,kDAAkD,GAAG,SAAkB,CAAC;AAErF;;GAEG;AACU,QAAA,+CAA+C,GAAG,MAAe,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;;;;GAYG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;GAOG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;GAOG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;;GAOG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;GAOG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;GAOG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;GAOG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;GAQG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,0CAA0C,GAAG,uCAAgD,CAAC;AAE3G;;GAEG;AACU,QAAA,uDAAuD,GAAG,aAAsB,CAAC;AAE9F;;GAEG;AACU,QAAA,uDAAuD,GAAG,aAAsB,CAAC;AAE9F;;GAEG;AACU,QAAA,gDAAgD,GAAG,MAAe,CAAC;AAEhF;;;;;;;;GAQG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;GAOG;AACU,QAAA,uCAAuC,GAAG,oCAA6C,CAAC;AAErG;;GAEG;AACU,QAAA,6CAA6C,GAAG,MAAe,CAAC;AAE7E;;GAEG;AACU,QAAA,gDAAgD,GAAG,SAAkB,CAAC;AAEnF;;;;;;;GAOG;AACU,QAAA,wCAAwC,GAAG,qCAA8C,CAAC;AAEvG;;;;;;GAMG;AACU,QAAA,8CAA8C,GAAG,2CAAoD,CAAC;AAEnH;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;GAEG;AACU,QAAA,gCAAgC,GAAG,MAAe,CAAC;AAEhE;;GAEG;AACU,QAAA,wCAAwC,GAAG,cAAuB,CAAC;AAEhF;;GAEG;AACU,QAAA,sCAAsC,GAAG,YAAqB,CAAC;AAE5E;;GAEG;AACU,QAAA,wCAAwC,GAAG,cAAuB,CAAC;AAEhF;;GAEG;AACU,QAAA,4CAA4C,GAAG,kBAA2B,CAAC;AAExF;;GAEG;AACU,QAAA,wCAAwC,GAAG,cAAuB,CAAC;AAEhF;;GAEG;AACU,QAAA,2CAA2C,GAAG,iBAA0B,CAAC;AAEtF;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;GAEG;AACU,QAAA,8BAA8B,GAAG,OAAgB,CAAC;AAE/D;;GAEG;AACU,QAAA,6BAA6B,GAAG,MAAe,CAAC;AAE7D;;GAEG;AACU,QAAA,+BAA+B,GAAG,QAAiB,CAAC;AAEjE;;GAEG;AACU,QAAA,6BAA6B,GAAG,MAAe,CAAC;AAE7D;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;;GASG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,kCAA2C,CAAC;AAEjG;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;;;;;;;;;GAkBG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;GAEG;AACU,QAAA,6BAA6B,GAAG,WAAoB,CAAC;AAElE;;GAEG;AACU,QAAA,+BAA+B,GAAG,aAAsB,CAAC;AAEtE;;GAEG;AACU,QAAA,mCAAmC,GAAG,iBAA0B,CAAC;AAE9E;;GAEG;AACU,QAAA,gCAAgC,GAAG,cAAuB,CAAC;AAExE;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,4BAA4B,GAAG,UAAmB,CAAC;AAEhE;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;GAEG;AACU,QAAA,iCAAiC,GAAG,eAAwB,CAAC;AAE1E;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,wBAAwB,GAAG,MAAe,CAAC;AAExD;;GAEG;AACU,QAAA,kCAAkC,GAAG,gBAAyB,CAAC;AAE5E;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;GAEG;AACU,QAAA,6BAA6B,GAAG,WAAoB,CAAC;AAElE;;GAEG;AACU,QAAA,uBAAuB,GAAG,KAAc,CAAC;AAEtD;;;;;;;GAOG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,6BAA6B,GAAG,OAAgB,CAAC;AAE9D;;GAEG;AACU,QAAA,kCAAkC,GAAG,QAAiB,CAAC;AAEpE;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;;;;;;;GAcG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;;GAQG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;GAIG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;GAOG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;GAEG;AACU,QAAA,0BAA0B,GAAG,OAAgB,CAAC;AAE3D;;;;;;;GAOG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;GAQG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;GAEG;AACU,QAAA,qCAAqC,GAAG,UAAmB,CAAC;AAEzE;;GAEG;AACU,QAAA,kCAAkC,GAAG,OAAgB,CAAC;AAEnE;;GAEG;AACU,QAAA,yCAAyC,GAAG,cAAuB,CAAC;AAEjF;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,sCAAsC,GAAG,mCAA4C,CAAC;AAEnG;;;;GAIG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,oBAAoB,GAAG,MAAe,CAAC;AAEpD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,qBAAqB,GAAG,OAAgB,CAAC;AAEtD;;GAEG;AACU,QAAA,mBAAmB,GAAG,KAAc,CAAC;AAElD;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;GAOG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;GAOG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;GAOG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;GAOG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;;;GAQG;AACU,QAAA,aAAa,GAAG,UAAmB,CAAC;AAEjD;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;GAOG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;GAEG;AACU,QAAA,kCAAkC,GAAG,QAAiB,CAAC;AAEpE;;GAEG;AACU,QAAA,gCAAgC,GAAG,MAAe,CAAC;AAEhE;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;GAEG;AACU,QAAA,0BAA0B,GAAG,KAAc,CAAC;AAEzD;;GAEG;AACU,QAAA,0BAA0B,GAAG,KAAc,CAAC;AAEzD;;GAEG;AACU,QAAA,0BAA0B,GAAG,KAAc,CAAC;AAEzD;;GAEG;AACU,QAAA,0BAA0B,GAAG,KAAc,CAAC;AAEzD;;GAEG;AACU,QAAA,sBAAsB,GAAG,MAAe,CAAC;AAEtD;;GAEG;AACU,QAAA,sBAAsB,GAAG,MAAe,CAAC;AAEtD;;;;;;;;GAQG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;;;;;GAUG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;GAQG;AACU,QAAA,6CAA6C,GAAG,0CAAmD,CAAC;AAEjH;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;;;GAQG;AACU,QAAA,8CAA8C,GAAG,2CAAoD,CAAC;AAEnH;;;;;;;;;GASG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,aAAa,GAAG,UAAmB,CAAC;AAEjD;;;;;;;;;GASG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,UAAU,GAAG,OAAgB,CAAC;AAE3C;;;;;;GAMG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;GAIG;AACU,QAAA,aAAa,GAAG,UAAmB,CAAC;AAEjD;;GAEG;AACU,QAAA,uBAAuB,GAAG,UAAmB,CAAC;AAE3D;;GAEG;AACU,QAAA,qBAAqB,GAAG,QAAiB,CAAC;AAEvD;;GAEG;AACU,QAAA,iBAAiB,GAAG,IAAa,CAAC;AAE/C;;;;;;GAMG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAkB,CAAC;AAExD;;GAEG;AACU,QAAA,iBAAiB,GAAG,KAAc,CAAC;AAEhD;;GAEG;AACU,QAAA,6BAA6B,GAAG,iBAA0B,CAAC;AAExE;;GAEG;AACU,QAAA,uBAAuB,GAAG,WAAoB,CAAC;AAE5D;;GAEG;AACU,QAAA,iBAAiB,GAAG,KAAc,CAAC;AAEhD;;GAEG;AACU,QAAA,iBAAiB,GAAG,KAAc,CAAC;AAEhD;;GAEG;AACU,QAAA,0BAA0B,GAAG,cAAuB,CAAC;AAElE;;GAEG;AACU,QAAA,oBAAoB,GAAG,QAAiB,CAAC;AAEtD;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAkB,CAAC;AAExD;;GAEG;AACU,QAAA,2BAA2B,GAAG,eAAwB,CAAC;AAEpE;;GAEG;AACU,QAAA,0BAA0B,GAAG,cAAuB,CAAC;AAElE;;GAEG;AACU,QAAA,wBAAwB,GAAG,YAAqB,CAAC;AAE9D;;GAEG;AACU,QAAA,yBAAyB,GAAG,aAAsB,CAAC;AAEhE;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAkB,CAAC;AAExD;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;GAEG;AACU,QAAA,8BAA8B,GAAG,YAAqB,CAAC;AAEpE;;GAEG;AACU,QAAA,4BAA4B,GAAG,UAAmB,CAAC;AAEhE;;GAEG;AACU,QAAA,6BAA6B,GAAG,WAAoB,CAAC;AAElE;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;GAEG;AACU,QAAA,sBAAsB,GAAG,QAAiB,CAAC;AAExD;;GAEG;AACU,QAAA,0BAA0B,GAAG,YAAqB,CAAC;AAEhE;;GAEG;AACU,QAAA,0BAA0B,GAAG,YAAqB,CAAC;AAEhE;;GAEG;AACU,QAAA,wBAAwB,GAAG,UAAmB,CAAC;AAE5D;;GAEG;AACU,QAAA,yBAAyB,GAAG,WAAoB,CAAC;AAE9D;;;;;;;;;GASG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;;GASG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;GAOG;AACU,QAAA,gDAAgD,GAAG,6CAAsD,CAAC;AAEvH;;;;;;;;;;;;;;GAcG;AACI,MAAM,2BAA2B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,0BAA0B,GAAG,EAAE,CAAC;AAA/E,QAAA,2BAA2B,+BAAoD;AAE5F;;;;;;;;;;;;;;GAcG;AACI,MAAM,sBAAsB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,qBAAqB,GAAG,EAAE,CAAC;AAArE,QAAA,sBAAsB,0BAA+C;AAElF;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;;;GASG;AACI,MAAM,6BAA6B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,4BAA4B,GAAG,EAAE,CAAC;AAAnF,QAAA,6BAA6B,iCAAsD;AAEhG;;;;;;;;;GASG;AACI,MAAM,wBAAwB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,uBAAuB,GAAG,EAAE,CAAC;AAAzE,QAAA,wBAAwB,4BAAiD;AAEtF;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;;GASG;AACI,MAAM,8BAA8B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,6BAA6B,GAAG,EAAE,CAAC;AAArF,QAAA,8BAA8B,kCAAuD;AAElG;;;;;;;;;GASG;AACI,MAAM,yBAAyB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,wBAAwB,GAAG,EAAE,CAAC;AAA3E,QAAA,yBAAyB,6BAAkD;AAExF;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;;GASG;AACI,MAAM,uBAAuB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,sBAAsB,GAAG,EAAE,CAAC;AAAvE,QAAA,uBAAuB,2BAAgD;AAEpF;;;;;;;;;GASG;AACI,MAAM,kBAAkB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAA7D,QAAA,kBAAkB,sBAA2C;AAE1E;;;;;;GAMG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;;GASG;AACI,MAAM,6BAA6B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,4BAA4B,GAAG,EAAE,CAAC;AAAnF,QAAA,6BAA6B,iCAAsD;AAEhG;;;;;;;;;GASG;AACI,MAAM,wBAAwB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,uBAAuB,GAAG,EAAE,CAAC;AAAzE,QAAA,wBAAwB,4BAAiD;AAEtF;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;;;GAUG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;GAEG;AACU,QAAA,gCAAgC,GAAG,QAAiB,CAAC;AAElE;;GAEG;AACU,QAAA,qCAAqC,GAAG,aAAsB,CAAC;AAE5E;;;;;;;;;;;;;;GAcG;AACI,MAAM,wBAAwB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,uBAAuB,GAAG,EAAE,CAAC;AAAzE,QAAA,wBAAwB,4BAAiD;AAEtF;;;;;;;;;;;;;;GAcG;AACI,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,kBAAkB,GAAG,EAAE,CAAC;AAA/D,QAAA,mBAAmB,uBAA4C;AAE5E;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;;;;;;;;;;GAiBG;AACI,MAAM,uBAAuB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,sBAAsB,GAAG,EAAE,CAAC;AAAvE,QAAA,uBAAuB,2BAAgD;AAEpF;;;;;;;;;;;;;;;;;GAiBG;AACI,MAAM,kBAAkB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAA7D,QAAA,kBAAkB,sBAA2C;AAE1E;;;;;;;;GAQG;AACI,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,kBAAkB,GAAG,EAAE,CAAC;AAA/D,QAAA,mBAAmB,uBAA4C;AAE5E;;;;;;GAMG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;;GASG;AACI,MAAM,8BAA8B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,6BAA6B,GAAG,EAAE,CAAC;AAArF,QAAA,8BAA8B,kCAAuD;AAElG;;;;;;;;;GASG;AACI,MAAM,yBAAyB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,wBAAwB,GAAG,EAAE,CAAC;AAA3E,QAAA,yBAAyB,6BAAkD;AAExF;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;GASG;AACI,MAAM,+BAA+B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,8BAA8B,GAAG,EAAE,CAAC;AAAvF,QAAA,+BAA+B,mCAAwD;AAEpG;;;;;;;;;GASG;AACI,MAAM,0BAA0B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,yBAAyB,GAAG,EAAE,CAAC;AAA7E,QAAA,0BAA0B,8BAAmD;AAE1F;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;GAOG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;GAEG;AACU,QAAA,gCAAgC,GAAG,WAAoB,CAAC;AAErE;;GAEG;AACU,QAAA,kCAAkC,GAAG,aAAsB,CAAC;AAEzE;;GAEG;AACU,QAAA,+BAA+B,GAAG,UAAmB,CAAC;AAEnE;;GAEG;AACU,QAAA,2BAA2B,GAAG,OAAgB,CAAC;AAE5D;;GAEG;AACU,QAAA,6CAA6C,GAAG,uBAAgC,CAAC;AAE9F;;GAEG;AACU,QAAA,4BAA4B,GAAG,QAAiB,CAAC;AAE9D;;;;;;;GAOG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,yCAAyC,GAAG,aAAsB,CAAC;AAEhF;;GAEG;AACU,QAAA,2CAA2C,GAAG,eAAwB,CAAC;AAEpF;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;GAIG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;GAEG;AACU,QAAA,yBAAyB,GAAG,QAAiB,CAAC;AAE3D;;GAEG;AACU,QAAA,yBAAyB,GAAG,QAAiB,CAAC;AAE3D;;;;;;;;;GASG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;GASG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;GAMG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;GAEG;AACU,QAAA,2BAA2B,GAAG,UAAmB,CAAC;AAE/D;;GAEG;AACU,QAAA,uBAAuB,GAAG,MAAe,CAAC;AAEvD;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;;;GAUG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;;GAOG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;GASG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;;;;;GAUG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;GAMG;AACU,QAAA,uCAAuC,GAAG,oCAA6C,CAAC;AAErG;;;;;;;;GAQG;AACU,QAAA,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;;;;;;;;GASG;AACU,QAAA,uCAAuC,GAAG,oCAA6C,CAAC;AAErG;;;;;;;;GAQG;AACU,QAAA,uCAAuC,GAAG,oCAA6C,CAAC;AAErG;;;;;;GAMG;AACU,QAAA,8CAA8C,GAAG,2CAAoD,CAAC;AAEnH;;;;;;GAMG;AACU,QAAA,8CAA8C,GAAG,2CAAoD,CAAC;AAEnH;;;;;;GAMG;AACU,QAAA,wCAAwC,GAAG,qCAA8C,CAAC;AAEvG;;;;;;GAMG;AACU,QAAA,kDAAkD,GAAG,+CAAwD,CAAC;AAE3H;;;;;;GAMG;AACU,QAAA,8CAA8C,GAAG,2CAAoD,CAAC;AAEnH;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;;;GAQG;AACU,QAAA,0CAA0C,GAAG,uCAAgD,CAAC;AAE3G;;;;;;;;GAQG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;GAQG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,sCAAsC,GAAG,mCAA4C,CAAC;AAEnG;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;;;GASG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,sCAAsC,GAAG,mCAA4C,CAAC;AAEnG;;;;;;;;;GASG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;;;;GAUG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;GAQG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;GAKG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;GAEG;AACU,QAAA,qCAAqC,GAAG,QAAiB,CAAC;AAEvE;;GAEG;AACU,QAAA,sCAAsC,GAAG,SAAkB,CAAC;AAEzE;;GAEG;AACU,QAAA,sCAAsC,GAAG,SAAkB,CAAC;AAEzE;;GAEG;AACU,QAAA,sCAAsC,GAAG,SAAkB,CAAC;AAEzE;;GAEG;AACU,QAAA,sCAAsC,GAAG,SAAkB,CAAC;AAEzE;;GAEG;AACU,QAAA,mCAAmC,GAAG,MAAe,CAAC;AAEnE;;GAEG;AACU,QAAA,qCAAqC,GAAG,QAAiB,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,+CAA+C,GAAG,4CAAqD,CAAC;AAErH;;;;;;GAMG;AACU,QAAA,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;;;;;;;GAQG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;GAIG;AACU,QAAA,yCAAyC,GAAG,sCAA+C,CAAC;AAEzG;;GAEG;AACU,QAAA,uDAAuD,GAAG,cAAuB,CAAC;AAE/F;;GAEG;AACU,QAAA,qDAAqD,GAAG,YAAqB,CAAC;AAE3F;;;;;;GAMG;AACU,QAAA,gDAAgD,GAAG,6CAAsD,CAAC;AAEvH;;;;;;GAMG;AACU,QAAA,kDAAkD,GAAG,+CAAwD,CAAC;AAE3H;;;;;;GAMG;AACU,QAAA,qCAAqC,GAAG,kCAA2C,CAAC;AAEjG;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;GAIG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;GAEG;AACU,QAAA,2CAA2C,GAAG,OAAgB,CAAC;AAE5E;;GAEG;AACU,QAAA,0CAA0C,GAAG,MAAe,CAAC;AAE1E;;GAEG;AACU,QAAA,4CAA4C,GAAG,QAAiB,CAAC;AAE9E;;GAEG;AACU,QAAA,iDAAiD,GAAG,aAAsB,CAAC;AAExF;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;;;GAQG;AACU,QAAA,uDAAuD,GAAG,oDAA6D,CAAC;AAErI;;;;GAIG;AACU,QAAA,4CAA4C,GAAG,yCAAkD,CAAC;AAE/G;;GAEG;AACU,QAAA,qDAAqD,GAAG,SAAkB,CAAC;AAExF;;GAEG;AACU,QAAA,sDAAsD,GAAG,UAAmB,CAAC;AAE1F;;GAEG;AACU,QAAA,yDAAyD,GAAG,aAAsB,CAAC;AAEhG;;GAEG;AACU,QAAA,mDAAmD,GAAG,OAAgB,CAAC;AAEpF;;;;;;GAMG;AACU,QAAA,gDAAgD,GAAG,6CAAsD,CAAC;AAEvH;;;;;;GAMG;AACU,QAAA,+CAA+C,GAAG,4CAAqD,CAAC;AAErH;;;;;;GAMG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;GAEG;AACU,QAAA,+BAA+B,GAAG,UAAmB,CAAC;AAEnE;;GAEG;AACU,QAAA,8BAA8B,GAAG,SAAkB,CAAC;AAEjE;;GAEG;AACU,QAAA,gCAAgC,GAAG,WAAoB,CAAC;AAErE;;GAEG;AACU,QAAA,gCAAgC,GAAG,WAAoB,CAAC;AAErE;;GAEG;AACU,QAAA,iCAAiC,GAAG,YAAqB,CAAC;AAEvE;;GAEG;AACU,QAAA,0BAA0B,GAAG,KAAc,CAAC;AAEzD;;GAEG;AACU,QAAA,4BAA4B,GAAG,OAAgB,CAAC;AAE7D;;GAEG;AACU,QAAA,6BAA6B,GAAG,QAAiB,CAAC;AAE/D;;GAEG;AACU,QAAA,+BAA+B,GAAG,UAAmB,CAAC;AAEnE;;GAEG;AACU,QAAA,+BAA+B,GAAG,UAAmB,CAAC;AAEnE;;GAEG;AACU,QAAA,iCAAiC,GAAG,YAAqB,CAAC;AAEvE;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;;;GAUG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;GAEG;AACU,QAAA,0BAA0B,GAAG,MAAe,CAAC;AAE1D;;GAEG;AACU,QAAA,2BAA2B,GAAG,OAAgB,CAAC;AAE5D;;GAEG;AACU,QAAA,0BAA0B,GAAG,MAAe,CAAC;AAE1D;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,0BAA0B,GAAG,QAAiB,CAAC;AAE5D;;GAEG;AACU,QAAA,yBAAyB,GAAG,OAAgB,CAAC;AAE1D;;GAEG;AACU,QAAA,wBAAwB,GAAG,MAAe,CAAC;AAExD;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;;GAOG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;GAEG;AACU,QAAA,yCAAyC,GAAG,YAAqB,CAAC;AAE/E;;GAEG;AACU,QAAA,qCAAqC,GAAG,QAAiB,CAAC;AAEvE;;GAEG;AACU,QAAA,sCAAsC,GAAG,SAAkB,CAAC;AAEzE;;GAEG;AACU,QAAA,0CAA0C,GAAG,aAAsB,CAAC;AAEjF;;GAEG;AACU,QAAA,yCAAyC,GAAG,YAAqB,CAAC;AAE/E;;GAEG;AACU,QAAA,yCAAyC,GAAG,YAAqB,CAAC;AAE/E;;GAEG;AACU,QAAA,uCAAuC,GAAG,UAAmB,CAAC;AAE3E;;GAEG;AACU,QAAA,qCAAqC,GAAG,QAAiB,CAAC;AAEvE;;GAEG;AACU,QAAA,2CAA2C,GAAG,cAAuB,CAAC;AAEnF;;GAEG;AACU,QAAA,uCAAuC,GAAG,UAAmB,CAAC;AAE3E;;GAEG;AACU,QAAA,wCAAwC,GAAG,WAAoB,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,+CAA+C,GAAG,gBAAyB,CAAC;AAEzF;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,oCAAoC,GAAG,KAAc,CAAC;AAEnE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,oCAAoC,GAAG,KAAc,CAAC;AAEnE;;GAEG;AACU,QAAA,uCAAuC,GAAG,QAAiB,CAAC;AAEzE;;GAEG;AACU,QAAA,mCAAmC,GAAG,IAAa,CAAC;AAEjE;;GAEG;AACU,QAAA,sCAAsC,GAAG,OAAgB,CAAC;AAEvE;;GAEG;AACU,QAAA,yCAAyC,GAAG,UAAmB,CAAC;AAE7E;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAe,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;GAEG;AACU,QAAA,yCAAyC,GAAG,aAAsB,CAAC;AAEhF;;GAEG;AACU,QAAA,qCAAqC,GAAG,SAAkB,CAAC;AAExE;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;GAEG;AACU,QAAA,mCAAmC,GAAG,OAAgB,CAAC;AAEpE;;;;;;;GAOG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;GAEG;AACU,QAAA,kCAAkC,GAAG,SAAkB,CAAC;AAErE;;GAEG;AACU,QAAA,mCAAmC,GAAG,UAAmB,CAAC;AAEvE;;;;GAIG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;GAEG;AACU,QAAA,mCAAmC,GAAG,QAAiB,CAAC;AAErE;;GAEG;AACU,QAAA,iCAAiC,GAAG,MAAe,CAAC;AAEjE;;;;;;;;;GASG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;GAEG;AACU,QAAA,mCAAmC,GAAG,UAAmB,CAAC;AAEvE;;GAEG;AACU,QAAA,uCAAuC,GAAG,cAAuB,CAAC;AAE/E;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;GAOG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;GAQG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;GAIG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;GAEG;AACU,QAAA,iBAAiB,GAAG,KAAc,CAAC;AAEhD;;GAEG;AACU,QAAA,oBAAoB,GAAG,QAAiB,CAAC;AAEtD;;GAEG;AACU,QAAA,0BAA0B,GAAG,cAAuB,CAAC;AAElE;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAkB,CAAC;AAExD;;GAEG;AACU,QAAA,kBAAkB,GAAG,MAAe,CAAC;AAElD;;GAEG;AACU,QAAA,mBAAmB,GAAG,OAAgB,CAAC;AAEpD;;GAEG;AACU,QAAA,oBAAoB,GAAG,QAAiB,CAAC;AAEtD;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAkB,CAAC;AAExD;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAkB,CAAC;AAExD;;GAEG;AACU,QAAA,qBAAqB,GAAG,SAAkB,CAAC;AAExD;;GAEG;AACU,QAAA,kBAAkB,GAAG,MAAe,CAAC;AAElD;;;;;;;GAOG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;;GAUG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;GAEG;AACU,QAAA,gDAAgD,GAAG,wBAAiC,CAAC;AAElG;;GAEG;AACU,QAAA,iDAAiD,GAAG,yBAAkC,CAAC;AAEpG;;GAEG;AACU,QAAA,gDAAgD,GAAG,wBAAiC,CAAC;AAElG;;GAEG;AACU,QAAA,mDAAmD,GAAG,2BAAoC,CAAC;AAExG;;GAEG;AACU,QAAA,iDAAiD,GAAG,yBAAkC,CAAC;AAEpG;;GAEG;AACU,QAAA,qDAAqD,GAAG,6BAAsC,CAAC;AAE5G;;GAEG;AACU,QAAA,wDAAwD,GAAG,gCAAyC,CAAC;AAElH;;GAEG;AACU,QAAA,sDAAsD,GAAG,8BAAuC,CAAC;AAE9G;;GAEG;AACU,QAAA,gDAAgD,GAAG,wBAAiC,CAAC;AAElG;;GAEG;AACU,QAAA,mDAAmD,GAAG,2BAAoC,CAAC;AAExG;;GAEG;AACU,QAAA,iDAAiD,GAAG,yBAAkC,CAAC;AAEpG;;GAEG;AACU,QAAA,gDAAgD,GAAG,wBAAiC,CAAC;AAElG;;GAEG;AACU,QAAA,8CAA8C,GAAG,sBAA+B,CAAC;AAE9F;;GAEG;AACU,QAAA,+CAA+C,GAAG,uBAAgC,CAAC;AAEhG;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;GAEG;AACU,QAAA,oCAAoC,GAAG,MAAe,CAAC;AAEpE;;GAEG;AACU,QAAA,iDAAiD,GAAG,mBAA4B,CAAC;AAE9F;;GAEG;AACU,QAAA,2CAA2C,GAAG,aAAsB,CAAC;AAElF;;;;;;GAMG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;GAQG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;;;GAQG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;GAEG;AACU,QAAA,6CAA6C,GAAG,aAAsB,CAAC;AAEpF;;GAEG;AACU,QAAA,2CAA2C,GAAG,WAAoB,CAAC;AAEhF;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;GAEG;AACU,QAAA,4BAA4B,GAAG,MAAe,CAAC;AAE5D;;GAEG;AACU,QAAA,4BAA4B,GAAG,MAAe,CAAC;AAE5D;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;;;;;;GAeG;AACI,MAAM,iCAAiC,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,gCAAgC,GAAG,EAAE,CAAC;AAA3F,QAAA,iCAAiC,qCAA0D;AAExG;;;;;;GAMG;AACU,QAAA,oCAAoC,GAAG,iCAA0C,CAAC;AAE/F;;;;;;GAMG;AACU,QAAA,mCAAmC,GAAG,gCAAyC,CAAC;AAE7F;;;;;;GAMG;AACU,QAAA,wCAAwC,GAAG,qCAA8C,CAAC;AAEvG;;;;;;;;GAQG;AACU,QAAA,0CAA0C,GAAG,uCAAgD,CAAC;AAE3G;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;GAIG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;GASG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;GAIG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;GAEG;AACU,QAAA,qCAAqC,GAAG,OAAgB,CAAC;AAEtE;;GAEG;AACU,QAAA,qCAAqC,GAAG,OAAgB,CAAC;AAEtE;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;;;;GAUG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;GAQG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;GAEG;AACU,QAAA,6BAA6B,GAAG,MAAe,CAAC;AAE7D;;GAEG;AACU,QAAA,gCAAgC,GAAG,SAAkB,CAAC;AAEnE;;GAEG;AACU,QAAA,+BAA+B,GAAG,QAAiB,CAAC;AAEjE;;GAEG;AACU,QAAA,2BAA2B,GAAG,IAAa,CAAC;AAEzD;;GAEG;AACU,QAAA,4BAA4B,GAAG,KAAc,CAAC;AAE3D;;GAEG;AACU,QAAA,+BAA+B,GAAG,QAAiB,CAAC;AAEjE;;GAEG;AACU,QAAA,+BAA+B,GAAG,QAAiB,CAAC;AAEjE;;GAEG;AACU,QAAA,6BAA6B,GAAG,MAAe,CAAC;AAE7D;;GAEG;AACU,QAAA,4BAA4B,GAAG,KAAc,CAAC;AAE3D;;GAEG;AACU,QAAA,6BAA6B,GAAG,MAAe,CAAC;AAE7D;;GAEG;AACU,QAAA,6BAA6B,GAAG,MAAe,CAAC;AAE7D;;GAEG;AACU,QAAA,6BAA6B,GAAG,MAAe,CAAC;AAE7D;;;;GAIG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;GAEG;AACU,QAAA,wCAAwC,GAAG,SAAkB,CAAC;AAE3E;;GAEG;AACU,QAAA,+CAA+C,GAAG,gBAAyB,CAAC;AAEzF;;GAEG;AACU,QAAA,0CAA0C,GAAG,WAAoB,CAAC;AAE/E;;GAEG;AACU,QAAA,0CAA0C,GAAG,WAAoB,CAAC;AAE/E;;GAEG;AACU,QAAA,kDAAkD,GAAG,mBAA4B,CAAC;AAE/F;;GAEG;AACU,QAAA,oDAAoD,GAAG,qBAA8B,CAAC;AAEnG;;GAEG;AACU,QAAA,yCAAyC,GAAG,UAAmB,CAAC;AAE7E;;GAEG;AACU,QAAA,iDAAiD,GAAG,kBAA2B,CAAC;AAE7F;;GAEG;AACU,QAAA,0CAA0C,GAAG,WAAoB,CAAC;AAE/E;;GAEG;AACU,QAAA,6CAA6C,GAAG,cAAuB,CAAC;AAErF;;GAEG;AACU,QAAA,kDAAkD,GAAG,mBAA4B,CAAC;AAE/F;;GAEG;AACU,QAAA,mDAAmD,GAAG,oBAA6B,CAAC;AAEjG;;GAEG;AACU,QAAA,gDAAgD,GAAG,iBAA0B,CAAC;AAE3F;;GAEG;AACU,QAAA,4CAA4C,GAAG,aAAsB,CAAC;AAEnF;;GAEG;AACU,QAAA,8CAA8C,GAAG,eAAwB,CAAC;AAEvF;;GAEG;AACU,QAAA,wCAAwC,GAAG,SAAkB,CAAC;AAE3E;;;;;;;;;;;;GAYG;AACI,MAAM,qCAAqC,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,oCAAoC,GAAG,EAAE,CAAC;AAAnG,QAAA,qCAAqC,yCAA8D;AAEhH;;;;;;;;;;;;GAYG;AACI,MAAM,sCAAsC,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,qCAAqC,GAAG,EAAE,CAAC;AAArG,QAAA,sCAAsC,0CAA+D;AAElH;;;;;;;;;;;;GAYG;AACI,MAAM,8BAA8B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,6BAA6B,GAAG,EAAE,CAAC;AAArF,QAAA,8BAA8B,kCAAuD;AAElG;;;;;;;;;;;;GAYG;AACI,MAAM,+BAA+B,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,8BAA8B,GAAG,EAAE,CAAC;AAAvF,QAAA,+BAA+B,mCAAwD;AAEpG;;;;GAIG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;GAEG;AACU,QAAA,6BAA6B,GAAG,CAAU,CAAC;AAExD;;GAEG;AACU,QAAA,oCAAoC,GAAG,CAAU,CAAC;AAE/D;;GAEG;AACU,QAAA,kCAAkC,GAAG,CAAU,CAAC;AAE7D;;GAEG;AACU,QAAA,2CAA2C,GAAG,CAAU,CAAC;AAEtE;;GAEG;AACU,QAAA,4CAA4C,GAAG,CAAU,CAAC;AAEvE;;GAEG;AACU,QAAA,oCAAoC,GAAG,CAAU,CAAC;AAE/D;;GAEG;AACU,QAAA,yCAAyC,GAAG,CAAU,CAAC;AAEpE;;GAEG;AACU,QAAA,4CAA4C,GAAG,CAAU,CAAC;AAEvE;;GAEG;AACU,QAAA,6CAA6C,GAAG,CAAU,CAAC;AAExE;;GAEG;AACU,QAAA,8CAA8C,GAAG,CAAU,CAAC;AAEzE;;GAEG;AACU,QAAA,kCAAkC,GAAG,EAAW,CAAC;AAE9D;;GAEG;AACU,QAAA,uCAAuC,GAAG,EAAW,CAAC;AAEnE;;GAEG;AACU,QAAA,wCAAwC,GAAG,EAAW,CAAC;AAEpE;;GAEG;AACU,QAAA,mCAAmC,GAAG,EAAW,CAAC;AAE/D;;GAEG;AACU,QAAA,sCAAsC,GAAG,EAAW,CAAC;AAElE;;GAEG;AACU,QAAA,oCAAoC,GAAG,EAAW,CAAC;AAEhE;;GAEG;AACU,QAAA,0CAA0C,GAAG,EAAW,CAAC;AAEtE;;;;;;;GAOG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;GAOG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;GAQG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;;GAOG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;GAIG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;GAKG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;GAIG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;GAEG;AACU,QAAA,+BAA+B,GAAG,UAAmB,CAAC;AAEnE;;GAEG;AACU,QAAA,2BAA2B,GAAG,MAAe,CAAC;AAE3D;;;;GAIG;AACU,QAAA,kCAAkC,GAAG,+BAAwC,CAAC;AAE3F;;;;;;;;GAQG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;;;GAQG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;GAIG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;GAEG;AACU,QAAA,6BAA6B,GAAG,cAAuB,CAAC;AAErE;;GAEG;AACU,QAAA,4BAA4B,GAAG,aAAsB,CAAC;AAEnE;;GAEG;AACU,QAAA,2BAA2B,GAAG,YAAqB,CAAC;AAEjE;;GAEG;AACU,QAAA,qBAAqB,GAAG,MAAe,CAAC;AAErD;;GAEG;AACU,QAAA,yBAAyB,GAAG,UAAmB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAAqC,CAAC;AAErF;;;;;;;GAOG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;GAQG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;;GAUG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;GAOG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;GAQG;AACU,QAAA,UAAU,GAAG,OAAgB,CAAC;AAE3C;;GAEG;AACU,QAAA,gBAAgB,GAAG,MAAe,CAAC;AAEhD;;GAEG;AACU,QAAA,gBAAgB,GAAG,MAAe,CAAC;AAEhD;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;;;;;;;;GASG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;GAEG;AACU,QAAA,2BAA2B,GAAG,MAAe,CAAC;AAE3D;;GAEG;AACU,QAAA,gCAAgC,GAAG,WAAoB,CAAC;AAErE;;GAEG;AACU,QAAA,6BAA6B,GAAG,QAAiB,CAAC;AAE/D;;GAEG;AACU,QAAA,2BAA2B,GAAG,MAAe,CAAC;AAE3D;;GAEG;AACU,QAAA,4BAA4B,GAAG,OAAgB,CAAC;AAE7D;;GAEG;AACU,QAAA,6BAA6B,GAAG,QAAiB,CAAC;AAE/D;;GAEG;AACU,QAAA,2BAA2B,GAAG,MAAe,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;GAEG;AACU,QAAA,sCAAsC,GAAG,UAAmB,CAAC;AAE1E;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;GAEG;AACU,QAAA,kCAAkC,GAAG,OAAgB,CAAC;AAEnE;;GAEG;AACU,QAAA,iCAAiC,GAAG,MAAe,CAAC;AAEjE;;GAEG;AACU,QAAA,kCAAkC,GAAG,OAAgB,CAAC;AAEnE;;GAEG;AACU,QAAA,oCAAoC,GAAG,SAAkB,CAAC;AAEvE;;GAEG;AACU,QAAA,iCAAiC,GAAG,MAAe,CAAC;AAEjE;;GAEG;AACU,QAAA,iCAAiC,GAAG,MAAe,CAAC;AAEjE;;;;;;;GAOG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;GAEG;AACU,QAAA,iCAAiC,GAAG,SAAkB,CAAC;AAEpE;;GAEG;AACU,QAAA,gCAAgC,GAAG,QAAiB,CAAC;AAElE;;GAEG;AACU,QAAA,8BAA8B,GAAG,MAAe,CAAC;AAE9D;;GAEG;AACU,QAAA,gCAAgC,GAAG,QAAiB,CAAC;AAElE;;GAEG;AACU,QAAA,8BAA8B,GAAG,MAAe,CAAC;AAE9D;;;;;;;;GAQG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;GAEG;AACU,QAAA,gCAAgC,GAAG,OAAgB,CAAC;AAEjE;;GAEG;AACU,QAAA,qCAAqC,GAAG,YAAqB,CAAC;AAE3E;;GAEG;AACU,QAAA,kCAAkC,GAAG,SAAkB,CAAC;AAErE;;GAEG;AACU,QAAA,iCAAiC,GAAG,QAAiB,CAAC;AAEnE;;GAEG;AACU,QAAA,sCAAsC,GAAG,aAAsB,CAAC;AAE7E;;GAEG;AACU,QAAA,qCAAqC,GAAG,YAAqB,CAAC;AAE3E;;GAEG;AACU,QAAA,qCAAqC,GAAG,YAAqB,CAAC;AAE3E;;GAEG;AACU,QAAA,mCAAmC,GAAG,UAAmB,CAAC;AAEvE;;GAEG;AACU,QAAA,iCAAiC,GAAG,QAAiB,CAAC;AAEnE;;GAEG;AACU,QAAA,mCAAmC,GAAG,UAAmB,CAAC;AAEvE;;GAEG;AACU,QAAA,mCAAmC,GAAG,UAAmB,CAAC;AAEvE;;GAEG;AACU,QAAA,oCAAoC,GAAG,WAAoB,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,gCAAgC,GAAG,IAAa,CAAC;AAE9D;;GAEG;AACU,QAAA,iCAAiC,GAAG,KAAc,CAAC;AAEhE;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;GAEG;AACU,QAAA,8BAA8B,GAAG,MAAe,CAAC;AAE9D;;GAEG;AACU,QAAA,8BAA8B,GAAG,MAAe,CAAC;AAE9D;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;GAEG;AACU,QAAA,8BAA8B,GAAG,OAAgB,CAAC;AAE/D;;GAEG;AACU,QAAA,8BAA8B,GAAG,OAAgB,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;GAEG;AACU,QAAA,mCAAmC,GAAG,SAAkB,CAAC;AAEtE;;GAEG;AACU,QAAA,mCAAmC,GAAG,SAAkB,CAAC;AAEtE;;GAEG;AACU,QAAA,oCAAoC,GAAG,UAAmB,CAAC;AAExE;;GAEG;AACU,QAAA,mCAAmC,GAAG,SAAkB,CAAC;AAEtE;;;;;;;;GAQG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,qCAAqC,GAAG,SAAkB,CAAC;AAExE;;GAEG;AACU,QAAA,qCAAqC,GAAG,SAAkB,CAAC;AAExE;;GAEG;AACU,QAAA,sCAAsC,GAAG,UAAmB,CAAC;AAE1E;;GAEG;AACU,QAAA,qCAAqC,GAAG,SAAkB,CAAC;AAExE;;;;;;;;;GASG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;GAOG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;GAEG;AACU,QAAA,kCAAkC,GAAG,MAAe,CAAC;AAElE;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;;;;;GAWG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;GAEG;AACU,QAAA,mCAAmC,GAAG,SAAkB,CAAC;AAEtE;;GAEG;AACU,QAAA,mCAAmC,GAAG,SAAkB,CAAC;AAEtE;;GAEG;AACU,QAAA,uCAAuC,GAAG,aAAsB,CAAC;AAE9E;;GAEG;AACU,QAAA,mCAAmC,GAAG,SAAkB,CAAC;AAEtE;;GAEG;AACU,QAAA,mCAAmC,GAAG,SAAkB,CAAC;AAEtE;;GAEG;AACU,QAAA,qCAAqC,GAAG,WAAoB,CAAC;AAE1E;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;;;;GASG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;GAQG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;GAIG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,2BAA2B,GAAG,KAAc,CAAC;AAE1D;;GAEG;AACU,QAAA,2BAA2B,GAAG,KAAc,CAAC;AAE1D;;;;;;;GAOG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,gBAAgB,GAAG,aAAsB,CAAC;AAEvD;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;;;;;GAMG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,2BAA2B,GAAG,wBAAiC,CAAC;AAE7E;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;GAMG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;;;;GAWG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;;;;GASG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;;;GAUG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;GAMG;AACU,QAAA,aAAa,GAAG,UAAmB,CAAC;AAEjD;;;;;;;;;GASG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;GASG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;;;;;;;;GASG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;GAMG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;GAQG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,YAAY,GAAG,SAAkB,CAAC;AAE/C;;;;;;GAMG;AACU,QAAA,cAAc,GAAG,WAAoB,CAAC;AAEnD;;;;;;GAMG;AACU,QAAA,eAAe,GAAG,YAAqB,CAAC;AAErD;;;;;;;;;GASG;AACU,QAAA,oBAAoB,GAAG,iBAA0B,CAAC;AAE/D;;;;;;;;;;GAUG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;;;;;;GASG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,8BAA8B,GAAG,2BAAoC,CAAC;AAEnF;;GAEG;AACU,QAAA,mCAAmC,GAAG,KAAc,CAAC;AAElE;;GAEG;AACU,QAAA,oCAAoC,GAAG,MAAe,CAAC;AAEpE;;;;;;;;;GASG;AACU,QAAA,uBAAuB,GAAG,oBAA6B,CAAC;AAErE;;;;GAIG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;GAEG;AACU,QAAA,8BAA8B,GAAG,aAAsB,CAAC;AAErE;;GAEG;AACU,QAAA,wBAAwB,GAAG,OAAgB,CAAC;AAEzD;;GAEG;AACU,QAAA,wBAAwB,GAAG,OAAgB,CAAC;AAEzD;;GAEG;AACU,QAAA,yBAAyB,GAAG,QAAiB,CAAC;AAE3D;;;;;;GAMG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;GAEG;AACU,QAAA,qCAAqC,GAAG,YAAqB,CAAC;AAE3E;;GAEG;AACU,QAAA,6CAA6C,GAAG,oBAA6B,CAAC;AAE3F;;GAEG;AACU,QAAA,oCAAoC,GAAG,WAAoB,CAAC;AAEzE;;GAEG;AACU,QAAA,oCAAoC,GAAG,WAAoB,CAAC;AAEzE;;GAEG;AACU,QAAA,oCAAoC,GAAG,WAAoB,CAAC;AAEzE;;;;;;GAMG;AACU,QAAA,kBAAkB,GAAG,eAAwB,CAAC;AAE3D;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;GAEG;AACU,QAAA,6BAA6B,GAAG,QAAiB,CAAC;AAE/D;;GAEG;AACU,QAAA,6BAA6B,GAAG,QAAiB,CAAC;AAE/D;;GAEG;AACU,QAAA,2BAA2B,GAAG,MAAe,CAAC;AAE3D;;GAEG;AACU,QAAA,0BAA0B,GAAG,KAAc,CAAC;AAEzD;;;;;;;;GAQG;AACU,QAAA,qBAAqB,GAAG,kBAA2B,CAAC;AAEjE;;;;;;;GAOG;AACU,QAAA,yBAAyB,GAAG,sBAA+B,CAAC;AAEzE;;GAEG;AACU,QAAA,gCAAgC,GAAG,OAAgB,CAAC;AAEjE;;GAEG;AACU,QAAA,kCAAkC,GAAG,SAAkB,CAAC;AAErE;;;;;;;;GAQG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;;;;GASG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,iCAAiC,GAAG,WAAoB,CAAC;AAEtE;;GAEG;AACU,QAAA,6BAA6B,GAAG,OAAgB,CAAC;AAE9D;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;;;;;;;;;;GAWG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;;GAWG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;GAEG;AACU,QAAA,2BAA2B,GAAG,KAAc,CAAC;AAE1D;;;;;;;;;;GAUG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;;;;;;;;;;;;;;;;;;;;GAqBG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;;;;;GAUG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC;AAEnE;;GAEG;AACU,QAAA,8BAA8B,GAAG,QAAiB,CAAC;AAEhE;;GAEG;AACU,QAAA,2BAA2B,GAAG,KAAc,CAAC;AAE1D;;;;;;;GAOG;AACU,QAAA,iBAAiB,GAAG,cAAuB,CAAC;AAEzD;;GAEG;AACU,QAAA,yBAAyB,GAAG,QAAiB,CAAC;AAE3D;;GAEG;AACU,QAAA,sBAAsB,GAAG,KAAc,CAAC;AAErD;;;;;;;;GAQG;AACU,QAAA,6BAA6B,GAAG,0BAAmC,CAAC;AAEjF;;;;;;;;;;GAUG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;;;;GAWG;AACU,QAAA,wBAAwB,GAAG,qBAA8B,CAAC;AAEvE;;;;;;;;;GASG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;;;;;GAWG;AACU,QAAA,gCAAgC,GAAG,6BAAsC,CAAC;AAEvF;;;;;;;;;GASG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;GAEG;AACU,QAAA,oCAAoC,GAAG,QAAiB,CAAC;AAEtE;;GAEG;AACU,QAAA,iCAAiC,GAAG,KAAc,CAAC;AAEhE;;;;;;;;;;GAUG;AACU,QAAA,4BAA4B,GAAG,yBAAkC,CAAC;AAE/E;;;;;;;GAOG;AACU,QAAA,iCAAiC,GAAG,8BAAuC,CAAC;AAEzF;;GAEG;AACU,QAAA,wCAAwC,GAAG,OAAgB,CAAC;AAEzE;;GAEG;AACU,QAAA,yCAAyC,GAAG,QAAiB,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,0BAA0B,GAAG,uBAAgC,CAAC;AAE3E;;;;;;GAMG;AACU,QAAA,mBAAmB,GAAG,gBAAyB,CAAC;AAE7D;;;;;;GAMG;AACU,QAAA,sBAAsB,GAAG,mBAA4B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n//----------------------------------------------------------------------------------------------------------\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/registry/stable/attributes.ts.j2\n//----------------------------------------------------------------------------------------------------------\n\n/**\n * This attribute represents the state of the application.\n *\n * @example created\n *\n * @note The Android lifecycle states are defined in [Activity lifecycle callbacks](https://developer.android.com/guide/components/activities/activity-lifecycle#lc), and from which the `OS identifiers` are derived.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ANDROID_APP_STATE = 'android.app.state' as const;\n\n/**\n * Enum value \"background\" for attribute {@link ATTR_ANDROID_APP_STATE}.\n */\nexport const ANDROID_APP_STATE_VALUE_BACKGROUND = \"background\" as const;\n\n/**\n * Enum value \"created\" for attribute {@link ATTR_ANDROID_APP_STATE}.\n */\nexport const ANDROID_APP_STATE_VALUE_CREATED = \"created\" as const;\n\n/**\n * Enum value \"foreground\" for attribute {@link ATTR_ANDROID_APP_STATE}.\n */\nexport const ANDROID_APP_STATE_VALUE_FOREGROUND = \"foreground\" as const;\n\n/**\n * Uniquely identifies the framework API revision offered by a version (`os.version`) of the android operating system. More information can be found [here](https://developer.android.com/guide/topics/manifest/uses-sdk-element#ApiLevels).\n *\n * @example 33\n * @example 32\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ANDROID_OS_API_LEVEL = 'android.os.api_level' as const;\n\n/**\n * Deprecated. Use `android.app.state` body field instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Use `android.app.state` body field instead.\n */\nexport const ATTR_ANDROID_STATE = 'android.state' as const;\n\n/**\n * Enum value \"background\" for attribute {@link ATTR_ANDROID_STATE}.\n */\nexport const ANDROID_STATE_VALUE_BACKGROUND = \"background\" as const;\n\n/**\n * Enum value \"created\" for attribute {@link ATTR_ANDROID_STATE}.\n */\nexport const ANDROID_STATE_VALUE_CREATED = \"created\" as const;\n\n/**\n * Enum value \"foreground\" for attribute {@link ATTR_ANDROID_STATE}.\n */\nexport const ANDROID_STATE_VALUE_FOREGROUND = \"foreground\" as const;\n\n/**\n * A unique identifier representing the installation of an application on a specific device\n *\n * @example 2ab2916d-a51f-4ac8-80ee-45ac31a28092\n *\n * @note Its value **SHOULD** persist across launches of the same application installation, including through application upgrades.\n * It **SHOULD** change if the application is uninstalled or if all applications of the vendor are uninstalled.\n * Additionally, users might be able to reset this value (e.g. by clearing application data).\n * If an app is installed multiple times on the same device (e.g. in different accounts on Android), each `app.installation.id` **SHOULD** have a different value.\n * If multiple OpenTelemetry SDKs are used within the same application, they **SHOULD** use the same value for `app.installation.id`.\n * Hardware IDs (e.g. serial number, IMEI, MAC address) **MUST NOT** be used as the `app.installation.id`.\n *\n * For iOS, this value **SHOULD** be equal to the [vendor identifier](https://developer.apple.com/documentation/uikit/uidevice/identifierforvendor).\n *\n * For Android, examples of `app.installation.id` implementations include:\n *\n *   - [Firebase Installation ID](https://firebase.google.com/docs/projects/manage-installations).\n *   - A globally unique UUID which is persisted across sessions in your application.\n *   - [App set ID](https://developer.android.com/identity/app-set-id).\n *   - [`Settings.getString(Settings.Secure.ANDROID_ID)`](https://developer.android.com/reference/android/provider/Settings.Secure#ANDROID_ID).\n *\n * More information about Android identifier best practices can be found [here](https://developer.android.com/training/articles/user-data-ids).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_APP_INSTALLATION_ID = 'app.installation.id' as const;\n\n/**\n * The x (horizontal) coordinate of a screen coordinate, in screen pixels.\n *\n * @example 0\n * @example 131\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_APP_SCREEN_COORDINATE_X = 'app.screen.coordinate.x' as const;\n\n/**\n * The y (vertical) component of a screen coordinate, in screen pixels.\n *\n * @example 12\n * @example 99\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_APP_SCREEN_COORDINATE_Y = 'app.screen.coordinate.y' as const;\n\n/**\n * An identifier that uniquely differentiates this widget from other widgets in the same application.\n *\n * @example f9bc787d-ff05-48ad-90e1-fca1d46130b3\n * @example submit_order_1829\n *\n * @note A widget is an application component, typically an on-screen visual GUI element.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_APP_WIDGET_ID = 'app.widget.id' as const;\n\n/**\n * The name of an application widget.\n *\n * @example submit\n * @example attack\n * @example Clear Cart\n *\n * @note A widget is an application component, typically an on-screen visual GUI element.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_APP_WIDGET_NAME = 'app.widget.name' as const;\n\n/**\n * The provenance filename of the built attestation which directly relates to the build artifact filename. This filename **SHOULD** accompany the artifact at publish time. See the [SLSA Relationship](https://slsa.dev/spec/v1.0/distributing-provenance#relationship-between-artifacts-and-attestations) specification for more information.\n *\n * @example golang-binary-amd64-v0.1.0.attestation\n * @example docker-image-amd64-v0.1.0.intoto.json1\n * @example release-1.tar.gz.attestation\n * @example file-name-package.tar.gz.intoto.json1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ARTIFACT_ATTESTATION_FILENAME = 'artifact.attestation.filename' as const;\n\n/**\n * The full [hash value (see glossary)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-5.pdf), of the built attestation. Some envelopes in the [software attestation space](https://github.com/in-toto/attestation/tree/main/spec) also refer to this as the **digest**.\n *\n * @example 1b31dfcd5b7f9267bf2ff47651df1cfb9147b9e4df1f335accf65b4cda498408\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ARTIFACT_ATTESTATION_HASH = 'artifact.attestation.hash' as const;\n\n/**\n * The id of the build [software attestation](https://slsa.dev/attestation-model).\n *\n * @example 123\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ARTIFACT_ATTESTATION_ID = 'artifact.attestation.id' as const;\n\n/**\n * The human readable file name of the artifact, typically generated during build and release processes. Often includes the package name and version in the file name.\n *\n * @example golang-binary-amd64-v0.1.0\n * @example docker-image-amd64-v0.1.0\n * @example release-1.tar.gz\n * @example file-name-package.tar.gz\n *\n * @note This file name can also act as the [Package Name](https://slsa.dev/spec/v1.0/terminology#package-model)\n * in cases where the package ecosystem maps accordingly.\n * Additionally, the artifact [can be published](https://slsa.dev/spec/v1.0/terminology#software-supply-chain)\n * for others, but that is not a guarantee.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ARTIFACT_FILENAME = 'artifact.filename' as const;\n\n/**\n * The full [hash value (see glossary)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-5.pdf), often found in checksum.txt on a release of the artifact and used to verify package integrity.\n *\n * @example 9ff4c52759e2c4ac70b7d517bc7fcdc1cda631ca0045271ddd1b192544f8a3e9\n *\n * @note The specific algorithm used to create the cryptographic hash value is\n * not defined. In situations where an artifact has multiple\n * cryptographic hashes, it is up to the implementer to choose which\n * hash value to set here; this should be the most secure hash algorithm\n * that is suitable for the situation and consistent with the\n * corresponding attestation. The implementer can then provide the other\n * hash values through an additional set of attribute extensions as they\n * deem necessary.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ARTIFACT_HASH = 'artifact.hash' as const;\n\n/**\n * The [Package URL](https://github.com/package-url/purl-spec) of the [package artifact](https://slsa.dev/spec/v1.0/terminology#package-model) provides a standard way to identify and locate the packaged artifact.\n *\n * @example pkg:github/package-url/purl-spec@1209109710924\n * @example pkg:npm/foo@12.12.3\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ARTIFACT_PURL = 'artifact.purl' as const;\n\n/**\n * The version of the artifact.\n *\n * @example v0.1.0\n * @example 1.2.1\n * @example 122691-build\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ARTIFACT_VERSION = 'artifact.version' as const;\n\n/**\n * The unique identifier of the AWS Bedrock Guardrail. A [guardrail](https://docs.aws.amazon.com/bedrock/latest/userguide/guardrails.html) helps safeguard and prevent unwanted behavior from model responses or user messages.\n *\n * @example sgi5gkybzqak\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_BEDROCK_GUARDRAIL_ID = 'aws.bedrock.guardrail.id' as const;\n\n/**\n * The unique identifier of the AWS Bedrock Knowledge base. A [knowledge base](https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base.html) is a bank of information that can be queried by models to generate more relevant responses and augment prompts.\n *\n * @example XFWUPB9PAW\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_BEDROCK_KNOWLEDGE_BASE_ID = 'aws.bedrock.knowledge_base.id' as const;\n\n/**\n * The JSON-serialized value of each item in the `AttributeDefinitions` request field.\n *\n * @example [\"{ \"AttributeName\": \"string\", \"AttributeType\": \"string\" }\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS = 'aws.dynamodb.attribute_definitions' as const;\n\n/**\n * The value of the `AttributesToGet` request parameter.\n *\n * @example [\"lives\", \"id\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_ATTRIBUTES_TO_GET = 'aws.dynamodb.attributes_to_get' as const;\n\n/**\n * The value of the `ConsistentRead` request parameter.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_CONSISTENT_READ = 'aws.dynamodb.consistent_read' as const;\n\n/**\n * The JSON-serialized value of each item in the `ConsumedCapacity` response field.\n *\n * @example [\"{ \"CapacityUnits\": number, \"GlobalSecondaryIndexes\": { \"string\" : { \"CapacityUnits\": number, \"ReadCapacityUnits\": number, \"WriteCapacityUnits\": number } }, \"LocalSecondaryIndexes\": { \"string\" : { \"CapacityUnits\": number, \"ReadCapacityUnits\": number, \"WriteCapacityUnits\": number } }, \"ReadCapacityUnits\": number, \"Table\": { \"CapacityUnits\": number, \"ReadCapacityUnits\": number, \"WriteCapacityUnits\": number }, \"TableName\": \"string\", \"WriteCapacityUnits\": number }\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_CONSUMED_CAPACITY = 'aws.dynamodb.consumed_capacity' as const;\n\n/**\n * The value of the `Count` response parameter.\n *\n * @example 10\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_COUNT = 'aws.dynamodb.count' as const;\n\n/**\n * The value of the `ExclusiveStartTableName` request parameter.\n *\n * @example Users\n * @example CatsTable\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_EXCLUSIVE_START_TABLE = 'aws.dynamodb.exclusive_start_table' as const;\n\n/**\n * The JSON-serialized value of each item in the `GlobalSecondaryIndexUpdates` request field.\n *\n * @example [\"{ \"Create\": { \"IndexName\": \"string\", \"KeySchema\": [ { \"AttributeName\": \"string\", \"KeyType\": \"string\" } ], \"Projection\": { \"NonKeyAttributes\": [ \"string\" ], \"ProjectionType\": \"string\" }, \"ProvisionedThroughput\": { \"ReadCapacityUnits\": number, \"WriteCapacityUnits\": number } }\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES = 'aws.dynamodb.global_secondary_index_updates' as const;\n\n/**\n * The JSON-serialized value of each item of the `GlobalSecondaryIndexes` request field\n *\n * @example [\"{ \"IndexName\": \"string\", \"KeySchema\": [ { \"AttributeName\": \"string\", \"KeyType\": \"string\" } ], \"Projection\": { \"NonKeyAttributes\": [ \"string\" ], \"ProjectionType\": \"string\" }, \"ProvisionedThroughput\": { \"ReadCapacityUnits\": number, \"WriteCapacityUnits\": number } }\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES = 'aws.dynamodb.global_secondary_indexes' as const;\n\n/**\n * The value of the `IndexName` request parameter.\n *\n * @example name_to_group\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_INDEX_NAME = 'aws.dynamodb.index_name' as const;\n\n/**\n * The JSON-serialized value of the `ItemCollectionMetrics` response field.\n *\n * @example { \"string\" : [ { \"ItemCollectionKey\": { \"string\" : { \"B\": blob, \"BOOL\": boolean, \"BS\": [ blob ], \"L\": [ \"AttributeValue\" ], \"M\": { \"string\" : \"AttributeValue\" }, \"N\": \"string\", \"NS\": [ \"string\" ], \"NULL\": boolean, \"S\": \"string\", \"SS\": [ \"string\" ] } }, \"SizeEstimateRangeGB\": [ number ] } ] }\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_ITEM_COLLECTION_METRICS = 'aws.dynamodb.item_collection_metrics' as const;\n\n/**\n * The value of the `Limit` request parameter.\n *\n * @example 10\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_LIMIT = 'aws.dynamodb.limit' as const;\n\n/**\n * The JSON-serialized value of each item of the `LocalSecondaryIndexes` request field.\n *\n * @example [\"{ \"IndexArn\": \"string\", \"IndexName\": \"string\", \"IndexSizeBytes\": number, \"ItemCount\": number, \"KeySchema\": [ { \"AttributeName\": \"string\", \"KeyType\": \"string\" } ], \"Projection\": { \"NonKeyAttributes\": [ \"string\" ], \"ProjectionType\": \"string\" } }\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES = 'aws.dynamodb.local_secondary_indexes' as const;\n\n/**\n * The value of the `ProjectionExpression` request parameter.\n *\n * @example Title\n * @example Title, Price, Color\n * @example Title, Description, RelatedItems, ProductReviews\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_PROJECTION = 'aws.dynamodb.projection' as const;\n\n/**\n * The value of the `ProvisionedThroughput.ReadCapacityUnits` request parameter.\n *\n * @example 1.0\n * @example 2.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY = 'aws.dynamodb.provisioned_read_capacity' as const;\n\n/**\n * The value of the `ProvisionedThroughput.WriteCapacityUnits` request parameter.\n *\n * @example 1.0\n * @example 2.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY = 'aws.dynamodb.provisioned_write_capacity' as const;\n\n/**\n * The value of the `ScanIndexForward` request parameter.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_SCAN_FORWARD = 'aws.dynamodb.scan_forward' as const;\n\n/**\n * The value of the `ScannedCount` response parameter.\n *\n * @example 50\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_SCANNED_COUNT = 'aws.dynamodb.scanned_count' as const;\n\n/**\n * The value of the `Segment` request parameter.\n *\n * @example 10\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_SEGMENT = 'aws.dynamodb.segment' as const;\n\n/**\n * The value of the `Select` request parameter.\n *\n * @example ALL_ATTRIBUTES\n * @example COUNT\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_SELECT = 'aws.dynamodb.select' as const;\n\n/**\n * The number of items in the `TableNames` response parameter.\n *\n * @example 20\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_TABLE_COUNT = 'aws.dynamodb.table_count' as const;\n\n/**\n * The keys in the `RequestItems` object field.\n *\n * @example [\"Users\", \"Cats\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_TABLE_NAMES = 'aws.dynamodb.table_names' as const;\n\n/**\n * The value of the `TotalSegments` request parameter.\n *\n * @example 100\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_DYNAMODB_TOTAL_SEGMENTS = 'aws.dynamodb.total_segments' as const;\n\n/**\n * The ARN of an [ECS cluster](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/clusters.html).\n *\n * @example arn:aws:ecs:us-west-2:************:cluster/my-cluster\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn' as const;\n\n/**\n * The Amazon Resource Name (ARN) of an [ECS container instance](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ECS_instances.html).\n *\n * @example arn:aws:ecs:us-west-1:************:container/*************-4f0e-acae-1a75b14fe4d9\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn' as const;\n\n/**\n * The [launch type](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/launch_types.html) for an ECS task.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype' as const;\n\n/**\n * Enum value \"ec2\" for attribute {@link ATTR_AWS_ECS_LAUNCHTYPE}.\n */\nexport const AWS_ECS_LAUNCHTYPE_VALUE_EC2 = \"ec2\" as const;\n\n/**\n * Enum value \"fargate\" for attribute {@link ATTR_AWS_ECS_LAUNCHTYPE}.\n */\nexport const AWS_ECS_LAUNCHTYPE_VALUE_FARGATE = \"fargate\" as const;\n\n/**\n * The ARN of a running [ECS task](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-account-settings.html#ecs-resource-ids).\n *\n * @example arn:aws:ecs:us-west-1:************:task/10838bed-421f-43ef-870a-f43feacbbb5b\n * @example arn:aws:ecs:us-west-1:************:task/my-cluster/task-id/23ebb8ac-c18f-46c6-8bbe-d55d0e37cfbd\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn' as const;\n\n/**\n * The family name of the [ECS task definition](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definitions.html) used to create the ECS task.\n *\n * @example opentelemetry-family\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family' as const;\n\n/**\n * The ID of a running ECS task. The ID **MUST** be extracted from `task.arn`.\n *\n * @example 10838bed-421f-43ef-870a-f43feacbbb5b\n * @example 23ebb8ac-c18f-46c6-8bbe-d55d0e37cfbd\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_TASK_ID = 'aws.ecs.task.id' as const;\n\n/**\n * The revision for the task definition used to create the ECS task.\n *\n * @example 8\n * @example 26\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision' as const;\n\n/**\n * The ARN of an EKS cluster.\n *\n * @example arn:aws:ecs:us-west-2:************:cluster/my-cluster\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn' as const;\n\n/**\n * The AWS extended request ID as returned in the response header `x-amz-id-2`.\n *\n * @example wzHcyEWfmOGDIE5QOhTAqFDoDWP3y8IUvpNINCwL9N4TEHbUw0/gZJ+VZTmCNCWR7fezEN3eCiQ=\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_EXTENDED_REQUEST_ID = 'aws.extended_request_id' as const;\n\n/**\n * The name of the AWS Kinesis [stream](https://docs.aws.amazon.com/streams/latest/dev/introduction.html) the request refers to. Corresponds to the `--stream-name` parameter of the Kinesis [describe-stream](https://docs.aws.amazon.com/cli/latest/reference/kinesis/describe-stream.html) operation.\n *\n * @example some-stream-name\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_KINESIS_STREAM_NAME = 'aws.kinesis.stream_name' as const;\n\n/**\n * The full invoked ARN as provided on the `Context` passed to the function (`Lambda-Runtime-Invoked-Function-Arn` header on the `/runtime/invocation/next` applicable).\n *\n * @example arn:aws:lambda:us-east-1:123456:function:myfunction:myalias\n *\n * @note This may be different from `cloud.resource_id` if an alias is involved.\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LAMBDA_INVOKED_ARN = 'aws.lambda.invoked_arn' as const;\n\n/**\n * The UUID of the [AWS Lambda EvenSource Mapping](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-lambda-eventsourcemapping.html). An event source is mapped to a lambda function. It's contents are read by Lambda and used to trigger a function. This isn't available in the lambda execution context or the lambda runtime environtment. This is going to be populated by the AWS SDK for each language when that UUID is present. Some of these operations are Create/Delete/Get/List/Update EventSourceMapping.\n *\n * @example 587ad24b-03b9-4413-8202-bbd56b36e5b7\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LAMBDA_RESOURCE_MAPPING_ID = 'aws.lambda.resource_mapping.id' as const;\n\n/**\n * The Amazon Resource Name(s) (ARN) of the AWS log group(s).\n *\n * @example [\"arn:aws:logs:us-west-1:123456789012:log-group:/aws/my/group:*\"]\n *\n * @note See the [log group ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns' as const;\n\n/**\n * The name(s) of the AWS log group(s) an application is writing to.\n *\n * @example [\"/aws/lambda/my-function\", \"opentelemetry-service\"]\n *\n * @note Multiple log groups must be supported for cases like multi-container applications, where a single application has sidecar containers, and each write to their own log group.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_GROUP_NAMES = 'aws.log.group.names' as const;\n\n/**\n * The ARN(s) of the AWS log stream(s).\n *\n * @example [\"arn:aws:logs:us-west-1:123456789012:log-group:/aws/my/group:log-stream:logs/main/10838bed-421f-43ef-870a-f43feacbbb5b\"]\n *\n * @note See the [log stream ARN format documentation](https://docs.aws.amazon.com/AmazonCloudWatch/latest/logs/iam-access-control-overview-cwl.html#CWL_ARN_Format). One log group can contain several log streams, so these ARNs necessarily identify both a log group and a log stream.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns' as const;\n\n/**\n * The name(s) of the AWS log stream(s) an application is writing to.\n *\n * @example [\"logs/main/10838bed-421f-43ef-870a-f43feacbbb5b\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names' as const;\n\n/**\n * The AWS request ID as returned in the response headers `x-amzn-requestid`, `x-amzn-request-id` or `x-amz-request-id`.\n *\n * @example 79b9da39-b7ae-508a-a6bc-864b2829c622\n * @example C9ER4AJX75574TDJ\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_REQUEST_ID = 'aws.request_id' as const;\n\n/**\n * The S3 bucket name the request refers to. Corresponds to the `--bucket` parameter of the [S3 API](https://docs.aws.amazon.com/cli/latest/reference/s3api/index.html) operations.\n *\n * @example some-bucket-name\n *\n * @note The `bucket` attribute is applicable to all S3 operations that reference a bucket, i.e. that require the bucket name as a mandatory parameter.\n * This applies to almost all S3 operations except `list-buckets`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_S3_BUCKET = 'aws.s3.bucket' as const;\n\n/**\n * The source object (in the form `bucket`/`key`) for the copy operation.\n *\n * @example someFile.yml\n *\n * @note The `copy_source` attribute applies to S3 copy operations and corresponds to the `--copy-source` parameter\n * of the [copy-object operation within the S3 API](https://docs.aws.amazon.com/cli/latest/reference/s3api/copy-object.html).\n * This applies in particular to the following operations:\n *\n *   - [copy-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/copy-object.html)\n *   - [upload-part-copy](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part-copy.html)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_S3_COPY_SOURCE = 'aws.s3.copy_source' as const;\n\n/**\n * The delete request container that specifies the objects to be deleted.\n *\n * @example Objects=[{Key=string,VersionId=string},{Key=string,VersionId=string}],Quiet=boolean\n *\n * @note The `delete` attribute is only applicable to the [delete-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/delete-object.html) operation.\n * The `delete` attribute corresponds to the `--delete` parameter of the\n * [delete-objects operation within the S3 API](https://docs.aws.amazon.com/cli/latest/reference/s3api/delete-objects.html).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_S3_DELETE = 'aws.s3.delete' as const;\n\n/**\n * The S3 object key the request refers to. Corresponds to the `--key` parameter of the [S3 API](https://docs.aws.amazon.com/cli/latest/reference/s3api/index.html) operations.\n *\n * @example someFile.yml\n *\n * @note The `key` attribute is applicable to all object-related S3 operations, i.e. that require the object key as a mandatory parameter.\n * This applies in particular to the following operations:\n *\n *   - [copy-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/copy-object.html)\n *   - [delete-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/delete-object.html)\n *   - [get-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/get-object.html)\n *   - [head-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/head-object.html)\n *   - [put-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/put-object.html)\n *   - [restore-object](https://docs.aws.amazon.com/cli/latest/reference/s3api/restore-object.html)\n *   - [select-object-content](https://docs.aws.amazon.com/cli/latest/reference/s3api/select-object-content.html)\n *   - [abort-multipart-upload](https://docs.aws.amazon.com/cli/latest/reference/s3api/abort-multipart-upload.html)\n *   - [complete-multipart-upload](https://docs.aws.amazon.com/cli/latest/reference/s3api/complete-multipart-upload.html)\n *   - [create-multipart-upload](https://docs.aws.amazon.com/cli/latest/reference/s3api/create-multipart-upload.html)\n *   - [list-parts](https://docs.aws.amazon.com/cli/latest/reference/s3api/list-parts.html)\n *   - [upload-part](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part.html)\n *   - [upload-part-copy](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part-copy.html)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_S3_KEY = 'aws.s3.key' as const;\n\n/**\n * The part number of the part being uploaded in a multipart-upload operation. This is a positive integer between 1 and 10,000.\n *\n * @example 3456\n *\n * @note The `part_number` attribute is only applicable to the [upload-part](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part.html)\n * and [upload-part-copy](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part-copy.html) operations.\n * The `part_number` attribute corresponds to the `--part-number` parameter of the\n * [upload-part operation within the S3 API](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part.html).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_S3_PART_NUMBER = 'aws.s3.part_number' as const;\n\n/**\n * Upload ID that identifies the multipart upload.\n *\n * @example dfRtDYWFbkRONycy.Yxwh66Yjlx.cph0gtNBtJ\n *\n * @note The `upload_id` attribute applies to S3 multipart-upload operations and corresponds to the `--upload-id` parameter\n * of the [S3 API](https://docs.aws.amazon.com/cli/latest/reference/s3api/index.html) multipart operations.\n * This applies in particular to the following operations:\n *\n *   - [abort-multipart-upload](https://docs.aws.amazon.com/cli/latest/reference/s3api/abort-multipart-upload.html)\n *   - [complete-multipart-upload](https://docs.aws.amazon.com/cli/latest/reference/s3api/complete-multipart-upload.html)\n *   - [list-parts](https://docs.aws.amazon.com/cli/latest/reference/s3api/list-parts.html)\n *   - [upload-part](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part.html)\n *   - [upload-part-copy](https://docs.aws.amazon.com/cli/latest/reference/s3api/upload-part-copy.html)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_S3_UPLOAD_ID = 'aws.s3.upload_id' as const;\n\n/**\n * The ARN of the Secret stored in the Secrets Mangger\n *\n * @example arn:aws:secretsmanager:us-east-1:123456789012:secret:SecretName-6RandomCharacters\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_SECRETSMANAGER_SECRET_ARN = 'aws.secretsmanager.secret.arn' as const;\n\n/**\n * The ARN of the AWS SNS Topic. An Amazon SNS [topic](https://docs.aws.amazon.com/sns/latest/dg/sns-create-topic.html) is a logical access point that acts as a communication channel.\n *\n * @example arn:aws:sns:us-east-1:123456789012:mystack-mytopic-NZJ5JSMVGFIE\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_SNS_TOPIC_ARN = 'aws.sns.topic.arn' as const;\n\n/**\n * The URL of the AWS SQS Queue. It's a unique identifier for a queue in Amazon Simple Queue Service (SQS) and is used to access the queue and perform actions on it.\n *\n * @example https://sqs.us-east-1.amazonaws.com/123456789012/MyQueue\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_SQS_QUEUE_URL = 'aws.sqs.queue.url' as const;\n\n/**\n * The ARN of the AWS Step Functions Activity.\n *\n * @example arn:aws:states:us-east-1:123456789012:activity:get-greeting\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_STEP_FUNCTIONS_ACTIVITY_ARN = 'aws.step_functions.activity.arn' as const;\n\n/**\n * The ARN of the AWS Step Functions State Machine.\n *\n * @example arn:aws:states:us-east-1:123456789012:stateMachine:myStateMachine:1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AWS_STEP_FUNCTIONS_STATE_MACHINE_ARN = 'aws.step_functions.state_machine.arn' as const;\n\n/**\n * [Azure Resource Provider Namespace](https://learn.microsoft.com/azure/azure-resource-manager/management/azure-services-resource-providers) as recognized by the client.\n *\n * @example Microsoft.Storage\n * @example Microsoft.KeyVault\n * @example Microsoft.ServiceBus\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZ_NAMESPACE = 'az.namespace' as const;\n\n/**\n * The unique identifier of the service request. It's generated by the Azure service and returned with the response.\n *\n * @example 00000000-0000-0000-0000-000000000000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZ_SERVICE_REQUEST_ID = 'az.service_request_id' as const;\n\n/**\n * The unique identifier of the client instance.\n *\n * @example 3ba4827d-4422-483f-b59f-85b74211c11d\n * @example storage-client-1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZURE_CLIENT_ID = 'azure.client.id' as const;\n\n/**\n * Cosmos client connection mode.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZURE_COSMOSDB_CONNECTION_MODE = 'azure.cosmosdb.connection.mode' as const;\n\n/**\n * Enum value \"direct\" for attribute {@link ATTR_AZURE_COSMOSDB_CONNECTION_MODE}.\n */\nexport const AZURE_COSMOSDB_CONNECTION_MODE_VALUE_DIRECT = \"direct\" as const;\n\n/**\n * Enum value \"gateway\" for attribute {@link ATTR_AZURE_COSMOSDB_CONNECTION_MODE}.\n */\nexport const AZURE_COSMOSDB_CONNECTION_MODE_VALUE_GATEWAY = \"gateway\" as const;\n\n/**\n * Account or request [consistency level](https://learn.microsoft.com/azure/cosmos-db/consistency-levels).\n *\n * @example Eventual\n * @example ConsistentPrefix\n * @example BoundedStaleness\n * @example Strong\n * @example Session\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZURE_COSMOSDB_CONSISTENCY_LEVEL = 'azure.cosmosdb.consistency.level' as const;\n\n/**\n * Enum value \"BoundedStaleness\" for attribute {@link ATTR_AZURE_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const AZURE_COSMOSDB_CONSISTENCY_LEVEL_VALUE_BOUNDED_STALENESS = \"BoundedStaleness\" as const;\n\n/**\n * Enum value \"ConsistentPrefix\" for attribute {@link ATTR_AZURE_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const AZURE_COSMOSDB_CONSISTENCY_LEVEL_VALUE_CONSISTENT_PREFIX = \"ConsistentPrefix\" as const;\n\n/**\n * Enum value \"Eventual\" for attribute {@link ATTR_AZURE_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const AZURE_COSMOSDB_CONSISTENCY_LEVEL_VALUE_EVENTUAL = \"Eventual\" as const;\n\n/**\n * Enum value \"Session\" for attribute {@link ATTR_AZURE_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const AZURE_COSMOSDB_CONSISTENCY_LEVEL_VALUE_SESSION = \"Session\" as const;\n\n/**\n * Enum value \"Strong\" for attribute {@link ATTR_AZURE_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const AZURE_COSMOSDB_CONSISTENCY_LEVEL_VALUE_STRONG = \"Strong\" as const;\n\n/**\n * List of regions contacted during operation in the order that they were contacted. If there is more than one region listed, it indicates that the operation was performed on multiple regions i.e. cross-regional call.\n *\n * @example [\"North Central US\", \"Australia East\", \"Australia Southeast\"]\n *\n * @note Region name matches the format of `displayName` in [Azure Location API](https://learn.microsoft.com/rest/api/subscription/subscriptions/list-locations?view=rest-subscription-2021-10-01&tabs=HTTP#location)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZURE_COSMOSDB_OPERATION_CONTACTED_REGIONS = 'azure.cosmosdb.operation.contacted_regions' as const;\n\n/**\n * The number of request units consumed by the operation.\n *\n * @example 46.18\n * @example 1.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZURE_COSMOSDB_OPERATION_REQUEST_CHARGE = 'azure.cosmosdb.operation.request_charge' as const;\n\n/**\n * Request payload size in bytes.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZURE_COSMOSDB_REQUEST_BODY_SIZE = 'azure.cosmosdb.request.body.size' as const;\n\n/**\n * Cosmos DB sub status code.\n *\n * @example 1000\n * @example 1002\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_AZURE_COSMOSDB_RESPONSE_SUB_STATUS_CODE = 'azure.cosmosdb.response.sub_status_code' as const;\n\n/**\n * Array of brand name and version separated by a space\n *\n * @example [\" Not A;Brand 99\", \"Chromium 99\", \"Chrome 99\"]\n *\n * @note This value is intended to be taken from the [UA client hints API](https://wicg.github.io/ua-client-hints/#interface) (`navigator.userAgentData.brands`).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_BROWSER_BRANDS = 'browser.brands' as const;\n\n/**\n * Preferred language of the user using the browser\n *\n * @example en\n * @example en-US\n * @example fr\n * @example fr-FR\n *\n * @note This value is intended to be taken from the Navigator API `navigator.language`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_BROWSER_LANGUAGE = 'browser.language' as const;\n\n/**\n * A boolean that is true if the browser is running on a mobile device\n *\n * @note This value is intended to be taken from the [UA client hints API](https://wicg.github.io/ua-client-hints/#interface) (`navigator.userAgentData.mobile`). If unavailable, this attribute **SHOULD** be left unset.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_BROWSER_MOBILE = 'browser.mobile' as const;\n\n/**\n * The platform on which the browser is running\n *\n * @example Windows\n * @example macOS\n * @example Android\n *\n * @note This value is intended to be taken from the [UA client hints API](https://wicg.github.io/ua-client-hints/#interface) (`navigator.userAgentData.platform`). If unavailable, the legacy `navigator.platform` API **SHOULD NOT** be used instead and this attribute **SHOULD** be left unset in order for the values to be consistent.\n * The list of possible values is defined in the [W3C User-Agent Client Hints specification](https://wicg.github.io/ua-client-hints/#sec-ch-ua-platform). Note that some (but not all) of these values can overlap with values in the [`os.type` and `os.name` attributes](./os.md). However, for consistency, the values in the `browser.platform` attribute should capture the exact value that the user agent provides.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_BROWSER_PLATFORM = 'browser.platform' as const;\n\n/**\n * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CASSANDRA_CONSISTENCY_LEVEL = 'cassandra.consistency.level' as const;\n\n/**\n * Enum value \"all\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL = \"all\" as const;\n\n/**\n * Enum value \"any\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY = \"any\" as const;\n\n/**\n * Enum value \"each_quorum\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM = \"each_quorum\" as const;\n\n/**\n * Enum value \"local_one\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE = \"local_one\" as const;\n\n/**\n * Enum value \"local_quorum\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM = \"local_quorum\" as const;\n\n/**\n * Enum value \"local_serial\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL = \"local_serial\" as const;\n\n/**\n * Enum value \"one\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE = \"one\" as const;\n\n/**\n * Enum value \"quorum\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM = \"quorum\" as const;\n\n/**\n * Enum value \"serial\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL = \"serial\" as const;\n\n/**\n * Enum value \"three\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE = \"three\" as const;\n\n/**\n * Enum value \"two\" for attribute {@link ATTR_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO = \"two\" as const;\n\n/**\n * The data center of the coordinating node for a query.\n *\n * @example \"us-west-2\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CASSANDRA_COORDINATOR_DC = 'cassandra.coordinator.dc' as const;\n\n/**\n * The ID of the coordinating node for a query.\n *\n * @example \"be13faa2-8574-4d71-926d-27f16cf8a7af\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CASSANDRA_COORDINATOR_ID = 'cassandra.coordinator.id' as const;\n\n/**\n * The fetch size used for paging, i.e. how many rows will be returned at once.\n *\n * @example 5000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CASSANDRA_PAGE_SIZE = 'cassandra.page.size' as const;\n\n/**\n * Whether or not the query is idempotent.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CASSANDRA_QUERY_IDEMPOTENT = 'cassandra.query.idempotent' as const;\n\n/**\n * The number of times a query was speculatively executed. Not set or `0` if the query was not executed speculatively.\n *\n * @example 0\n * @example 2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CASSANDRA_SPECULATIVE_EXECUTION_COUNT = 'cassandra.speculative_execution.count' as const;\n\n/**\n * The kind of action a pipeline run is performing.\n *\n * @example BUILD\n * @example RUN\n * @example SYNC\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_ACTION_NAME = 'cicd.pipeline.action.name' as const;\n\n/**\n * Enum value \"BUILD\" for attribute {@link ATTR_CICD_PIPELINE_ACTION_NAME}.\n */\nexport const CICD_PIPELINE_ACTION_NAME_VALUE_BUILD = \"BUILD\" as const;\n\n/**\n * Enum value \"RUN\" for attribute {@link ATTR_CICD_PIPELINE_ACTION_NAME}.\n */\nexport const CICD_PIPELINE_ACTION_NAME_VALUE_RUN = \"RUN\" as const;\n\n/**\n * Enum value \"SYNC\" for attribute {@link ATTR_CICD_PIPELINE_ACTION_NAME}.\n */\nexport const CICD_PIPELINE_ACTION_NAME_VALUE_SYNC = \"SYNC\" as const;\n\n/**\n * The human readable name of the pipeline within a CI/CD system.\n *\n * @example Build and Test\n * @example Lint\n * @example Deploy Go Project\n * @example deploy_to_environment\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_NAME = 'cicd.pipeline.name' as const;\n\n/**\n * The result of a pipeline run.\n *\n * @example success\n * @example failure\n * @example timeout\n * @example skipped\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_RESULT = 'cicd.pipeline.result' as const;\n\n/**\n * Enum value \"cancellation\" for attribute {@link ATTR_CICD_PIPELINE_RESULT}.\n */\nexport const CICD_PIPELINE_RESULT_VALUE_CANCELLATION = \"cancellation\" as const;\n\n/**\n * Enum value \"error\" for attribute {@link ATTR_CICD_PIPELINE_RESULT}.\n */\nexport const CICD_PIPELINE_RESULT_VALUE_ERROR = \"error\" as const;\n\n/**\n * Enum value \"failure\" for attribute {@link ATTR_CICD_PIPELINE_RESULT}.\n */\nexport const CICD_PIPELINE_RESULT_VALUE_FAILURE = \"failure\" as const;\n\n/**\n * Enum value \"skip\" for attribute {@link ATTR_CICD_PIPELINE_RESULT}.\n */\nexport const CICD_PIPELINE_RESULT_VALUE_SKIP = \"skip\" as const;\n\n/**\n * Enum value \"success\" for attribute {@link ATTR_CICD_PIPELINE_RESULT}.\n */\nexport const CICD_PIPELINE_RESULT_VALUE_SUCCESS = \"success\" as const;\n\n/**\n * Enum value \"timeout\" for attribute {@link ATTR_CICD_PIPELINE_RESULT}.\n */\nexport const CICD_PIPELINE_RESULT_VALUE_TIMEOUT = \"timeout\" as const;\n\n/**\n * The unique identifier of a pipeline run within a CI/CD system.\n *\n * @example 120912\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_RUN_ID = 'cicd.pipeline.run.id' as const;\n\n/**\n * The pipeline run goes through these states during its lifecycle.\n *\n * @example pending\n * @example executing\n * @example finalizing\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_RUN_STATE = 'cicd.pipeline.run.state' as const;\n\n/**\n * Enum value \"executing\" for attribute {@link ATTR_CICD_PIPELINE_RUN_STATE}.\n */\nexport const CICD_PIPELINE_RUN_STATE_VALUE_EXECUTING = \"executing\" as const;\n\n/**\n * Enum value \"finalizing\" for attribute {@link ATTR_CICD_PIPELINE_RUN_STATE}.\n */\nexport const CICD_PIPELINE_RUN_STATE_VALUE_FINALIZING = \"finalizing\" as const;\n\n/**\n * Enum value \"pending\" for attribute {@link ATTR_CICD_PIPELINE_RUN_STATE}.\n */\nexport const CICD_PIPELINE_RUN_STATE_VALUE_PENDING = \"pending\" as const;\n\n/**\n * The [URL](https://wikipedia.org/wiki/URL) of the pipeline run, providing the complete address in order to locate and identify the pipeline run.\n *\n * @example https://github.com/open-telemetry/semantic-conventions/actions/runs/9753949763?pr=1075\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_RUN_URL_FULL = 'cicd.pipeline.run.url.full' as const;\n\n/**\n * The human readable name of a task within a pipeline. Task here most closely aligns with a [computing process](https://wikipedia.org/wiki/Pipeline_(computing)) in a pipeline. Other terms for tasks include commands, steps, and procedures.\n *\n * @example Run GoLang Linter\n * @example Go Build\n * @example go-test\n * @example deploy_binary\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_TASK_NAME = 'cicd.pipeline.task.name' as const;\n\n/**\n * The unique identifier of a task run within a pipeline.\n *\n * @example 12097\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_TASK_RUN_ID = 'cicd.pipeline.task.run.id' as const;\n\n/**\n * The result of a task run.\n *\n * @example success\n * @example failure\n * @example timeout\n * @example skipped\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_TASK_RUN_RESULT = 'cicd.pipeline.task.run.result' as const;\n\n/**\n * Enum value \"cancellation\" for attribute {@link ATTR_CICD_PIPELINE_TASK_RUN_RESULT}.\n */\nexport const CICD_PIPELINE_TASK_RUN_RESULT_VALUE_CANCELLATION = \"cancellation\" as const;\n\n/**\n * Enum value \"error\" for attribute {@link ATTR_CICD_PIPELINE_TASK_RUN_RESULT}.\n */\nexport const CICD_PIPELINE_TASK_RUN_RESULT_VALUE_ERROR = \"error\" as const;\n\n/**\n * Enum value \"failure\" for attribute {@link ATTR_CICD_PIPELINE_TASK_RUN_RESULT}.\n */\nexport const CICD_PIPELINE_TASK_RUN_RESULT_VALUE_FAILURE = \"failure\" as const;\n\n/**\n * Enum value \"skip\" for attribute {@link ATTR_CICD_PIPELINE_TASK_RUN_RESULT}.\n */\nexport const CICD_PIPELINE_TASK_RUN_RESULT_VALUE_SKIP = \"skip\" as const;\n\n/**\n * Enum value \"success\" for attribute {@link ATTR_CICD_PIPELINE_TASK_RUN_RESULT}.\n */\nexport const CICD_PIPELINE_TASK_RUN_RESULT_VALUE_SUCCESS = \"success\" as const;\n\n/**\n * Enum value \"timeout\" for attribute {@link ATTR_CICD_PIPELINE_TASK_RUN_RESULT}.\n */\nexport const CICD_PIPELINE_TASK_RUN_RESULT_VALUE_TIMEOUT = \"timeout\" as const;\n\n/**\n * The [URL](https://wikipedia.org/wiki/URL) of the pipeline task run, providing the complete address in order to locate and identify the pipeline task run.\n *\n * @example https://github.com/open-telemetry/semantic-conventions/actions/runs/9753949763/job/26920038674?pr=1075\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_TASK_RUN_URL_FULL = 'cicd.pipeline.task.run.url.full' as const;\n\n/**\n * The type of the task within a pipeline.\n *\n * @example build\n * @example test\n * @example deploy\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_PIPELINE_TASK_TYPE = 'cicd.pipeline.task.type' as const;\n\n/**\n * Enum value \"build\" for attribute {@link ATTR_CICD_PIPELINE_TASK_TYPE}.\n */\nexport const CICD_PIPELINE_TASK_TYPE_VALUE_BUILD = \"build\" as const;\n\n/**\n * Enum value \"deploy\" for attribute {@link ATTR_CICD_PIPELINE_TASK_TYPE}.\n */\nexport const CICD_PIPELINE_TASK_TYPE_VALUE_DEPLOY = \"deploy\" as const;\n\n/**\n * Enum value \"test\" for attribute {@link ATTR_CICD_PIPELINE_TASK_TYPE}.\n */\nexport const CICD_PIPELINE_TASK_TYPE_VALUE_TEST = \"test\" as const;\n\n/**\n * The name of a component of the CICD system.\n *\n * @example controller\n * @example scheduler\n * @example agent\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_SYSTEM_COMPONENT = 'cicd.system.component' as const;\n\n/**\n * The unique identifier of a worker within a CICD system.\n *\n * @example abc123\n * @example ********\n * @example controller\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_WORKER_ID = 'cicd.worker.id' as const;\n\n/**\n * The name of a worker within a CICD system.\n *\n * @example agent-abc\n * @example controller\n * @example Ubuntu LTS\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_WORKER_NAME = 'cicd.worker.name' as const;\n\n/**\n * The state of a CICD worker / agent.\n *\n * @example idle\n * @example busy\n * @example down\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_WORKER_STATE = 'cicd.worker.state' as const;\n\n/**\n * Enum value \"available\" for attribute {@link ATTR_CICD_WORKER_STATE}.\n */\nexport const CICD_WORKER_STATE_VALUE_AVAILABLE = \"available\" as const;\n\n/**\n * Enum value \"busy\" for attribute {@link ATTR_CICD_WORKER_STATE}.\n */\nexport const CICD_WORKER_STATE_VALUE_BUSY = \"busy\" as const;\n\n/**\n * Enum value \"offline\" for attribute {@link ATTR_CICD_WORKER_STATE}.\n */\nexport const CICD_WORKER_STATE_VALUE_OFFLINE = \"offline\" as const;\n\n/**\n * The [URL](https://wikipedia.org/wiki/URL) of the worker, providing the complete address in order to locate and identify the worker.\n *\n * @example https://cicd.example.org/worker/abc123\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CICD_WORKER_URL_FULL = 'cicd.worker.url.full' as const;\n\n/**\n * The cloud account ID the resource is assigned to.\n *\n * @example ************\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_ACCOUNT_ID = 'cloud.account.id' as const;\n\n/**\n * Cloud regions often have multiple, isolated locations known as zones to increase availability. Availability zone represents the zone where the resource is running.\n *\n * @example us-east-1c\n *\n * @note Availability zones are called \"zones\" on Alibaba Cloud and Google Cloud.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone' as const;\n\n/**\n * The cloud platform in use.\n *\n * @note The prefix of the service **SHOULD** match the one specified in `cloud.provider`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_PLATFORM = 'cloud.platform' as const;\n\n/**\n * Enum value \"alibaba_cloud_ecs\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS = \"alibaba_cloud_ecs\" as const;\n\n/**\n * Enum value \"alibaba_cloud_fc\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC = \"alibaba_cloud_fc\" as const;\n\n/**\n * Enum value \"alibaba_cloud_openshift\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_OPENSHIFT = \"alibaba_cloud_openshift\" as const;\n\n/**\n * Enum value \"aws_app_runner\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_APP_RUNNER = \"aws_app_runner\" as const;\n\n/**\n * Enum value \"aws_ec2\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_EC2 = \"aws_ec2\" as const;\n\n/**\n * Enum value \"aws_ecs\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_ECS = \"aws_ecs\" as const;\n\n/**\n * Enum value \"aws_eks\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_EKS = \"aws_eks\" as const;\n\n/**\n * Enum value \"aws_elastic_beanstalk\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK = \"aws_elastic_beanstalk\" as const;\n\n/**\n * Enum value \"aws_lambda\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_LAMBDA = \"aws_lambda\" as const;\n\n/**\n * Enum value \"aws_openshift\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AWS_OPENSHIFT = \"aws_openshift\" as const;\n\n/**\n * Enum value \"azure_aks\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AZURE_AKS = \"azure_aks\" as const;\n\n/**\n * Enum value \"azure_app_service\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE = \"azure_app_service\" as const;\n\n/**\n * Enum value \"azure_container_apps\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_APPS = \"azure_container_apps\" as const;\n\n/**\n * Enum value \"azure_container_instances\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES = \"azure_container_instances\" as const;\n\n/**\n * Enum value \"azure_functions\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS = \"azure_functions\" as const;\n\n/**\n * Enum value \"azure_openshift\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AZURE_OPENSHIFT = \"azure_openshift\" as const;\n\n/**\n * Enum value \"azure_vm\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_AZURE_VM = \"azure_vm\" as const;\n\n/**\n * Enum value \"gcp_app_engine\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE = \"gcp_app_engine\" as const;\n\n/**\n * Enum value \"gcp_bare_metal_solution\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_GCP_BARE_METAL_SOLUTION = \"gcp_bare_metal_solution\" as const;\n\n/**\n * Enum value \"gcp_cloud_functions\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS = \"gcp_cloud_functions\" as const;\n\n/**\n * Enum value \"gcp_cloud_run\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN = \"gcp_cloud_run\" as const;\n\n/**\n * Enum value \"gcp_compute_engine\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE = \"gcp_compute_engine\" as const;\n\n/**\n * Enum value \"gcp_kubernetes_engine\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE = \"gcp_kubernetes_engine\" as const;\n\n/**\n * Enum value \"gcp_openshift\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_GCP_OPENSHIFT = \"gcp_openshift\" as const;\n\n/**\n * Enum value \"ibm_cloud_openshift\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_IBM_CLOUD_OPENSHIFT = \"ibm_cloud_openshift\" as const;\n\n/**\n * Enum value \"oracle_cloud_compute\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_ORACLE_CLOUD_COMPUTE = \"oracle_cloud_compute\" as const;\n\n/**\n * Enum value \"oracle_cloud_oke\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_ORACLE_CLOUD_OKE = \"oracle_cloud_oke\" as const;\n\n/**\n * Enum value \"tencent_cloud_cvm\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_CVM = \"tencent_cloud_cvm\" as const;\n\n/**\n * Enum value \"tencent_cloud_eks\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_EKS = \"tencent_cloud_eks\" as const;\n\n/**\n * Enum value \"tencent_cloud_scf\" for attribute {@link ATTR_CLOUD_PLATFORM}.\n */\nexport const CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_SCF = \"tencent_cloud_scf\" as const;\n\n/**\n * Name of the cloud provider.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_PROVIDER = 'cloud.provider' as const;\n\n/**\n * Enum value \"alibaba_cloud\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD = \"alibaba_cloud\" as const;\n\n/**\n * Enum value \"aws\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_AWS = \"aws\" as const;\n\n/**\n * Enum value \"azure\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_AZURE = \"azure\" as const;\n\n/**\n * Enum value \"gcp\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_GCP = \"gcp\" as const;\n\n/**\n * Enum value \"heroku\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_HEROKU = \"heroku\" as const;\n\n/**\n * Enum value \"ibm_cloud\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_IBM_CLOUD = \"ibm_cloud\" as const;\n\n/**\n * Enum value \"oracle_cloud\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_ORACLE_CLOUD = \"oracle_cloud\" as const;\n\n/**\n * Enum value \"tencent_cloud\" for attribute {@link ATTR_CLOUD_PROVIDER}.\n */\nexport const CLOUD_PROVIDER_VALUE_TENCENT_CLOUD = \"tencent_cloud\" as const;\n\n/**\n * The geographical region within a cloud provider. When associated with a resource, this attribute specifies the region where the resource operates. When calling services or APIs deployed on a cloud, this attribute identifies the region where the called destination is deployed.\n *\n * @example us-central1\n * @example us-east-1\n *\n * @note Refer to your provider's docs to see the available regions, for example [Alibaba Cloud regions](https://www.alibabacloud.com/help/doc-detail/40654.htm), [AWS regions](https://aws.amazon.com/about-aws/global-infrastructure/regions_az/), [Azure regions](https://azure.microsoft.com/global-infrastructure/geographies/), [Google Cloud regions](https://cloud.google.com/about/locations), or [Tencent Cloud regions](https://www.tencentcloud.com/document/product/213/6091).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_REGION = 'cloud.region' as const;\n\n/**\n * Cloud provider-specific native identifier of the monitored cloud resource (e.g. an [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html) on AWS, a [fully qualified resource ID](https://learn.microsoft.com/rest/api/resources/resources/get-by-id) on Azure, a [full resource name](https://google.aip.dev/122#full-resource-names) on GCP)\n *\n * @example arn:aws:lambda:REGION:ACCOUNT_ID:function:my-function\n * @example //run.googleapis.com/projects/PROJECT_ID/locations/LOCATION_ID/services/SERVICE_ID\n * @example /subscriptions/<SUBSCRIPTION_GUID>/resourceGroups/<RG>/providers/Microsoft.Web/sites/<FUNCAPP>/functions/<FUNC>\n *\n * @note On some cloud providers, it may not be possible to determine the full ID at startup,\n * so it may be necessary to set `cloud.resource_id` as a span attribute instead.\n *\n * The exact value to use for `cloud.resource_id` depends on the cloud provider.\n * The following well-known definitions **MUST** be used if you set this attribute and they apply:\n *\n *   - **AWS Lambda:** The function [ARN](https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html).\n *     Take care not to use the \"invoked ARN\" directly but replace any\n *     [alias suffix](https://docs.aws.amazon.com/lambda/latest/dg/configuration-aliases.html)\n *     with the resolved function version, as the same runtime instance may be invocable with\n *     multiple different aliases.\n *   - **GCP:** The [URI of the resource](https://cloud.google.com/iam/docs/full-resource-names)\n *   - **Azure:** The [Fully Qualified Resource ID](https://docs.microsoft.com/rest/api/resources/resources/get-by-id) of the invoked function,\n *     *not* the function app, having the form\n *     `/subscriptions/<SUBSCRIPTION_GUID>/resourceGroups/<RG>/providers/Microsoft.Web/sites/<FUNCAPP>/functions/<FUNC>`.\n *     This means that a span attribute **MUST** be used, as an Azure function app can host multiple functions that would usually share\n *     a TracerProvider.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUD_RESOURCE_ID = 'cloud.resource_id' as const;\n\n/**\n * The [event_id](https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/spec.md#id) uniquely identifies the event.\n *\n * @example 123e4567-e89b-12d3-a456-************\n * @example 0001\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDEVENTS_EVENT_ID = 'cloudevents.event_id' as const;\n\n/**\n * The [source](https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/spec.md#source-1) identifies the context in which an event happened.\n *\n * @example https://github.com/cloudevents\n * @example /cloudevents/spec/pull/123\n * @example my-service\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDEVENTS_EVENT_SOURCE = 'cloudevents.event_source' as const;\n\n/**\n * The [version of the CloudEvents specification](https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/spec.md#specversion) which the event uses.\n *\n * @example \"1.0\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDEVENTS_EVENT_SPEC_VERSION = 'cloudevents.event_spec_version' as const;\n\n/**\n * The [subject](https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/spec.md#subject) of the event in the context of the event producer (identified by source).\n *\n * @example \"mynewfile.jpg\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDEVENTS_EVENT_SUBJECT = 'cloudevents.event_subject' as const;\n\n/**\n * The [event_type](https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/spec.md#type) contains a value describing the type of event related to the originating occurrence.\n *\n * @example com.github.pull_request.opened\n * @example com.example.object.deleted.v2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDEVENTS_EVENT_TYPE = 'cloudevents.event_type' as const;\n\n/**\n * The guid of the application.\n *\n * @example 218fc5a9-a5f1-4b54-aa05-46717d0ab26d\n *\n * @note Application instrumentation should use the value from environment\n * variable `VCAP_APPLICATION.application_id`. This is the same value as\n * reported by `cf app <app-name> --guid`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_APP_ID = 'cloudfoundry.app.id' as const;\n\n/**\n * The index of the application instance. 0 when just one instance is active.\n *\n * @example 0\n * @example 1\n *\n * @note CloudFoundry defines the `instance_id` in the [Loggregator v2 envelope](https://github.com/cloudfoundry/loggregator-api#v2-envelope).\n * It is used for logs and metrics emitted by CloudFoundry. It is\n * supposed to contain the application instance index for applications\n * deployed on the runtime.\n *\n * Application instrumentation should use the value from environment\n * variable `CF_INSTANCE_INDEX`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_APP_INSTANCE_ID = 'cloudfoundry.app.instance.id' as const;\n\n/**\n * The name of the application.\n *\n * @example my-app-name\n *\n * @note Application instrumentation should use the value from environment\n * variable `VCAP_APPLICATION.application_name`. This is the same value\n * as reported by `cf apps`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_APP_NAME = 'cloudfoundry.app.name' as const;\n\n/**\n * The guid of the CloudFoundry org the application is running in.\n *\n * @example 218fc5a9-a5f1-4b54-aa05-46717d0ab26d\n *\n * @note Application instrumentation should use the value from environment\n * variable `VCAP_APPLICATION.org_id`. This is the same value as\n * reported by `cf org <org-name> --guid`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_ORG_ID = 'cloudfoundry.org.id' as const;\n\n/**\n * The name of the CloudFoundry organization the app is running in.\n *\n * @example my-org-name\n *\n * @note Application instrumentation should use the value from environment\n * variable `VCAP_APPLICATION.org_name`. This is the same value as\n * reported by `cf orgs`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_ORG_NAME = 'cloudfoundry.org.name' as const;\n\n/**\n * The UID identifying the process.\n *\n * @example 218fc5a9-a5f1-4b54-aa05-46717d0ab26d\n *\n * @note Application instrumentation should use the value from environment\n * variable `VCAP_APPLICATION.process_id`. It is supposed to be equal to\n * `VCAP_APPLICATION.app_id` for applications deployed to the runtime.\n * For system components, this could be the actual PID.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_PROCESS_ID = 'cloudfoundry.process.id' as const;\n\n/**\n * The type of process.\n *\n * @example web\n *\n * @note CloudFoundry applications can consist of multiple jobs. Usually the\n * main process will be of type `web`. There can be additional background\n * tasks or side-cars with different process types.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_PROCESS_TYPE = 'cloudfoundry.process.type' as const;\n\n/**\n * The guid of the CloudFoundry space the application is running in.\n *\n * @example 218fc5a9-a5f1-4b54-aa05-46717d0ab26d\n *\n * @note Application instrumentation should use the value from environment\n * variable `VCAP_APPLICATION.space_id`. This is the same value as\n * reported by `cf space <space-name> --guid`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_SPACE_ID = 'cloudfoundry.space.id' as const;\n\n/**\n * The name of the CloudFoundry space the application is running in.\n *\n * @example my-space-name\n *\n * @note Application instrumentation should use the value from environment\n * variable `VCAP_APPLICATION.space_name`. This is the same value as\n * reported by `cf spaces`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_SPACE_NAME = 'cloudfoundry.space.name' as const;\n\n/**\n * A guid or another name describing the event source.\n *\n * @example cf/gorouter\n *\n * @note CloudFoundry defines the `source_id` in the [Loggregator v2 envelope](https://github.com/cloudfoundry/loggregator-api#v2-envelope).\n * It is used for logs and metrics emitted by CloudFoundry. It is\n * supposed to contain the component name, e.g. \"gorouter\", for\n * CloudFoundry components.\n *\n * When system components are instrumented, values from the\n * [Bosh spec](https://bosh.io/docs/jobs/#properties-spec)\n * should be used. The `system.id` should be set to\n * `spec.deployment/spec.name`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_SYSTEM_ID = 'cloudfoundry.system.id' as const;\n\n/**\n * A guid describing the concrete instance of the event source.\n *\n * @example 218fc5a9-a5f1-4b54-aa05-46717d0ab26d\n *\n * @note CloudFoundry defines the `instance_id` in the [Loggregator v2 envelope](https://github.com/cloudfoundry/loggregator-api#v2-envelope).\n * It is used for logs and metrics emitted by CloudFoundry. It is\n * supposed to contain the vm id for CloudFoundry components.\n *\n * When system components are instrumented, values from the\n * [Bosh spec](https://bosh.io/docs/jobs/#properties-spec)\n * should be used. The `system.instance.id` should be set to `spec.id`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CLOUDFOUNDRY_SYSTEM_INSTANCE_ID = 'cloudfoundry.system.instance.id' as const;\n\n/**\n * Deprecated, use `code.column.number`\n *\n * @example 16\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `code.column.number`.\n */\nexport const ATTR_CODE_COLUMN = 'code.column' as const;\n\n/**\n * Deprecated, use `code.file.path` instead\n *\n * @example \"/usr/local/MyApplication/content_root/app/index.php\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `code.file.path`.\n */\nexport const ATTR_CODE_FILEPATH = 'code.filepath' as const;\n\n/**\n * Deprecated, use `code.function.name` instead\n *\n * @example \"serveRequest\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Value should be included in `code.function.name` which is expected to be a fully-qualified name.\n */\nexport const ATTR_CODE_FUNCTION = 'code.function' as const;\n\n/**\n * Deprecated, use `code.line.number` instead\n *\n * @example 42\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `code.line.number`.\n */\nexport const ATTR_CODE_LINENO = 'code.lineno' as const;\n\n/**\n * Deprecated, namespace is now included into `code.function.name`\n *\n * @example \"com.example.MyHttpService\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Value should be included in `code.function.name` which is expected to be a fully-qualified name.\n */\nexport const ATTR_CODE_NAMESPACE = 'code.namespace' as const;\n\n/**\n * The command used to run the container (i.e. the command name).\n *\n * @example otelcontribcol\n *\n * @note If using embedded credentials or sensitive data, it is recommended to remove them to prevent potential leakage.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_COMMAND = 'container.command' as const;\n\n/**\n * All the command arguments (including the command/executable itself) run by the container.\n *\n * @example [\"otelcontribcol\", \"--config\", \"config.yaml\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_COMMAND_ARGS = 'container.command_args' as const;\n\n/**\n * The full command run by the container as a single string representing the full command.\n *\n * @example otelcontribcol --config config.yaml\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_COMMAND_LINE = 'container.command_line' as const;\n\n/**\n * Deprecated, use `cpu.mode` instead.\n *\n * @example user\n * @example kernel\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cpu.mode`.\n */\nexport const ATTR_CONTAINER_CPU_STATE = 'container.cpu.state' as const;\n\n/**\n * Enum value \"kernel\" for attribute {@link ATTR_CONTAINER_CPU_STATE}.\n */\nexport const CONTAINER_CPU_STATE_VALUE_KERNEL = \"kernel\" as const;\n\n/**\n * Enum value \"system\" for attribute {@link ATTR_CONTAINER_CPU_STATE}.\n */\nexport const CONTAINER_CPU_STATE_VALUE_SYSTEM = \"system\" as const;\n\n/**\n * Enum value \"user\" for attribute {@link ATTR_CONTAINER_CPU_STATE}.\n */\nexport const CONTAINER_CPU_STATE_VALUE_USER = \"user\" as const;\n\n/**\n * The name of the CSI ([Container Storage Interface](https://github.com/container-storage-interface/spec)) plugin used by the volume.\n *\n * @example pd.csi.storage.gke.io\n *\n * @note This can sometimes be referred to as a \"driver\" in CSI implementations. This should represent the `name` field of the GetPluginInfo RPC.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_CSI_PLUGIN_NAME = 'container.csi.plugin.name' as const;\n\n/**\n * The unique volume ID returned by the CSI ([Container Storage Interface](https://github.com/container-storage-interface/spec)) plugin.\n *\n * @example projects/my-gcp-project/zones/my-gcp-zone/disks/my-gcp-disk\n *\n * @note This can sometimes be referred to as a \"volume handle\" in CSI implementations. This should represent the `Volume.volume_id` field in CSI spec.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_CSI_VOLUME_ID = 'container.csi.volume.id' as const;\n\n/**\n * Container ID. Usually a UUID, as for example used to [identify Docker containers](https://docs.docker.com/engine/containers/run/#container-identification). The UUID might be abbreviated.\n *\n * @example a3bf90e006b2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_ID = 'container.id' as const;\n\n/**\n * Runtime specific image identifier. Usually a hash algorithm followed by a UUID.\n *\n * @example sha256:19c92d0a00d1b66d897bceaa7319bee0dd38a10a851c60bcec9474aa3f01e50f\n *\n * @note Docker defines a sha256 of the image id; `container.image.id` corresponds to the `Image` field from the Docker container inspect [API](https://docs.docker.com/engine/api/v1.43/#tag/Container/operation/ContainerInspect) endpoint.\n * K8s defines a link to the container registry repository with digest `\"imageID\": \"registry.azurecr.io /namespace/service/dockerfile@sha256:bdeabd40c3a8a492eaf9e8e44d0ebbb84bac7ee25ac0cf8a7159d25f62555625\"`.\n * The ID is assigned by the container runtime and can vary in different environments. Consider using `oci.manifest.digest` if it is important to identify the same image in different environments/runtimes.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_IMAGE_ID = 'container.image.id' as const;\n\n/**\n * Name of the image the container was built on.\n *\n * @example gcr.io/opentelemetry/operator\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_IMAGE_NAME = 'container.image.name' as const;\n\n/**\n * Repo digests of the container image as provided by the container runtime.\n *\n * @example [\"example@sha256:afcc7f1ac1b49db317a7196c902e61c6c3c4607d63599ee1a82d702d249a0ccb\", \"internal.registry.example.com:5000/example@sha256:b69959407d21e8a062e0416bf13405bb2b71ed7a84dde4158ebafacfa06f5578\"]\n *\n * @note [Docker](https://docs.docker.com/engine/api/v1.43/#tag/Image/operation/ImageInspect) and [CRI](https://github.com/kubernetes/cri-api/blob/c75ef5b473bbe2d0a4fc92f82235efd665ea8e9f/pkg/apis/runtime/v1/api.proto#L1237-L1238) report those under the `RepoDigests` field.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_IMAGE_REPO_DIGESTS = 'container.image.repo_digests' as const;\n\n/**\n * Container image tags. An example can be found in [Docker Image Inspect](https://docs.docker.com/engine/api/v1.43/#tag/Image/operation/ImageInspect). Should be only the `<tag>` section of the full name for example from `registry.example.com/my-org/my-image:<tag>`.\n *\n * @example [\"v1.27.1\", \"3.5.7-0\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_IMAGE_TAGS = 'container.image.tags' as const;\n\n/**\n * Container labels, `<key>` being the label name, the value being the label value.\n *\n * @example nginx\n *\n * @note For example, a docker container label `app` with value `nginx` **SHOULD** be recorded as the `container.label.app` attribute with value `\"nginx\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_LABEL = (key: string) => `container.label.${key}`;\n\n/**\n * Deprecated, use `container.label` instead.\n *\n * @example nginx\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `container.label`.\n */\nexport const ATTR_CONTAINER_LABELS = (key: string) => `container.labels.${key}`;\n\n/**\n * Container name used by container runtime.\n *\n * @example opentelemetry-autoconf\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_NAME = 'container.name' as const;\n\n/**\n * The container runtime managing this container.\n *\n * @example docker\n * @example containerd\n * @example rkt\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CONTAINER_RUNTIME = 'container.runtime' as const;\n\n/**\n * The logical CPU number [0..n-1]\n *\n * @example 1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CPU_LOGICAL_NUMBER = 'cpu.logical_number' as const;\n\n/**\n * The mode of the CPU\n *\n * @example user\n * @example system\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CPU_MODE = 'cpu.mode' as const;\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_IDLE = \"idle\" as const;\n\n/**\n * Enum value \"interrupt\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_INTERRUPT = \"interrupt\" as const;\n\n/**\n * Enum value \"iowait\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_IOWAIT = \"iowait\" as const;\n\n/**\n * Enum value \"kernel\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_KERNEL = \"kernel\" as const;\n\n/**\n * Enum value \"nice\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_NICE = \"nice\" as const;\n\n/**\n * Enum value \"steal\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_STEAL = \"steal\" as const;\n\n/**\n * Enum value \"system\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_SYSTEM = \"system\" as const;\n\n/**\n * Enum value \"user\" for attribute {@link ATTR_CPU_MODE}.\n */\nexport const CPU_MODE_VALUE_USER = \"user\" as const;\n\n/**\n * Value of the garbage collector collection generation.\n *\n * @example 0\n * @example 1\n * @example 2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_CPYTHON_GC_GENERATION = 'cpython.gc.generation' as const;\n\n/**\n * Enum value 0 for attribute {@link ATTR_CPYTHON_GC_GENERATION}.\n */\nexport const CPYTHON_GC_GENERATION_VALUE_GENERATION_0 = 0 as const;\n\n/**\n * Enum value 1 for attribute {@link ATTR_CPYTHON_GC_GENERATION}.\n */\nexport const CPYTHON_GC_GENERATION_VALUE_GENERATION_1 = 1 as const;\n\n/**\n * Enum value 2 for attribute {@link ATTR_CPYTHON_GC_GENERATION}.\n */\nexport const CPYTHON_GC_GENERATION_VALUE_GENERATION_2 = 2 as const;\n\n/**\n * Deprecated, use `cassandra.consistency.level` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cassandra.consistency.level`.\n */\nexport const ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL = 'db.cassandra.consistency_level' as const;\n\n/**\n * Enum value \"all\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL = \"all\" as const;\n\n/**\n * Enum value \"any\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY = \"any\" as const;\n\n/**\n * Enum value \"each_quorum\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM = \"each_quorum\" as const;\n\n/**\n * Enum value \"local_one\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE = \"local_one\" as const;\n\n/**\n * Enum value \"local_quorum\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM = \"local_quorum\" as const;\n\n/**\n * Enum value \"local_serial\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL = \"local_serial\" as const;\n\n/**\n * Enum value \"one\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE = \"one\" as const;\n\n/**\n * Enum value \"quorum\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM = \"quorum\" as const;\n\n/**\n * Enum value \"serial\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL = \"serial\" as const;\n\n/**\n * Enum value \"three\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE = \"three\" as const;\n\n/**\n * Enum value \"two\" for attribute {@link ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL}.\n */\nexport const DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO = \"two\" as const;\n\n/**\n * Deprecated, use `cassandra.coordinator.dc` instead.\n *\n * @example \"us-west-2\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cassandra.coordinator.dc`.\n */\nexport const ATTR_DB_CASSANDRA_COORDINATOR_DC = 'db.cassandra.coordinator.dc' as const;\n\n/**\n * Deprecated, use `cassandra.coordinator.id` instead.\n *\n * @example \"be13faa2-8574-4d71-926d-27f16cf8a7af\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cassandra.coordinator.id`.\n */\nexport const ATTR_DB_CASSANDRA_COORDINATOR_ID = 'db.cassandra.coordinator.id' as const;\n\n/**\n * Deprecated, use `cassandra.query.idempotent` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cassandra.query.idempotent`.\n */\nexport const ATTR_DB_CASSANDRA_IDEMPOTENCE = 'db.cassandra.idempotence' as const;\n\n/**\n * Deprecated, use `cassandra.page.size` instead.\n *\n * @example 5000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cassandra.page.size`.\n */\nexport const ATTR_DB_CASSANDRA_PAGE_SIZE = 'db.cassandra.page_size' as const;\n\n/**\n * Deprecated, use `cassandra.speculative_execution.count` instead.\n *\n * @example 0\n * @example 2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cassandra.speculative_execution.count`.\n */\nexport const ATTR_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT = 'db.cassandra.speculative_execution_count' as const;\n\n/**\n * Deprecated, use `db.collection.name` instead.\n *\n * @example \"mytable\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.collection.name`.\n */\nexport const ATTR_DB_CASSANDRA_TABLE = 'db.cassandra.table' as const;\n\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name' as const;\n\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state' as const;\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexport const DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = \"idle\" as const;\n\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexport const DB_CLIENT_CONNECTION_STATE_VALUE_USED = \"used\" as const;\n\n/**\n * Deprecated, use `db.client.connection.pool.name` instead.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.pool.name`.\n */\nexport const ATTR_DB_CLIENT_CONNECTIONS_POOL_NAME = 'db.client.connections.pool.name' as const;\n\n/**\n * Deprecated, use `db.client.connection.state` instead.\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.state`.\n */\nexport const ATTR_DB_CLIENT_CONNECTIONS_STATE = 'db.client.connections.state' as const;\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTIONS_STATE}.\n */\nexport const DB_CLIENT_CONNECTIONS_STATE_VALUE_IDLE = \"idle\" as const;\n\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTIONS_STATE}.\n */\nexport const DB_CLIENT_CONNECTIONS_STATE_VALUE_USED = \"used\" as const;\n\n/**\n * Deprecated, use `server.address`, `server.port` attributes instead.\n *\n * @example \"Server=(localdb)\\\\v11.0;Integrated Security=true;\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address` and `server.port`.\n */\nexport const ATTR_DB_CONNECTION_STRING = 'db.connection_string' as const;\n\n/**\n * Deprecated, use `azure.client.id` instead.\n *\n * @example \"3ba4827d-4422-483f-b59f-85b74211c11d\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.client.id`.\n */\nexport const ATTR_DB_COSMOSDB_CLIENT_ID = 'db.cosmosdb.client_id' as const;\n\n/**\n * Deprecated, use `azure.cosmosdb.connection.mode` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.connection.mode`.\n */\nexport const ATTR_DB_COSMOSDB_CONNECTION_MODE = 'db.cosmosdb.connection_mode' as const;\n\n/**\n * Enum value \"direct\" for attribute {@link ATTR_DB_COSMOSDB_CONNECTION_MODE}.\n */\nexport const DB_COSMOSDB_CONNECTION_MODE_VALUE_DIRECT = \"direct\" as const;\n\n/**\n * Enum value \"gateway\" for attribute {@link ATTR_DB_COSMOSDB_CONNECTION_MODE}.\n */\nexport const DB_COSMOSDB_CONNECTION_MODE_VALUE_GATEWAY = \"gateway\" as const;\n\n/**\n * Deprecated, use `cosmosdb.consistency.level` instead.\n *\n * @example Eventual\n * @example ConsistentPrefix\n * @example BoundedStaleness\n * @example Strong\n * @example Session\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.consistency.level`.\n */\nexport const ATTR_DB_COSMOSDB_CONSISTENCY_LEVEL = 'db.cosmosdb.consistency_level' as const;\n\n/**\n * Enum value \"BoundedStaleness\" for attribute {@link ATTR_DB_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const DB_COSMOSDB_CONSISTENCY_LEVEL_VALUE_BOUNDED_STALENESS = \"BoundedStaleness\" as const;\n\n/**\n * Enum value \"ConsistentPrefix\" for attribute {@link ATTR_DB_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const DB_COSMOSDB_CONSISTENCY_LEVEL_VALUE_CONSISTENT_PREFIX = \"ConsistentPrefix\" as const;\n\n/**\n * Enum value \"Eventual\" for attribute {@link ATTR_DB_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const DB_COSMOSDB_CONSISTENCY_LEVEL_VALUE_EVENTUAL = \"Eventual\" as const;\n\n/**\n * Enum value \"Session\" for attribute {@link ATTR_DB_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const DB_COSMOSDB_CONSISTENCY_LEVEL_VALUE_SESSION = \"Session\" as const;\n\n/**\n * Enum value \"Strong\" for attribute {@link ATTR_DB_COSMOSDB_CONSISTENCY_LEVEL}.\n */\nexport const DB_COSMOSDB_CONSISTENCY_LEVEL_VALUE_STRONG = \"Strong\" as const;\n\n/**\n * Deprecated, use `db.collection.name` instead.\n *\n * @example \"mytable\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.collection.name`.\n */\nexport const ATTR_DB_COSMOSDB_CONTAINER = 'db.cosmosdb.container' as const;\n\n/**\n * Deprecated, no replacement at this time.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no replacement at this time.\n */\nexport const ATTR_DB_COSMOSDB_OPERATION_TYPE = 'db.cosmosdb.operation_type' as const;\n\n/**\n * Enum value \"batch\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_BATCH = \"batch\" as const;\n\n/**\n * Enum value \"create\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_CREATE = \"create\" as const;\n\n/**\n * Enum value \"delete\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_DELETE = \"delete\" as const;\n\n/**\n * Enum value \"execute\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE = \"execute\" as const;\n\n/**\n * Enum value \"execute_javascript\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE_JAVASCRIPT = \"execute_javascript\" as const;\n\n/**\n * Enum value \"head\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD = \"head\" as const;\n\n/**\n * Enum value \"head_feed\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD_FEED = \"head_feed\" as const;\n\n/**\n * Enum value \"invalid\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_INVALID = \"invalid\" as const;\n\n/**\n * Enum value \"patch\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_PATCH = \"patch\" as const;\n\n/**\n * Enum value \"query\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY = \"query\" as const;\n\n/**\n * Enum value \"query_plan\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY_PLAN = \"query_plan\" as const;\n\n/**\n * Enum value \"read\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_READ = \"read\" as const;\n\n/**\n * Enum value \"read_feed\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_READ_FEED = \"read_feed\" as const;\n\n/**\n * Enum value \"replace\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_REPLACE = \"replace\" as const;\n\n/**\n * Enum value \"upsert\" for attribute {@link ATTR_DB_COSMOSDB_OPERATION_TYPE}.\n */\nexport const DB_COSMOSDB_OPERATION_TYPE_VALUE_UPSERT = \"upsert\" as const;\n\n/**\n * Deprecated, use `azure.cosmosdb.operation.contacted_regions` instead.\n *\n * @example [\"North Central US\", \"Australia East\", \"Australia Southeast\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.operation.contacted_regions`.\n */\nexport const ATTR_DB_COSMOSDB_REGIONS_CONTACTED = 'db.cosmosdb.regions_contacted' as const;\n\n/**\n * Deprecated, use `azure.cosmosdb.operation.request_charge` instead.\n *\n * @example 46.18\n * @example 1.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.operation.request_charge`.\n */\nexport const ATTR_DB_COSMOSDB_REQUEST_CHARGE = 'db.cosmosdb.request_charge' as const;\n\n/**\n * Deprecated, use `azure.cosmosdb.request.body.size` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.request.body.size`.\n */\nexport const ATTR_DB_COSMOSDB_REQUEST_CONTENT_LENGTH = 'db.cosmosdb.request_content_length' as const;\n\n/**\n * Deprecated, use `db.response.status_code` instead.\n *\n * @example 200\n * @example 201\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.response.status_code`.\n */\nexport const ATTR_DB_COSMOSDB_STATUS_CODE = 'db.cosmosdb.status_code' as const;\n\n/**\n * Deprecated, use `azure.cosmosdb.response.sub_status_code` instead.\n *\n * @example 1000\n * @example 1002\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `azure.cosmosdb.response.sub_status_code`.\n */\nexport const ATTR_DB_COSMOSDB_SUB_STATUS_CODE = 'db.cosmosdb.sub_status_code' as const;\n\n/**\n * Deprecated, use `db.namespace` instead.\n *\n * @example e9106fc68e3044f0b1475b04bf4ffd5f\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.namespace`.\n */\nexport const ATTR_DB_ELASTICSEARCH_CLUSTER_NAME = 'db.elasticsearch.cluster.name' as const;\n\n/**\n * Deprecated, use `elasticsearch.node.name` instead.\n *\n * @example instance-0000000001\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `elasticsearch.node.name`.\n */\nexport const ATTR_DB_ELASTICSEARCH_NODE_NAME = 'db.elasticsearch.node.name' as const;\n\n/**\n * Deprecated, use `db.operation.parameter` instead.\n *\n * @example test-index\n * @example 123\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.operation.parameter`.\n */\nexport const ATTR_DB_ELASTICSEARCH_PATH_PARTS = (key: string) => `db.elasticsearch.path_parts.${key}`;\n\n/**\n * Deprecated, no general replacement at this time. For Elasticsearch, use `db.elasticsearch.node.name` instead.\n *\n * @example \"mysql-e26b99z.example.com\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no general replacement at this time. For Elasticsearch, use `db.elasticsearch.node.name` instead.\n */\nexport const ATTR_DB_INSTANCE_ID = 'db.instance.id' as const;\n\n/**\n * Removed, no replacement at this time.\n *\n * @example org.postgresql.Driver\n * @example com.microsoft.sqlserver.jdbc.SQLServerDriver\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no replacement at this time.\n */\nexport const ATTR_DB_JDBC_DRIVER_CLASSNAME = 'db.jdbc.driver_classname' as const;\n\n/**\n * Deprecated, use `db.collection.name` instead.\n *\n * @example \"mytable\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.collection.name`.\n */\nexport const ATTR_DB_MONGODB_COLLECTION = 'db.mongodb.collection' as const;\n\n/**\n * Deprecated, SQL Server instance is now populated as a part of `db.namespace` attribute.\n *\n * @example \"MSSQLSERVER\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no replacement at this time.\n */\nexport const ATTR_DB_MSSQL_INSTANCE_NAME = 'db.mssql.instance_name' as const;\n\n/**\n * Deprecated, use `db.namespace` instead.\n *\n * @example customers\n * @example main\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.namespace`.\n */\nexport const ATTR_DB_NAME = 'db.name' as const;\n\n/**\n * Deprecated, use `db.operation.name` instead.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.operation.name`.\n */\nexport const ATTR_DB_OPERATION = 'db.operation' as const;\n\n/**\n * A database operation parameter, with `<key>` being the parameter name, and the attribute value being a string representation of the parameter value.\n *\n * @example someval\n * @example 55\n *\n * @note For example, a client-side maximum number of rows to read from the database\n * **MAY** be recorded as the `db.operation.parameter.max_rows` attribute.\n *\n * `db.query.text` parameters **SHOULD** be captured using `db.query.parameter.<key>`\n * instead of `db.operation.parameter.<key>`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_OPERATION_PARAMETER = (key: string) => `db.operation.parameter.${key}`;\n\n/**\n * A database query parameter, with `<key>` being the parameter name, and the attribute value being a string representation of the parameter value.\n *\n * @example someval\n * @example 55\n *\n * @note If a query parameter has no name and instead is referenced only by index,\n * then `<key>` **SHOULD** be the 0-based index.\n *\n * `db.query.parameter.<key>` **SHOULD** match\n * up with the parameterized placeholders present in `db.query.text`.\n *\n * `db.query.parameter.<key>` **SHOULD NOT** be captured on batch operations.\n *\n * Examples:\n *\n *   - For a query `SELECT * FROM users where username =  %s` with the parameter `\"jdoe\"`,\n *     the attribute `db.query.parameter.0` **SHOULD** be set to `\"jdoe\"`.\n *   - For a query `\"SELECT * FROM users WHERE username = %(username)s;` with parameter\n *     `username = \"jdoe\"`, the attribute `db.query.parameter.username` **SHOULD** be set to `\"jdoe\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_QUERY_PARAMETER = (key: string) => `db.query.parameter.${key}`;\n\n/**\n * Deprecated, use `db.namespace` instead.\n *\n * @example 0\n * @example 1\n * @example 15\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.namespace`.\n */\nexport const ATTR_DB_REDIS_DATABASE_INDEX = 'db.redis.database_index' as const;\n\n/**\n * Number of rows returned by the operation.\n *\n * @example 10\n * @example 30\n * @example 1000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_RESPONSE_RETURNED_ROWS = 'db.response.returned_rows' as const;\n\n/**\n * Deprecated, use `db.collection.name` instead.\n *\n * @example \"mytable\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.collection.name`, but only if not extracting the value from `db.query.text`.\n */\nexport const ATTR_DB_SQL_TABLE = 'db.sql.table' as const;\n\n/**\n * The database statement being executed.\n *\n * @example SELECT * FROM wuser_table\n * @example SET mykey \"WuValue\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.query.text`.\n */\nexport const ATTR_DB_STATEMENT = 'db.statement' as const;\n\n/**\n * Deprecated, use `db.system.name` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.system.name`.\n */\nexport const ATTR_DB_SYSTEM = 'db.system' as const;\n\n/**\n * Enum value \"adabas\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_ADABAS = \"adabas\" as const;\n\n/**\n * Enum value \"cache\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_CACHE = \"cache\" as const;\n\n/**\n * Enum value \"cassandra\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_CASSANDRA = \"cassandra\" as const;\n\n/**\n * Enum value \"clickhouse\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_CLICKHOUSE = \"clickhouse\" as const;\n\n/**\n * Enum value \"cloudscape\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_CLOUDSCAPE = \"cloudscape\" as const;\n\n/**\n * Enum value \"cockroachdb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_COCKROACHDB = \"cockroachdb\" as const;\n\n/**\n * Enum value \"coldfusion\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_COLDFUSION = \"coldfusion\" as const;\n\n/**\n * Enum value \"cosmosdb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_COSMOSDB = \"cosmosdb\" as const;\n\n/**\n * Enum value \"couchbase\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_COUCHBASE = \"couchbase\" as const;\n\n/**\n * Enum value \"couchdb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_COUCHDB = \"couchdb\" as const;\n\n/**\n * Enum value \"db2\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_DB2 = \"db2\" as const;\n\n/**\n * Enum value \"derby\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_DERBY = \"derby\" as const;\n\n/**\n * Enum value \"dynamodb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_DYNAMODB = \"dynamodb\" as const;\n\n/**\n * Enum value \"edb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_EDB = \"edb\" as const;\n\n/**\n * Enum value \"elasticsearch\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_ELASTICSEARCH = \"elasticsearch\" as const;\n\n/**\n * Enum value \"filemaker\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_FILEMAKER = \"filemaker\" as const;\n\n/**\n * Enum value \"firebird\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_FIREBIRD = \"firebird\" as const;\n\n/**\n * Enum value \"firstsql\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_FIRSTSQL = \"firstsql\" as const;\n\n/**\n * Enum value \"geode\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_GEODE = \"geode\" as const;\n\n/**\n * Enum value \"h2\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_H2 = \"h2\" as const;\n\n/**\n * Enum value \"hanadb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_HANADB = \"hanadb\" as const;\n\n/**\n * Enum value \"hbase\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_HBASE = \"hbase\" as const;\n\n/**\n * Enum value \"hive\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_HIVE = \"hive\" as const;\n\n/**\n * Enum value \"hsqldb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_HSQLDB = \"hsqldb\" as const;\n\n/**\n * Enum value \"influxdb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_INFLUXDB = \"influxdb\" as const;\n\n/**\n * Enum value \"informix\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_INFORMIX = \"informix\" as const;\n\n/**\n * Enum value \"ingres\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_INGRES = \"ingres\" as const;\n\n/**\n * Enum value \"instantdb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_INSTANTDB = \"instantdb\" as const;\n\n/**\n * Enum value \"interbase\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_INTERBASE = \"interbase\" as const;\n\n/**\n * Enum value \"intersystems_cache\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_INTERSYSTEMS_CACHE = \"intersystems_cache\" as const;\n\n/**\n * Enum value \"mariadb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_MARIADB = \"mariadb\" as const;\n\n/**\n * Enum value \"maxdb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_MAXDB = \"maxdb\" as const;\n\n/**\n * Enum value \"memcached\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_MEMCACHED = \"memcached\" as const;\n\n/**\n * Enum value \"mongodb\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_MONGODB = \"mongodb\" as const;\n\n/**\n * Enum value \"mssql\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_MSSQL = \"mssql\" as const;\n\n/**\n * Enum value \"mssqlcompact\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_MSSQLCOMPACT = \"mssqlcompact\" as const;\n\n/**\n * Enum value \"mysql\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_MYSQL = \"mysql\" as const;\n\n/**\n * Enum value \"neo4j\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_NEO4J = \"neo4j\" as const;\n\n/**\n * Enum value \"netezza\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_NETEZZA = \"netezza\" as const;\n\n/**\n * Enum value \"opensearch\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_OPENSEARCH = \"opensearch\" as const;\n\n/**\n * Enum value \"oracle\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_ORACLE = \"oracle\" as const;\n\n/**\n * Enum value \"other_sql\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_OTHER_SQL = \"other_sql\" as const;\n\n/**\n * Enum value \"pervasive\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_PERVASIVE = \"pervasive\" as const;\n\n/**\n * Enum value \"pointbase\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_POINTBASE = \"pointbase\" as const;\n\n/**\n * Enum value \"postgresql\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_POSTGRESQL = \"postgresql\" as const;\n\n/**\n * Enum value \"progress\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_PROGRESS = \"progress\" as const;\n\n/**\n * Enum value \"redis\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_REDIS = \"redis\" as const;\n\n/**\n * Enum value \"redshift\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_REDSHIFT = \"redshift\" as const;\n\n/**\n * Enum value \"spanner\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_SPANNER = \"spanner\" as const;\n\n/**\n * Enum value \"sqlite\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_SQLITE = \"sqlite\" as const;\n\n/**\n * Enum value \"sybase\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_SYBASE = \"sybase\" as const;\n\n/**\n * Enum value \"teradata\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_TERADATA = \"teradata\" as const;\n\n/**\n * Enum value \"trino\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_TRINO = \"trino\" as const;\n\n/**\n * Enum value \"vertica\" for attribute {@link ATTR_DB_SYSTEM}.\n */\nexport const DB_SYSTEM_VALUE_VERTICA = \"vertica\" as const;\n\n/**\n * Enum value \"actian.ingres\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_ACTIAN_INGRES = \"actian.ingres\" as const;\n\n/**\n * Enum value \"aws.dynamodb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_AWS_DYNAMODB = \"aws.dynamodb\" as const;\n\n/**\n * Enum value \"aws.redshift\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_AWS_REDSHIFT = \"aws.redshift\" as const;\n\n/**\n * Enum value \"azure.cosmosdb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_AZURE_COSMOSDB = \"azure.cosmosdb\" as const;\n\n/**\n * Enum value \"cassandra\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_CASSANDRA = \"cassandra\" as const;\n\n/**\n * Enum value \"clickhouse\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_CLICKHOUSE = \"clickhouse\" as const;\n\n/**\n * Enum value \"cockroachdb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_COCKROACHDB = \"cockroachdb\" as const;\n\n/**\n * Enum value \"couchbase\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_COUCHBASE = \"couchbase\" as const;\n\n/**\n * Enum value \"couchdb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_COUCHDB = \"couchdb\" as const;\n\n/**\n * Enum value \"derby\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_DERBY = \"derby\" as const;\n\n/**\n * Enum value \"elasticsearch\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_ELASTICSEARCH = \"elasticsearch\" as const;\n\n/**\n * Enum value \"firebirdsql\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_FIREBIRDSQL = \"firebirdsql\" as const;\n\n/**\n * Enum value \"gcp.spanner\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_GCP_SPANNER = \"gcp.spanner\" as const;\n\n/**\n * Enum value \"geode\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_GEODE = \"geode\" as const;\n\n/**\n * Enum value \"h2database\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_H2DATABASE = \"h2database\" as const;\n\n/**\n * Enum value \"hbase\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_HBASE = \"hbase\" as const;\n\n/**\n * Enum value \"hive\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_HIVE = \"hive\" as const;\n\n/**\n * Enum value \"hsqldb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_HSQLDB = \"hsqldb\" as const;\n\n/**\n * Enum value \"ibm.db2\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_IBM_DB2 = \"ibm.db2\" as const;\n\n/**\n * Enum value \"ibm.informix\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_IBM_INFORMIX = \"ibm.informix\" as const;\n\n/**\n * Enum value \"ibm.netezza\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_IBM_NETEZZA = \"ibm.netezza\" as const;\n\n/**\n * Enum value \"influxdb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_INFLUXDB = \"influxdb\" as const;\n\n/**\n * Enum value \"instantdb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_INSTANTDB = \"instantdb\" as const;\n\n/**\n * Enum value \"intersystems.cache\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_INTERSYSTEMS_CACHE = \"intersystems.cache\" as const;\n\n/**\n * Enum value \"memcached\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_MEMCACHED = \"memcached\" as const;\n\n/**\n * Enum value \"mongodb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_MONGODB = \"mongodb\" as const;\n\n/**\n * Enum value \"neo4j\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_NEO4J = \"neo4j\" as const;\n\n/**\n * Enum value \"opensearch\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_OPENSEARCH = \"opensearch\" as const;\n\n/**\n * Enum value \"oracle.db\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_ORACLE_DB = \"oracle.db\" as const;\n\n/**\n * Enum value \"other_sql\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_OTHER_SQL = \"other_sql\" as const;\n\n/**\n * Enum value \"redis\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_REDIS = \"redis\" as const;\n\n/**\n * Enum value \"sap.hana\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_SAP_HANA = \"sap.hana\" as const;\n\n/**\n * Enum value \"sap.maxdb\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_SAP_MAXDB = \"sap.maxdb\" as const;\n\n/**\n * Enum value \"softwareag.adabas\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_SOFTWAREAG_ADABAS = \"softwareag.adabas\" as const;\n\n/**\n * Enum value \"sqlite\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_SQLITE = \"sqlite\" as const;\n\n/**\n * Enum value \"teradata\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_TERADATA = \"teradata\" as const;\n\n/**\n * Enum value \"trino\" for attribute {@link ATTR_DB_SYSTEM_NAME}.\n */\nexport const DB_SYSTEM_NAME_VALUE_TRINO = \"trino\" as const;\n\n/**\n * Deprecated, no replacement at this time.\n *\n * @example readonly_user\n * @example reporting_user\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no replacement at this time.\n */\nexport const ATTR_DB_USER = 'db.user' as const;\n\n/**\n * 'Deprecated, use `deployment.environment.name` instead.'\n *\n * @example staging\n * @example production\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `deployment.environment.name`.\n */\nexport const ATTR_DEPLOYMENT_ENVIRONMENT = 'deployment.environment' as const;\n\n/**\n * Name of the [deployment environment](https://wikipedia.org/wiki/Deployment_environment) (aka deployment tier).\n *\n * @example staging\n * @example production\n *\n * @note `deployment.environment.name` does not affect the uniqueness constraints defined through\n * the `service.namespace`, `service.name` and `service.instance.id` resource attributes.\n * This implies that resources carrying the following attribute combinations **MUST** be\n * considered to be identifying the same service:\n *\n *   - `service.name=frontend`, `deployment.environment.name=production`\n *   - `service.name=frontend`, `deployment.environment.name=staging`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEPLOYMENT_ENVIRONMENT_NAME = 'deployment.environment.name' as const;\n\n/**\n * The id of the deployment.\n *\n * @example 1208\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEPLOYMENT_ID = 'deployment.id' as const;\n\n/**\n * The name of the deployment.\n *\n * @example deploy my app\n * @example deploy-frontend\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEPLOYMENT_NAME = 'deployment.name' as const;\n\n/**\n * The status of the deployment.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEPLOYMENT_STATUS = 'deployment.status' as const;\n\n/**\n * Enum value \"failed\" for attribute {@link ATTR_DEPLOYMENT_STATUS}.\n */\nexport const DEPLOYMENT_STATUS_VALUE_FAILED = \"failed\" as const;\n\n/**\n * Enum value \"succeeded\" for attribute {@link ATTR_DEPLOYMENT_STATUS}.\n */\nexport const DEPLOYMENT_STATUS_VALUE_SUCCEEDED = \"succeeded\" as const;\n\n/**\n * Destination address - domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n *\n * @example destination.example.com\n * @example *********\n * @example /tmp/my.sock\n *\n * @note When observed from the source side, and when communicating through an intermediary, `destination.address` **SHOULD** represent the destination address behind any intermediaries, for example proxies, if it's available.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DESTINATION_ADDRESS = 'destination.address' as const;\n\n/**\n * Destination port number\n *\n * @example 3389\n * @example 2888\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DESTINATION_PORT = 'destination.port' as const;\n\n/**\n * A unique identifier representing the device\n *\n * @example 123456789012345\n * @example 01:23:45:67:89:AB\n *\n * @note Its value **SHOULD** be identical for all apps on a device and it **SHOULD NOT** change if an app is uninstalled and re-installed.\n * However, it might be resettable by the user for all apps on a device.\n * Hardware IDs (e.g. vendor-specific serial number, IMEI or MAC address) **MAY** be used as values.\n *\n * More information about Android identifier best practices can be found [here](https://developer.android.com/training/articles/user-data-ids).\n *\n * > [!WARNING]> This attribute may contain sensitive (PII) information. Caution should be taken when storing personal data or anything which can identify a user. GDPR and data protection laws may apply,\n * > ensure you do your own due diligence.> Due to these reasons, this identifier is not recommended for consumer applications and will likely result in rejection from both Google Play and App Store.\n * > However, it may be appropriate for specific enterprise scenarios, such as kiosk devices or enterprise-managed devices, with appropriate compliance clearance.\n * > Any instrumentation providing this identifier **> MUST**>  implement it as an opt-in feature.> See [`app.installation.id`](/docs/registry/attributes/app.md#app-installation-id)>  for a more privacy-preserving alternative.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEVICE_ID = 'device.id' as const;\n\n/**\n * The name of the device manufacturer\n *\n * @example Apple\n * @example Samsung\n *\n * @note The Android OS provides this field via [Build](https://developer.android.com/reference/android/os/Build#MANUFACTURER). iOS apps **SHOULD** hardcode the value `Apple`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEVICE_MANUFACTURER = 'device.manufacturer' as const;\n\n/**\n * The model identifier for the device\n *\n * @example iPhone3,4\n * @example SM-G920F\n *\n * @note It's recommended this value represents a machine-readable version of the model identifier rather than the market or consumer-friendly name of the device.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier' as const;\n\n/**\n * The marketing name for the device model\n *\n * @example iPhone 6s Plus\n * @example Samsung Galaxy S6\n *\n * @note It's recommended this value represents a human-readable version of the device model rather than a machine-readable alternative.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DEVICE_MODEL_NAME = 'device.model.name' as const;\n\n/**\n * The disk IO operation direction.\n *\n * @example read\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DISK_IO_DIRECTION = 'disk.io.direction' as const;\n\n/**\n * Enum value \"read\" for attribute {@link ATTR_DISK_IO_DIRECTION}.\n */\nexport const DISK_IO_DIRECTION_VALUE_READ = \"read\" as const;\n\n/**\n * Enum value \"write\" for attribute {@link ATTR_DISK_IO_DIRECTION}.\n */\nexport const DISK_IO_DIRECTION_VALUE_WRITE = \"write\" as const;\n\n/**\n * The name being queried.\n *\n * @example www.example.com\n * @example opentelemetry.io\n *\n * @note If the name field contains non-printable characters (below 32 or above 126), those characters should be represented as escaped base 10 integers (\\\\DDD). Back slashes and quotes should be escaped. Tabs, carriage returns, and line feeds should be converted to \\\\t, \\\\r, and \\\\n respectively.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DNS_QUESTION_NAME = 'dns.question.name' as const;\n\n/**\n * Represents the human-readable identifier of the node/instance to which a request was routed.\n *\n * @example instance-0000000001\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ELASTICSEARCH_NODE_NAME = 'elasticsearch.node.name' as const;\n\n/**\n * Unique identifier of an end user in the system. It maybe a username, email address, or other identifier.\n *\n * @example username\n *\n * @note Unique identifier of an end user in the system.\n *\n * > [!Warning]\n * > This field contains sensitive (PII) information.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ENDUSER_ID = 'enduser.id' as const;\n\n/**\n * Pseudonymous identifier of an end user. This identifier should be a random value that is not directly linked or associated with the end user's actual identity.\n *\n * @example QdH5CAWJgqVT4rOr0qtumf\n *\n * @note Pseudonymous identifier of an end user.\n *\n * > [!Warning]\n * > This field contains sensitive (linkable PII) information.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ENDUSER_PSEUDO_ID = 'enduser.pseudo.id' as const;\n\n/**\n * Deprecated, use `user.roles` instead.\n *\n * @example \"admin\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Use `user.roles` attribute instead.\n */\nexport const ATTR_ENDUSER_ROLE = 'enduser.role' as const;\n\n/**\n * Deprecated, no replacement at this time.\n *\n * @example \"read:message, write:files\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no replacement at this time.\n */\nexport const ATTR_ENDUSER_SCOPE = 'enduser.scope' as const;\n\n/**\n * A message providing more detail about an error in human-readable form.\n *\n * @example Unexpected input type: string\n * @example The user has exceeded their storage quota\n *\n * @note `error.message` should provide additional context and detail about an error.\n * It is NOT **RECOMMENDED** to duplicate the value of `error.type` in `error.message`.\n * It is also NOT **RECOMMENDED** to duplicate the value of `exception.message` in `error.message`.\n *\n * `error.message` is NOT **RECOMMENDED** for metrics or spans due to its unbounded cardinality and overlap with span status.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_ERROR_MESSAGE = 'error.message' as const;\n\n/**\n * Identifies the class / type of event.\n *\n * @example browser.mouse.click\n * @example device.app.lifecycle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by EventName top-level field on the LogRecord.\n */\nexport const ATTR_EVENT_NAME = 'event.name' as const;\n\n/**\n * A boolean that is true if the serverless function is executed for the first time (aka cold-start).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_COLDSTART = 'faas.coldstart' as const;\n\n/**\n * A string containing the schedule period as [Cron Expression](https://docs.oracle.com/cd/E12058_01/doc/doc.1014/e12030/cron_expressions.htm).\n *\n * @example \"0/5 * * * ? *\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_CRON = 'faas.cron' as const;\n\n/**\n * The name of the source on which the triggering operation was performed. For example, in Cloud Storage or S3 corresponds to the bucket name, and in Cosmos DB to the database name.\n *\n * @example myBucketName\n * @example myDbName\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_DOCUMENT_COLLECTION = 'faas.document.collection' as const;\n\n/**\n * The document name/table subjected to the operation. For example, in Cloud Storage or S3 is the name of the file, and in Cosmos DB the table name.\n *\n * @example myFile.txt\n * @example myTableName\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_DOCUMENT_NAME = 'faas.document.name' as const;\n\n/**\n * Describes the type of the operation that was performed on the data.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_DOCUMENT_OPERATION = 'faas.document.operation' as const;\n\n/**\n * Enum value \"delete\" for attribute {@link ATTR_FAAS_DOCUMENT_OPERATION}.\n */\nexport const FAAS_DOCUMENT_OPERATION_VALUE_DELETE = \"delete\" as const;\n\n/**\n * Enum value \"edit\" for attribute {@link ATTR_FAAS_DOCUMENT_OPERATION}.\n */\nexport const FAAS_DOCUMENT_OPERATION_VALUE_EDIT = \"edit\" as const;\n\n/**\n * Enum value \"insert\" for attribute {@link ATTR_FAAS_DOCUMENT_OPERATION}.\n */\nexport const FAAS_DOCUMENT_OPERATION_VALUE_INSERT = \"insert\" as const;\n\n/**\n * A string containing the time when the data was accessed in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n *\n * @example \"2020-01-23T13:47:06Z\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_DOCUMENT_TIME = 'faas.document.time' as const;\n\n/**\n * The execution environment ID as a string, that will be potentially reused for other invocations to the same function/function version.\n *\n * @example 2021/06/28/[$LATEST]2f399eb14537447da05ab2a2e39309de\n *\n * @note - **AWS Lambda:** Use the (full) log stream name.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_INSTANCE = 'faas.instance' as const;\n\n/**\n * The invocation ID of the current function invocation.\n *\n * @example \"af9d5aa4-a685-4c5f-a22b-444f80b3cc28\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_INVOCATION_ID = 'faas.invocation_id' as const;\n\n/**\n * The name of the invoked function.\n *\n * @example \"my-function\"\n *\n * @note **SHOULD** be equal to the `faas.name` resource attribute of the invoked function.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_INVOKED_NAME = 'faas.invoked_name' as const;\n\n/**\n * The cloud provider of the invoked function.\n *\n * @note **SHOULD** be equal to the `cloud.provider` resource attribute of the invoked function.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_INVOKED_PROVIDER = 'faas.invoked_provider' as const;\n\n/**\n * Enum value \"alibaba_cloud\" for attribute {@link ATTR_FAAS_INVOKED_PROVIDER}.\n */\nexport const FAAS_INVOKED_PROVIDER_VALUE_ALIBABA_CLOUD = \"alibaba_cloud\" as const;\n\n/**\n * Enum value \"aws\" for attribute {@link ATTR_FAAS_INVOKED_PROVIDER}.\n */\nexport const FAAS_INVOKED_PROVIDER_VALUE_AWS = \"aws\" as const;\n\n/**\n * Enum value \"azure\" for attribute {@link ATTR_FAAS_INVOKED_PROVIDER}.\n */\nexport const FAAS_INVOKED_PROVIDER_VALUE_AZURE = \"azure\" as const;\n\n/**\n * Enum value \"gcp\" for attribute {@link ATTR_FAAS_INVOKED_PROVIDER}.\n */\nexport const FAAS_INVOKED_PROVIDER_VALUE_GCP = \"gcp\" as const;\n\n/**\n * Enum value \"tencent_cloud\" for attribute {@link ATTR_FAAS_INVOKED_PROVIDER}.\n */\nexport const FAAS_INVOKED_PROVIDER_VALUE_TENCENT_CLOUD = \"tencent_cloud\" as const;\n\n/**\n * The cloud region of the invoked function.\n *\n * @example \"eu-central-1\"\n *\n * @note **SHOULD** be equal to the `cloud.region` resource attribute of the invoked function.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_INVOKED_REGION = 'faas.invoked_region' as const;\n\n/**\n * The amount of memory available to the serverless function converted to Bytes.\n *\n * @example 134217728\n *\n * @note It's recommended to set this attribute since e.g. too little memory can easily stop a Java AWS Lambda function from working correctly. On AWS Lambda, the environment variable `AWS_LAMBDA_FUNCTION_MEMORY_SIZE` provides this information (which must be multiplied by 1,048,576).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_MAX_MEMORY = 'faas.max_memory' as const;\n\n/**\n * The name of the single function that this runtime instance executes.\n *\n * @example my-function\n * @example myazurefunctionapp/some-function-name\n *\n * @note This is the name of the function as configured/deployed on the FaaS\n * platform and is usually different from the name of the callback\n * function (which may be stored in the\n * [`code.namespace`/`code.function.name`](/docs/general/attributes.md#source-code-attributes)\n * span attributes).\n *\n * For some cloud providers, the above definition is ambiguous. The following\n * definition of function name **MUST** be used for this attribute\n * (and consequently the span name) for the listed cloud providers/products:\n *\n *   - **Azure:**  The full name `<FUNCAPP>/<FUNC>`, i.e., function app name\n *     followed by a forward slash followed by the function name (this form\n *     can also be seen in the resource JSON for the function).\n *     This means that a span attribute **MUST** be used, as an Azure function\n *     app can host multiple functions that would usually share\n *     a TracerProvider (see also the `cloud.resource_id` attribute).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_NAME = 'faas.name' as const;\n\n/**\n * A string containing the function invocation time in the [ISO 8601](https://www.iso.org/iso-8601-date-and-time-format.html) format expressed in [UTC](https://www.w3.org/TR/NOTE-datetime).\n *\n * @example \"2020-01-23T13:47:06Z\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_TIME = 'faas.time' as const;\n\n/**\n * Type of the trigger which caused this function invocation.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_TRIGGER = 'faas.trigger' as const;\n\n/**\n * Enum value \"datasource\" for attribute {@link ATTR_FAAS_TRIGGER}.\n */\nexport const FAAS_TRIGGER_VALUE_DATASOURCE = \"datasource\" as const;\n\n/**\n * Enum value \"http\" for attribute {@link ATTR_FAAS_TRIGGER}.\n */\nexport const FAAS_TRIGGER_VALUE_HTTP = \"http\" as const;\n\n/**\n * Enum value \"other\" for attribute {@link ATTR_FAAS_TRIGGER}.\n */\nexport const FAAS_TRIGGER_VALUE_OTHER = \"other\" as const;\n\n/**\n * Enum value \"pubsub\" for attribute {@link ATTR_FAAS_TRIGGER}.\n */\nexport const FAAS_TRIGGER_VALUE_PUBSUB = \"pubsub\" as const;\n\n/**\n * Enum value \"timer\" for attribute {@link ATTR_FAAS_TRIGGER}.\n */\nexport const FAAS_TRIGGER_VALUE_TIMER = \"timer\" as const;\n\n/**\n * The immutable version of the function being executed.\n *\n * @example 26\n * @example pinkfroid-00002\n *\n * @note Depending on the cloud provider and platform, use:\n *\n *   - **AWS Lambda:** The [function version](https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html)\n *     (an integer represented as a decimal string).\n *   - **Google Cloud Run (Services):** The [revision](https://cloud.google.com/run/docs/managing/revisions)\n *     (i.e., the function name plus the revision suffix).\n *   - **Google Cloud Functions:** The value of the\n *     [`K_REVISION` environment variable](https://cloud.google.com/functions/docs/env-var#runtime_environment_variables_set_automatically).\n *   - **Azure Functions:** Not applicable. Do not set this attribute.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FAAS_VERSION = 'faas.version' as const;\n\n/**\n * The unique identifier for the flag evaluation context. For example, the targeting key.\n *\n * @example 5157782b-2203-4c80-a857-dbbd5e7761db\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_CONTEXT_ID = 'feature_flag.context.id' as const;\n\n/**\n * Deprecated, use `error.message` instead.\n *\n * @example Flag `header-color` expected type `string` but found type `number`\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `error.message`.\n */\nexport const ATTR_FEATURE_FLAG_EVALUATION_ERROR_MESSAGE = 'feature_flag.evaluation.error.message' as const;\n\n/**\n * Deprecated, use `feature_flag.result.reason` instead.\n *\n * @example static\n * @example targeting_match\n * @example error\n * @example default\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `feature_flag.result.reason`.\n */\nexport const ATTR_FEATURE_FLAG_EVALUATION_REASON = 'feature_flag.evaluation.reason' as const;\n\n/**\n * Enum value \"cached\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_CACHED = \"cached\" as const;\n\n/**\n * Enum value \"default\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_DEFAULT = \"default\" as const;\n\n/**\n * Enum value \"disabled\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_DISABLED = \"disabled\" as const;\n\n/**\n * Enum value \"error\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_ERROR = \"error\" as const;\n\n/**\n * Enum value \"split\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_SPLIT = \"split\" as const;\n\n/**\n * Enum value \"stale\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_STALE = \"stale\" as const;\n\n/**\n * Enum value \"static\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_STATIC = \"static\" as const;\n\n/**\n * Enum value \"targeting_match\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_TARGETING_MATCH = \"targeting_match\" as const;\n\n/**\n * Enum value \"unknown\" for attribute {@link ATTR_FEATURE_FLAG_EVALUATION_REASON}.\n */\nexport const FEATURE_FLAG_EVALUATION_REASON_VALUE_UNKNOWN = \"unknown\" as const;\n\n/**\n * The lookup key of the feature flag.\n *\n * @example logo-color\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_KEY = 'feature_flag.key' as const;\n\n/**\n * Identifies the feature flag provider.\n *\n * @example Flag Manager\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_PROVIDER_NAME = 'feature_flag.provider.name' as const;\n\n/**\n * The reason code which shows how a feature flag value was determined.\n *\n * @example static\n * @example targeting_match\n * @example error\n * @example default\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_RESULT_REASON = 'feature_flag.result.reason' as const;\n\n/**\n * Enum value \"cached\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_CACHED = \"cached\" as const;\n\n/**\n * Enum value \"default\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_DEFAULT = \"default\" as const;\n\n/**\n * Enum value \"disabled\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_DISABLED = \"disabled\" as const;\n\n/**\n * Enum value \"error\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_ERROR = \"error\" as const;\n\n/**\n * Enum value \"split\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_SPLIT = \"split\" as const;\n\n/**\n * Enum value \"stale\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_STALE = \"stale\" as const;\n\n/**\n * Enum value \"static\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_STATIC = \"static\" as const;\n\n/**\n * Enum value \"targeting_match\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_TARGETING_MATCH = \"targeting_match\" as const;\n\n/**\n * Enum value \"unknown\" for attribute {@link ATTR_FEATURE_FLAG_RESULT_REASON}.\n */\nexport const FEATURE_FLAG_RESULT_REASON_VALUE_UNKNOWN = \"unknown\" as const;\n\n/**\n * The evaluated value of the feature flag.\n *\n * @example #ff0000\n * @example true\n * @example 3\n *\n * @note With some feature flag providers, feature flag results can be quite large or contain private or sensitive details.\n * Because of this, `feature_flag.result.variant` is often the preferred attribute if it is available.\n *\n * It may be desirable to redact or otherwise limit the size and scope of `feature_flag.result.value` if possible.\n * Because the evaluated flag value is unstructured and may be any type, it is left to the instrumentation author to determine how best to achieve this.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_RESULT_VALUE = 'feature_flag.result.value' as const;\n\n/**\n * A semantic identifier for an evaluated flag value.\n *\n * @example red\n * @example true\n * @example on\n *\n * @note A semantic identifier, commonly referred to as a variant, provides a means\n * for referring to a value without including the value itself. This can\n * provide additional context for understanding the meaning behind a value.\n * For example, the variant `red` maybe be used for the value `#c05543`.\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_RESULT_VARIANT = 'feature_flag.result.variant' as const;\n\n/**\n * The identifier of the [flag set](https://openfeature.dev/specification/glossary/#flag-set) to which the feature flag belongs.\n *\n * @example proj-1\n * @example ab98sgs\n * @example service1/dev\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_SET_ID = 'feature_flag.set.id' as const;\n\n/**\n * Deprecated, use `feature_flag.result.variant` instead.\n *\n * @example red\n * @example true\n * @example on\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `feature_flag.result.variant`.\n */\nexport const ATTR_FEATURE_FLAG_VARIANT = 'feature_flag.variant' as const;\n\n/**\n * The version of the ruleset used during the evaluation. This may be any stable value which uniquely identifies the ruleset.\n *\n * @example 1\n * @example 01ABCDEF\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FEATURE_FLAG_VERSION = 'feature_flag.version' as const;\n\n/**\n * Time when the file was last accessed, in ISO 8601 format.\n *\n * @example 2021-01-01T12:00:00Z\n *\n * @note This attribute might not be supported by some file systems — NFS, FAT32, in embedded OS, etc.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_ACCESSED = 'file.accessed' as const;\n\n/**\n * Array of file attributes.\n *\n * @example [\"readonly\", \"hidden\"]\n *\n * @note Attributes names depend on the OS or file system. Here’s a non-exhaustive list of values expected for this attribute: `archive`, `compressed`, `directory`, `encrypted`, `execute`, `hidden`, `immutable`, `journaled`, `read`, `readonly`, `symbolic link`, `system`, `temporary`, `write`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_ATTRIBUTES = 'file.attributes' as const;\n\n/**\n * Time when the file attributes or metadata was last changed, in ISO 8601 format.\n *\n * @example 2021-01-01T12:00:00Z\n *\n * @note `file.changed` captures the time when any of the file's properties or attributes (including the content) are changed, while `file.modified` captures the timestamp when the file content is modified.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_CHANGED = 'file.changed' as const;\n\n/**\n * Time when the file was created, in ISO 8601 format.\n *\n * @example 2021-01-01T12:00:00Z\n *\n * @note This attribute might not be supported by some file systems — NFS, FAT32, in embedded OS, etc.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_CREATED = 'file.created' as const;\n\n/**\n * Directory where the file is located. It should include the drive letter, when appropriate.\n *\n * @example /home/<USER>\n * @example C:\\\\Program Files\\\\MyApp\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_DIRECTORY = 'file.directory' as const;\n\n/**\n * File extension, excluding the leading dot.\n *\n * @example png\n * @example gz\n *\n * @note When the file name has multiple extensions (example.tar.gz), only the last one should be captured (\"gz\", not \"tar.gz\").\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_EXTENSION = 'file.extension' as const;\n\n/**\n * Name of the fork. A fork is additional data associated with a filesystem object.\n *\n * @example Zone.Identifer\n *\n * @note On Linux, a resource fork is used to store additional data with a filesystem object. A file always has at least one fork for the data portion, and additional forks may exist.\n * On NTFS, this is analogous to an Alternate Data Stream (ADS), and the default data stream for a file is just called $DATA. Zone.Identifier is commonly used by Windows to track contents downloaded from the Internet. An ADS is typically of the form: C:\\\\path\\\\to\\\\filename.extension:some_fork_name, and some_fork_name is the value that should populate `fork_name`. `filename.extension` should populate `file.name`, and `extension` should populate `file.extension`. The full path, `file.path`, will include the fork name.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_FORK_NAME = 'file.fork_name' as const;\n\n/**\n * Primary Group ID (GID) of the file.\n *\n * @example 1000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_GROUP_ID = 'file.group.id' as const;\n\n/**\n * Primary group name of the file.\n *\n * @example users\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_GROUP_NAME = 'file.group.name' as const;\n\n/**\n * Inode representing the file in the filesystem.\n *\n * @example 256383\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_INODE = 'file.inode' as const;\n\n/**\n * Mode of the file in octal representation.\n *\n * @example 0640\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_MODE = 'file.mode' as const;\n\n/**\n * Time when the file content was last modified, in ISO 8601 format.\n *\n * @example 2021-01-01T12:00:00Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_MODIFIED = 'file.modified' as const;\n\n/**\n * Name of the file including the extension, without the directory.\n *\n * @example example.png\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_NAME = 'file.name' as const;\n\n/**\n * The user ID (UID) or security identifier (SID) of the file owner.\n *\n * @example 1000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_OWNER_ID = 'file.owner.id' as const;\n\n/**\n * Username of the file owner.\n *\n * @example root\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_OWNER_NAME = 'file.owner.name' as const;\n\n/**\n * Full path to the file, including the file name. It should include the drive letter, when appropriate.\n *\n * @example /home/<USER>/example.png\n * @example C:\\\\Program Files\\\\MyApp\\\\myapp.exe\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_PATH = 'file.path' as const;\n\n/**\n * File size in bytes.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_SIZE = 'file.size' as const;\n\n/**\n * Path to the target of a symbolic link.\n *\n * @example /usr/bin/python3\n *\n * @note This attribute is only applicable to symbolic links.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_FILE_SYMBOLIC_LINK_TARGET_PATH = 'file.symbolic_link.target_path' as const;\n\n/**\n * The container within GCP where the AppHub application is defined.\n *\n * @example projects/my-container-project\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_APPLICATION_CONTAINER = 'gcp.apphub.application.container' as const;\n\n/**\n * The name of the application as configured in AppHub.\n *\n * @example my-application\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_APPLICATION_ID = 'gcp.apphub.application.id' as const;\n\n/**\n * The GCP zone or region where the application is defined.\n *\n * @example us-central1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_APPLICATION_LOCATION = 'gcp.apphub.application.location' as const;\n\n/**\n * Criticality of a service indicates its importance to the business.\n *\n * @note [See AppHub type enum](https://cloud.google.com/app-hub/docs/reference/rest/v1/Attributes#type)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_SERVICE_CRITICALITY_TYPE = 'gcp.apphub.service.criticality_type' as const;\n\n/**\n * Enum value \"HIGH\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_CRITICALITY_TYPE_VALUE_HIGH = \"HIGH\" as const;\n\n/**\n * Enum value \"LOW\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_CRITICALITY_TYPE_VALUE_LOW = \"LOW\" as const;\n\n/**\n * Enum value \"MEDIUM\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_CRITICALITY_TYPE_VALUE_MEDIUM = \"MEDIUM\" as const;\n\n/**\n * Enum value \"MISSION_CRITICAL\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_CRITICALITY_TYPE_VALUE_MISSION_CRITICAL = \"MISSION_CRITICAL\" as const;\n\n/**\n * Environment of a service is the stage of a software lifecycle.\n *\n * @note [See AppHub environment type](https://cloud.google.com/app-hub/docs/reference/rest/v1/Attributes#type_1)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE = 'gcp.apphub.service.environment_type' as const;\n\n/**\n * Enum value \"DEVELOPMENT\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE_VALUE_DEVELOPMENT = \"DEVELOPMENT\" as const;\n\n/**\n * Enum value \"PRODUCTION\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE_VALUE_PRODUCTION = \"PRODUCTION\" as const;\n\n/**\n * Enum value \"STAGING\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE_VALUE_STAGING = \"STAGING\" as const;\n\n/**\n * Enum value \"TEST\" for attribute {@link ATTR_GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_SERVICE_ENVIRONMENT_TYPE_VALUE_TEST = \"TEST\" as const;\n\n/**\n * The name of the service as configured in AppHub.\n *\n * @example my-service\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_SERVICE_ID = 'gcp.apphub.service.id' as const;\n\n/**\n * Criticality of a workload indicates its importance to the business.\n *\n * @note [See AppHub type enum](https://cloud.google.com/app-hub/docs/reference/rest/v1/Attributes#type)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE = 'gcp.apphub.workload.criticality_type' as const;\n\n/**\n * Enum value \"HIGH\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE_VALUE_HIGH = \"HIGH\" as const;\n\n/**\n * Enum value \"LOW\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE_VALUE_LOW = \"LOW\" as const;\n\n/**\n * Enum value \"MEDIUM\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE_VALUE_MEDIUM = \"MEDIUM\" as const;\n\n/**\n * Enum value \"MISSION_CRITICAL\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_CRITICALITY_TYPE_VALUE_MISSION_CRITICAL = \"MISSION_CRITICAL\" as const;\n\n/**\n * Environment of a workload is the stage of a software lifecycle.\n *\n * @note [See AppHub environment type](https://cloud.google.com/app-hub/docs/reference/rest/v1/Attributes#type_1)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE = 'gcp.apphub.workload.environment_type' as const;\n\n/**\n * Enum value \"DEVELOPMENT\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE_VALUE_DEVELOPMENT = \"DEVELOPMENT\" as const;\n\n/**\n * Enum value \"PRODUCTION\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE_VALUE_PRODUCTION = \"PRODUCTION\" as const;\n\n/**\n * Enum value \"STAGING\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE_VALUE_STAGING = \"STAGING\" as const;\n\n/**\n * Enum value \"TEST\" for attribute {@link ATTR_GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE}.\n */\nexport const GCP_APPHUB_WORKLOAD_ENVIRONMENT_TYPE_VALUE_TEST = \"TEST\" as const;\n\n/**\n * The name of the workload as configured in AppHub.\n *\n * @example my-workload\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_APPHUB_WORKLOAD_ID = 'gcp.apphub.workload.id' as const;\n\n/**\n * Identifies the Google Cloud service for which the official client library is intended.\n *\n * @example appengine\n * @example run\n * @example firestore\n * @example alloydb\n * @example spanner\n *\n * @note Intended to be a stable identifier for Google Cloud client libraries that is uniform across implementation languages. The value should be derived from the canonical service domain for the service; for example, 'foo.googleapis.com' should result in a value of 'foo'.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_CLIENT_SERVICE = 'gcp.client.service' as const;\n\n/**\n * The name of the Cloud Run [execution](https://cloud.google.com/run/docs/managing/job-executions) being run for the Job, as set by the [`CLOUD_RUN_EXECUTION`](https://cloud.google.com/run/docs/container-contract#jobs-env-vars) environment variable.\n *\n * @example job-name-xxxx\n * @example sample-job-mdw84\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_CLOUD_RUN_JOB_EXECUTION = 'gcp.cloud_run.job.execution' as const;\n\n/**\n * The index for a task within an execution as provided by the [`CLOUD_RUN_TASK_INDEX`](https://cloud.google.com/run/docs/container-contract#jobs-env-vars) environment variable.\n *\n * @example 0\n * @example 1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_CLOUD_RUN_JOB_TASK_INDEX = 'gcp.cloud_run.job.task_index' as const;\n\n/**\n * The hostname of a GCE instance. This is the full value of the default or [custom hostname](https://cloud.google.com/compute/docs/instances/custom-hostname-vm).\n *\n * @example my-host1234.example.com\n * @example sample-vm.us-west1-b.c.my-project.internal\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_GCE_INSTANCE_HOSTNAME = 'gcp.gce.instance.hostname' as const;\n\n/**\n * The instance name of a GCE instance. This is the value provided by `host.name`, the visible name of the instance in the Cloud Console UI, and the prefix for the default hostname of the instance as defined by the [default internal DNS name](https://cloud.google.com/compute/docs/internal-dns#instance-fully-qualified-domain-names).\n *\n * @example instance-1\n * @example my-vm-name\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GCP_GCE_INSTANCE_NAME = 'gcp.gce.instance.name' as const;\n\n/**\n * Free-form description of the GenAI agent provided by the application.\n *\n * @example Helps with math problems\n * @example Generates fiction stories\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_AGENT_DESCRIPTION = 'gen_ai.agent.description' as const;\n\n/**\n * The unique identifier of the GenAI agent.\n *\n * @example asst_5j66UpCpwteGg4YSxUnt7lPY\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_AGENT_ID = 'gen_ai.agent.id' as const;\n\n/**\n * Human-readable name of the GenAI agent provided by the application.\n *\n * @example Math Tutor\n * @example Fiction Writer\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_AGENT_NAME = 'gen_ai.agent.name' as const;\n\n/**\n * Deprecated, use Event API to report completions contents.\n *\n * @example [{'role': 'assistant', 'content': 'The capital of France is Paris.'}]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no replacement at this time.\n */\nexport const ATTR_GEN_AI_COMPLETION = 'gen_ai.completion' as const;\n\n/**\n * The unique identifier for a conversation (session, thread), used to store and correlate messages within this conversation.\n *\n * @example conv_5j66UpCpwteGg4YSxUnt7lPY\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_CONVERSATION_ID = 'gen_ai.conversation.id' as const;\n\n/**\n * The data source identifier.\n *\n * @example H7STPQYOND\n *\n * @note Data sources are used by AI agents and RAG applications to store grounding data. A data source may be an external database, object store, document collection, website, or any other storage system used by the GenAI agent or application. The `gen_ai.data_source.id` **SHOULD** match the identifier used by the GenAI system rather than a name specific to the external storage, such as a database or object store. Semantic conventions referencing `gen_ai.data_source.id` **MAY** also leverage additional attributes, such as `db.*`, to further identify and describe the data source.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_DATA_SOURCE_ID = 'gen_ai.data_source.id' as const;\n\n/**\n * Deprecated, use `gen_ai.output.type`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `gen_ai.output.type`.\n */\nexport const ATTR_GEN_AI_OPENAI_REQUEST_RESPONSE_FORMAT = 'gen_ai.openai.request.response_format' as const;\n\n/**\n * Enum value \"json_object\" for attribute {@link ATTR_GEN_AI_OPENAI_REQUEST_RESPONSE_FORMAT}.\n */\nexport const GEN_AI_OPENAI_REQUEST_RESPONSE_FORMAT_VALUE_JSON_OBJECT = \"json_object\" as const;\n\n/**\n * Enum value \"json_schema\" for attribute {@link ATTR_GEN_AI_OPENAI_REQUEST_RESPONSE_FORMAT}.\n */\nexport const GEN_AI_OPENAI_REQUEST_RESPONSE_FORMAT_VALUE_JSON_SCHEMA = \"json_schema\" as const;\n\n/**\n * Enum value \"text\" for attribute {@link ATTR_GEN_AI_OPENAI_REQUEST_RESPONSE_FORMAT}.\n */\nexport const GEN_AI_OPENAI_REQUEST_RESPONSE_FORMAT_VALUE_TEXT = \"text\" as const;\n\n/**\n * Deprecated, use `gen_ai.request.seed`.\n *\n * @example 100\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `gen_ai.request.seed`.\n */\nexport const ATTR_GEN_AI_OPENAI_REQUEST_SEED = 'gen_ai.openai.request.seed' as const;\n\n/**\n * The service tier requested. May be a specific tier, default, or auto.\n *\n * @example auto\n * @example default\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_OPENAI_REQUEST_SERVICE_TIER = 'gen_ai.openai.request.service_tier' as const;\n\n/**\n * Enum value \"auto\" for attribute {@link ATTR_GEN_AI_OPENAI_REQUEST_SERVICE_TIER}.\n */\nexport const GEN_AI_OPENAI_REQUEST_SERVICE_TIER_VALUE_AUTO = \"auto\" as const;\n\n/**\n * Enum value \"default\" for attribute {@link ATTR_GEN_AI_OPENAI_REQUEST_SERVICE_TIER}.\n */\nexport const GEN_AI_OPENAI_REQUEST_SERVICE_TIER_VALUE_DEFAULT = \"default\" as const;\n\n/**\n * The service tier used for the response.\n *\n * @example scale\n * @example default\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_OPENAI_RESPONSE_SERVICE_TIER = 'gen_ai.openai.response.service_tier' as const;\n\n/**\n * A fingerprint to track any eventual change in the Generative AI environment.\n *\n * @example fp_44709d6fcb\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_OPENAI_RESPONSE_SYSTEM_FINGERPRINT = 'gen_ai.openai.response.system_fingerprint' as const;\n\n/**\n * The name of the operation being performed.\n *\n * @note If one of the predefined values applies, but specific system uses a different name it's **RECOMMENDED** to document it in the semantic conventions for specific GenAI system and use system-specific name in the instrumentation. If a different name is not documented, instrumentation libraries **SHOULD** use applicable predefined value.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_OPERATION_NAME = 'gen_ai.operation.name' as const;\n\n/**\n * Enum value \"chat\" for attribute {@link ATTR_GEN_AI_OPERATION_NAME}.\n */\nexport const GEN_AI_OPERATION_NAME_VALUE_CHAT = \"chat\" as const;\n\n/**\n * Enum value \"create_agent\" for attribute {@link ATTR_GEN_AI_OPERATION_NAME}.\n */\nexport const GEN_AI_OPERATION_NAME_VALUE_CREATE_AGENT = \"create_agent\" as const;\n\n/**\n * Enum value \"embeddings\" for attribute {@link ATTR_GEN_AI_OPERATION_NAME}.\n */\nexport const GEN_AI_OPERATION_NAME_VALUE_EMBEDDINGS = \"embeddings\" as const;\n\n/**\n * Enum value \"execute_tool\" for attribute {@link ATTR_GEN_AI_OPERATION_NAME}.\n */\nexport const GEN_AI_OPERATION_NAME_VALUE_EXECUTE_TOOL = \"execute_tool\" as const;\n\n/**\n * Enum value \"generate_content\" for attribute {@link ATTR_GEN_AI_OPERATION_NAME}.\n */\nexport const GEN_AI_OPERATION_NAME_VALUE_GENERATE_CONTENT = \"generate_content\" as const;\n\n/**\n * Enum value \"invoke_agent\" for attribute {@link ATTR_GEN_AI_OPERATION_NAME}.\n */\nexport const GEN_AI_OPERATION_NAME_VALUE_INVOKE_AGENT = \"invoke_agent\" as const;\n\n/**\n * Enum value \"text_completion\" for attribute {@link ATTR_GEN_AI_OPERATION_NAME}.\n */\nexport const GEN_AI_OPERATION_NAME_VALUE_TEXT_COMPLETION = \"text_completion\" as const;\n\n/**\n * Represents the content type requested by the client.\n *\n * @note This attribute **SHOULD** be used when the client requests output of a specific type. The model may return zero or more outputs of this type.\n * This attribute specifies the output modality and not the actual output format. For example, if an image is requested, the actual output could be a URL pointing to an image file.\n * Additional output format details may be recorded in the future in the `gen_ai.output.{type}.*` attributes.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_OUTPUT_TYPE = 'gen_ai.output.type' as const;\n\n/**\n * Enum value \"image\" for attribute {@link ATTR_GEN_AI_OUTPUT_TYPE}.\n */\nexport const GEN_AI_OUTPUT_TYPE_VALUE_IMAGE = \"image\" as const;\n\n/**\n * Enum value \"json\" for attribute {@link ATTR_GEN_AI_OUTPUT_TYPE}.\n */\nexport const GEN_AI_OUTPUT_TYPE_VALUE_JSON = \"json\" as const;\n\n/**\n * Enum value \"speech\" for attribute {@link ATTR_GEN_AI_OUTPUT_TYPE}.\n */\nexport const GEN_AI_OUTPUT_TYPE_VALUE_SPEECH = \"speech\" as const;\n\n/**\n * Enum value \"text\" for attribute {@link ATTR_GEN_AI_OUTPUT_TYPE}.\n */\nexport const GEN_AI_OUTPUT_TYPE_VALUE_TEXT = \"text\" as const;\n\n/**\n * Deprecated, use Event API to report prompt contents.\n *\n * @example [{'role': 'user', 'content': 'What is the capital of France?'}]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed, no replacement at this time.\n */\nexport const ATTR_GEN_AI_PROMPT = 'gen_ai.prompt' as const;\n\n/**\n * The target number of candidate completions to return.\n *\n * @example 3\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_CHOICE_COUNT = 'gen_ai.request.choice.count' as const;\n\n/**\n * The encoding formats requested in an embeddings operation, if specified.\n *\n * @example [\"base64\"]\n * @example [\"float\", \"binary\"]\n *\n * @note In some GenAI systems the encoding formats are called embedding types. Also, some GenAI systems only accept a single format per request.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_ENCODING_FORMATS = 'gen_ai.request.encoding_formats' as const;\n\n/**\n * The frequency penalty setting for the GenAI request.\n *\n * @example 0.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_FREQUENCY_PENALTY = 'gen_ai.request.frequency_penalty' as const;\n\n/**\n * The maximum number of tokens the model generates for a request.\n *\n * @example 100\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_MAX_TOKENS = 'gen_ai.request.max_tokens' as const;\n\n/**\n * The name of the GenAI model a request is being made to.\n *\n * @example \"gpt-4\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_MODEL = 'gen_ai.request.model' as const;\n\n/**\n * The presence penalty setting for the GenAI request.\n *\n * @example 0.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_PRESENCE_PENALTY = 'gen_ai.request.presence_penalty' as const;\n\n/**\n * Requests with same seed value more likely to return same result.\n *\n * @example 100\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_SEED = 'gen_ai.request.seed' as const;\n\n/**\n * List of sequences that the model will use to stop generating further tokens.\n *\n * @example [\"forest\", \"lived\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_STOP_SEQUENCES = 'gen_ai.request.stop_sequences' as const;\n\n/**\n * The temperature setting for the GenAI request.\n *\n * @example 0.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_TEMPERATURE = 'gen_ai.request.temperature' as const;\n\n/**\n * The top_k sampling setting for the GenAI request.\n *\n * @example 1.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_TOP_K = 'gen_ai.request.top_k' as const;\n\n/**\n * The top_p sampling setting for the GenAI request.\n *\n * @example 1.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_REQUEST_TOP_P = 'gen_ai.request.top_p' as const;\n\n/**\n * Array of reasons the model stopped generating tokens, corresponding to each generation received.\n *\n * @example [\"stop\"]\n * @example [\"stop\", \"length\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_RESPONSE_FINISH_REASONS = 'gen_ai.response.finish_reasons' as const;\n\n/**\n * The unique identifier for the completion.\n *\n * @example chatcmpl-123\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_RESPONSE_ID = 'gen_ai.response.id' as const;\n\n/**\n * The name of the model that generated the response.\n *\n * @example gpt-4-0613\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_RESPONSE_MODEL = 'gen_ai.response.model' as const;\n\n/**\n * The Generative AI product as identified by the client or server instrumentation.\n *\n * @example \"openai\"\n *\n * @note The `gen_ai.system` describes a family of GenAI models with specific model identified\n * by `gen_ai.request.model` and `gen_ai.response.model` attributes.\n *\n * The actual GenAI product may differ from the one identified by the client.\n * Multiple systems, including Azure OpenAI and Gemini, are accessible by OpenAI client\n * libraries. In such cases, the `gen_ai.system` is set to `openai` based on the\n * instrumentation's best knowledge, instead of the actual system. The `server.address`\n * attribute may help identify the actual system in use for `openai`.\n *\n * For custom model, a custom friendly name **SHOULD** be used.\n * If none of these options apply, the `gen_ai.system` **SHOULD** be set to `_OTHER`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_SYSTEM = 'gen_ai.system' as const;\n\n/**\n * Enum value \"anthropic\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_ANTHROPIC = \"anthropic\" as const;\n\n/**\n * Enum value \"aws.bedrock\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_AWS_BEDROCK = \"aws.bedrock\" as const;\n\n/**\n * Enum value \"az.ai.inference\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_AZ_AI_INFERENCE = \"az.ai.inference\" as const;\n\n/**\n * Enum value \"az.ai.openai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_AZ_AI_OPENAI = \"az.ai.openai\" as const;\n\n/**\n * Enum value \"cohere\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_COHERE = \"cohere\" as const;\n\n/**\n * Enum value \"deepseek\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_DEEPSEEK = \"deepseek\" as const;\n\n/**\n * Enum value \"gcp.gemini\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_GCP_GEMINI = \"gcp.gemini\" as const;\n\n/**\n * Enum value \"gcp.gen_ai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_GCP_GEN_AI = \"gcp.gen_ai\" as const;\n\n/**\n * Enum value \"gcp.vertex_ai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_GCP_VERTEX_AI = \"gcp.vertex_ai\" as const;\n\n/**\n * Enum value \"gemini\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_GEMINI = \"gemini\" as const;\n\n/**\n * Enum value \"groq\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_GROQ = \"groq\" as const;\n\n/**\n * Enum value \"ibm.watsonx.ai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_IBM_WATSONX_AI = \"ibm.watsonx.ai\" as const;\n\n/**\n * Enum value \"mistral_ai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_MISTRAL_AI = \"mistral_ai\" as const;\n\n/**\n * Enum value \"openai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_OPENAI = \"openai\" as const;\n\n/**\n * Enum value \"perplexity\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_PERPLEXITY = \"perplexity\" as const;\n\n/**\n * Enum value \"vertex_ai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_VERTEX_AI = \"vertex_ai\" as const;\n\n/**\n * Enum value \"xai\" for attribute {@link ATTR_GEN_AI_SYSTEM}.\n */\nexport const GEN_AI_SYSTEM_VALUE_XAI = \"xai\" as const;\n\n/**\n * The type of token being counted.\n *\n * @example input\n * @example output\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_TOKEN_TYPE = 'gen_ai.token.type' as const;\n\n/**\n * Enum value \"input\" for attribute {@link ATTR_GEN_AI_TOKEN_TYPE}.\n */\nexport const GEN_AI_TOKEN_TYPE_VALUE_INPUT = \"input\" as const;\n\n/**\n * Enum value \"output\" for attribute {@link ATTR_GEN_AI_TOKEN_TYPE}.\n */\nexport const GEN_AI_TOKEN_TYPE_VALUE_COMPLETION = \"output\" as const;\n\n/**\n * Enum value \"output\" for attribute {@link ATTR_GEN_AI_TOKEN_TYPE}.\n */\nexport const GEN_AI_TOKEN_TYPE_VALUE_OUTPUT = \"output\" as const;\n\n/**\n * The tool call identifier.\n *\n * @example call_mszuSIzqtI65i1wAUOE8w5H4\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_TOOL_CALL_ID = 'gen_ai.tool.call.id' as const;\n\n/**\n * The tool description.\n *\n * @example Multiply two numbers\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_TOOL_DESCRIPTION = 'gen_ai.tool.description' as const;\n\n/**\n * Name of the tool utilized by the agent.\n *\n * @example Flights\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_TOOL_NAME = 'gen_ai.tool.name' as const;\n\n/**\n * Type of the tool utilized by the agent\n *\n * @example function\n * @example extension\n * @example datastore\n *\n * @note Extension: A tool executed on the agent-side to directly call external APIs, bridging the gap between the agent and real-world systems.\n * Agent-side operations involve actions that are performed by the agent on the server or within the agent's controlled environment.\n * Function: A tool executed on the client-side, where the agent generates parameters for a predefined function, and the client executes the logic.\n * Client-side operations are actions taken on the user's end or within the client application.\n * Datastore: A tool used by the agent to access and query structured or unstructured external data for retrieval-augmented tasks or knowledge updates.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_TOOL_TYPE = 'gen_ai.tool.type' as const;\n\n/**\n * Deprecated, use `gen_ai.usage.output_tokens` instead.\n *\n * @example 42\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `gen_ai.usage.output_tokens`.\n */\nexport const ATTR_GEN_AI_USAGE_COMPLETION_TOKENS = 'gen_ai.usage.completion_tokens' as const;\n\n/**\n * The number of tokens used in the GenAI input (prompt).\n *\n * @example 100\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_USAGE_INPUT_TOKENS = 'gen_ai.usage.input_tokens' as const;\n\n/**\n * The number of tokens used in the GenAI response (completion).\n *\n * @example 180\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEN_AI_USAGE_OUTPUT_TOKENS = 'gen_ai.usage.output_tokens' as const;\n\n/**\n * Deprecated, use `gen_ai.usage.input_tokens` instead.\n *\n * @example 42\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `gen_ai.usage.input_tokens`.\n */\nexport const ATTR_GEN_AI_USAGE_PROMPT_TOKENS = 'gen_ai.usage.prompt_tokens' as const;\n\n/**\n * Two-letter code representing continent’s name.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEO_CONTINENT_CODE = 'geo.continent.code' as const;\n\n/**\n * Enum value \"AF\" for attribute {@link ATTR_GEO_CONTINENT_CODE}.\n */\nexport const GEO_CONTINENT_CODE_VALUE_AF = \"AF\" as const;\n\n/**\n * Enum value \"AN\" for attribute {@link ATTR_GEO_CONTINENT_CODE}.\n */\nexport const GEO_CONTINENT_CODE_VALUE_AN = \"AN\" as const;\n\n/**\n * Enum value \"AS\" for attribute {@link ATTR_GEO_CONTINENT_CODE}.\n */\nexport const GEO_CONTINENT_CODE_VALUE_AS = \"AS\" as const;\n\n/**\n * Enum value \"EU\" for attribute {@link ATTR_GEO_CONTINENT_CODE}.\n */\nexport const GEO_CONTINENT_CODE_VALUE_EU = \"EU\" as const;\n\n/**\n * Enum value \"NA\" for attribute {@link ATTR_GEO_CONTINENT_CODE}.\n */\nexport const GEO_CONTINENT_CODE_VALUE_NA = \"NA\" as const;\n\n/**\n * Enum value \"OC\" for attribute {@link ATTR_GEO_CONTINENT_CODE}.\n */\nexport const GEO_CONTINENT_CODE_VALUE_OC = \"OC\" as const;\n\n/**\n * Enum value \"SA\" for attribute {@link ATTR_GEO_CONTINENT_CODE}.\n */\nexport const GEO_CONTINENT_CODE_VALUE_SA = \"SA\" as const;\n\n/**\n * Two-letter ISO Country Code ([ISO 3166-1 alpha2](https://wikipedia.org/wiki/ISO_3166-1#Codes)).\n *\n * @example CA\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEO_COUNTRY_ISO_CODE = 'geo.country.iso_code' as const;\n\n/**\n * Locality name. Represents the name of a city, town, village, or similar populated place.\n *\n * @example Montreal\n * @example Berlin\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEO_LOCALITY_NAME = 'geo.locality.name' as const;\n\n/**\n * Latitude of the geo location in [WGS84](https://wikipedia.org/wiki/World_Geodetic_System#WGS84).\n *\n * @example 45.505918\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEO_LOCATION_LAT = 'geo.location.lat' as const;\n\n/**\n * Longitude of the geo location in [WGS84](https://wikipedia.org/wiki/World_Geodetic_System#WGS84).\n *\n * @example -73.61483\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEO_LOCATION_LON = 'geo.location.lon' as const;\n\n/**\n * Postal code associated with the location. Values appropriate for this field may also be known as a postcode or ZIP code and will vary widely from country to country.\n *\n * @example 94040\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEO_POSTAL_CODE = 'geo.postal_code' as const;\n\n/**\n * Region ISO code ([ISO 3166-2](https://wikipedia.org/wiki/ISO_3166-2)).\n *\n * @example CA-QC\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GEO_REGION_ISO_CODE = 'geo.region.iso_code' as const;\n\n/**\n * The type of memory.\n *\n * @example other\n * @example stack\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GO_MEMORY_TYPE = 'go.memory.type' as const;\n\n/**\n * Enum value \"other\" for attribute {@link ATTR_GO_MEMORY_TYPE}.\n */\nexport const GO_MEMORY_TYPE_VALUE_OTHER = \"other\" as const;\n\n/**\n * Enum value \"stack\" for attribute {@link ATTR_GO_MEMORY_TYPE}.\n */\nexport const GO_MEMORY_TYPE_VALUE_STACK = \"stack\" as const;\n\n/**\n * The GraphQL document being executed.\n *\n * @example \"query findBookById { bookById(id: ?) { name } }\"\n *\n * @note The value may be sanitized to exclude sensitive information.\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GRAPHQL_DOCUMENT = 'graphql.document' as const;\n\n/**\n * The name of the operation being executed.\n *\n * @example \"findBookById\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GRAPHQL_OPERATION_NAME = 'graphql.operation.name' as const;\n\n/**\n * The type of the operation being executed.\n *\n * @example query\n * @example mutation\n * @example subscription\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_GRAPHQL_OPERATION_TYPE = 'graphql.operation.type' as const;\n\n/**\n * Enum value \"mutation\" for attribute {@link ATTR_GRAPHQL_OPERATION_TYPE}.\n */\nexport const GRAPHQL_OPERATION_TYPE_VALUE_MUTATION = \"mutation\" as const;\n\n/**\n * Enum value \"query\" for attribute {@link ATTR_GRAPHQL_OPERATION_TYPE}.\n */\nexport const GRAPHQL_OPERATION_TYPE_VALUE_QUERY = \"query\" as const;\n\n/**\n * Enum value \"subscription\" for attribute {@link ATTR_GRAPHQL_OPERATION_TYPE}.\n */\nexport const GRAPHQL_OPERATION_TYPE_VALUE_SUBSCRIPTION = \"subscription\" as const;\n\n/**\n * Unique identifier for the application\n *\n * @example 2daa2797-e42b-4624-9322-ec3f968df4da\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HEROKU_APP_ID = 'heroku.app.id' as const;\n\n/**\n * Commit hash for the current release\n *\n * @example e6134959463efd8966b20e75b913cafe3f5ec\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HEROKU_RELEASE_COMMIT = 'heroku.release.commit' as const;\n\n/**\n * Time and date the release was created\n *\n * @example 2022-10-23T18:00:42Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HEROKU_RELEASE_CREATION_TIMESTAMP = 'heroku.release.creation_timestamp' as const;\n\n/**\n * The CPU architecture the host system is running on.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_ARCH = 'host.arch' as const;\n\n/**\n * Enum value \"amd64\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_AMD64 = \"amd64\" as const;\n\n/**\n * Enum value \"arm32\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_ARM32 = \"arm32\" as const;\n\n/**\n * Enum value \"arm64\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_ARM64 = \"arm64\" as const;\n\n/**\n * Enum value \"ia64\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_IA64 = \"ia64\" as const;\n\n/**\n * Enum value \"ppc32\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_PPC32 = \"ppc32\" as const;\n\n/**\n * Enum value \"ppc64\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_PPC64 = \"ppc64\" as const;\n\n/**\n * Enum value \"s390x\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_S390X = \"s390x\" as const;\n\n/**\n * Enum value \"x86\" for attribute {@link ATTR_HOST_ARCH}.\n */\nexport const HOST_ARCH_VALUE_X86 = \"x86\" as const;\n\n/**\n * The amount of level 2 memory cache available to the processor (in Bytes).\n *\n * @example 12288000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_CPU_CACHE_L2_SIZE = 'host.cpu.cache.l2.size' as const;\n\n/**\n * Family or generation of the CPU.\n *\n * @example 6\n * @example PA-RISC 1.1e\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_CPU_FAMILY = 'host.cpu.family' as const;\n\n/**\n * Model identifier. It provides more granular information about the CPU, distinguishing it from other CPUs within the same family.\n *\n * @example 6\n * @example 9000/778/B180L\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_CPU_MODEL_ID = 'host.cpu.model.id' as const;\n\n/**\n * Model designation of the processor.\n *\n * @example 11th Gen Intel(R) Core(TM) i7-1185G7 @ 3.00GHz\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_CPU_MODEL_NAME = 'host.cpu.model.name' as const;\n\n/**\n * Stepping or core revisions.\n *\n * @example 1\n * @example r1p1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_CPU_STEPPING = 'host.cpu.stepping' as const;\n\n/**\n * Processor manufacturer identifier. A maximum 12-character string.\n *\n * @example GenuineIntel\n *\n * @note [CPUID](https://wiki.osdev.org/CPUID) command returns the vendor ID string in EBX, EDX and ECX registers. Writing these to memory in this order results in a 12-character string.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_CPU_VENDOR_ID = 'host.cpu.vendor.id' as const;\n\n/**\n * Unique host ID. For Cloud, this must be the instance_id assigned by the cloud provider. For non-containerized systems, this should be the `machine-id`. See the table below for the sources to use to determine the `machine-id` based on operating system.\n *\n * @example fdbf79e8af94cb7f9e8df36789187052\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_ID = 'host.id' as const;\n\n/**\n * VM image ID or host OS image ID. For Cloud, this value is from the provider.\n *\n * @example ami-07b06b442921831e5\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_IMAGE_ID = 'host.image.id' as const;\n\n/**\n * Name of the VM image or OS install the host was instantiated from.\n *\n * @example infra-ami-eks-worker-node-7d4ec78312\n * @example CentOS-8-x86_64-1905\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_IMAGE_NAME = 'host.image.name' as const;\n\n/**\n * The version string of the VM image or host OS as defined in [Version Attributes](/docs/resource/README.md#version-attributes).\n *\n * @example 0.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_IMAGE_VERSION = 'host.image.version' as const;\n\n/**\n * Available IP addresses of the host, excluding loopback interfaces.\n *\n * @example [\"*************\", \"fe80::abc2:4a28:737a:609e\"]\n *\n * @note IPv4 Addresses **MUST** be specified in dotted-quad notation. IPv6 addresses **MUST** be specified in the [RFC 5952](https://www.rfc-editor.org/rfc/rfc5952.html) format.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_IP = 'host.ip' as const;\n\n/**\n * Available MAC addresses of the host, excluding loopback interfaces.\n *\n * @example [\"AC-DE-48-23-45-67\", \"AC-DE-48-23-45-67-01-9F\"]\n *\n * @note MAC Addresses **MUST** be represented in [IEEE RA hexadecimal form](https://standards.ieee.org/wp-content/uploads/import/documents/tutorials/eui.pdf): as hyphen-separated octets in uppercase hexadecimal form from most to least significant.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_MAC = 'host.mac' as const;\n\n/**\n * Name of the host. On Unix systems, it may contain what the hostname command returns, or the fully qualified hostname, or another name specified by the user.\n *\n * @example opentelemetry-test\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_NAME = 'host.name' as const;\n\n/**\n * Type of host. For Cloud, this must be the machine type.\n *\n * @example n1-standard-1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HOST_TYPE = 'host.type' as const;\n\n/**\n * Deprecated, use `client.address` instead.\n *\n * @example \"**************\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `client.address`.\n */\nexport const ATTR_HTTP_CLIENT_IP = 'http.client_ip' as const;\n\n/**\n * State of the HTTP connection in the HTTP connection pool.\n *\n * @example active\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HTTP_CONNECTION_STATE = 'http.connection.state' as const;\n\n/**\n * Enum value \"active\" for attribute {@link ATTR_HTTP_CONNECTION_STATE}.\n */\nexport const HTTP_CONNECTION_STATE_VALUE_ACTIVE = \"active\" as const;\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_HTTP_CONNECTION_STATE}.\n */\nexport const HTTP_CONNECTION_STATE_VALUE_IDLE = \"idle\" as const;\n\n/**\n * Deprecated, use `network.protocol.name` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.protocol.name`.\n */\nexport const ATTR_HTTP_FLAVOR = 'http.flavor' as const;\n\n/**\n * Enum value \"1.0\" for attribute {@link ATTR_HTTP_FLAVOR}.\n */\nexport const HTTP_FLAVOR_VALUE_HTTP_1_0 = \"1.0\" as const;\n\n/**\n * Enum value \"1.1\" for attribute {@link ATTR_HTTP_FLAVOR}.\n */\nexport const HTTP_FLAVOR_VALUE_HTTP_1_1 = \"1.1\" as const;\n\n/**\n * Enum value \"2.0\" for attribute {@link ATTR_HTTP_FLAVOR}.\n */\nexport const HTTP_FLAVOR_VALUE_HTTP_2_0 = \"2.0\" as const;\n\n/**\n * Enum value \"3.0\" for attribute {@link ATTR_HTTP_FLAVOR}.\n */\nexport const HTTP_FLAVOR_VALUE_HTTP_3_0 = \"3.0\" as const;\n\n/**\n * Enum value \"QUIC\" for attribute {@link ATTR_HTTP_FLAVOR}.\n */\nexport const HTTP_FLAVOR_VALUE_QUIC = \"QUIC\" as const;\n\n/**\n * Enum value \"SPDY\" for attribute {@link ATTR_HTTP_FLAVOR}.\n */\nexport const HTTP_FLAVOR_VALUE_SPDY = \"SPDY\" as const;\n\n/**\n * Deprecated, use one of `server.address`, `client.address` or `http.request.header.host` instead, depending on the usage.\n *\n * @example www.example.org\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by one of `server.address`, `client.address` or `http.request.header.host`, depending on the usage.\n */\nexport const ATTR_HTTP_HOST = 'http.host' as const;\n\n/**\n * Deprecated, use `http.request.method` instead.\n *\n * @example GET\n * @example POST\n * @example HEAD\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.request.method`.\n */\nexport const ATTR_HTTP_METHOD = 'http.method' as const;\n\n/**\n * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n *\n * @example 3495\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HTTP_REQUEST_BODY_SIZE = 'http.request.body.size' as const;\n\n/**\n * The total size of the request in bytes. This should be the total number of bytes sent over the wire, including the request line (HTTP/1.1), framing (HTTP/2 and HTTP/3), headers, and request body if any.\n *\n * @example 1437\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HTTP_REQUEST_SIZE = 'http.request.size' as const;\n\n/**\n * Deprecated, use `http.request.header.content-length` instead.\n *\n * @example 3495\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.request.header.content-length`.\n */\nexport const ATTR_HTTP_REQUEST_CONTENT_LENGTH = 'http.request_content_length' as const;\n\n/**\n * Deprecated, use `http.request.body.size` instead.\n *\n * @example 5493\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.request.body.size`.\n */\nexport const ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED = 'http.request_content_length_uncompressed' as const;\n\n/**\n * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n *\n * @example 3495\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HTTP_RESPONSE_BODY_SIZE = 'http.response.body.size' as const;\n\n/**\n * The total size of the response in bytes. This should be the total number of bytes sent over the wire, including the status line (HTTP/1.1), framing (HTTP/2 and HTTP/3), headers, and response body and trailers if any.\n *\n * @example 1437\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HTTP_RESPONSE_SIZE = 'http.response.size' as const;\n\n/**\n * Deprecated, use `http.response.header.content-length` instead.\n *\n * @example 3495\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated hp.response.header.content-length\n */\nexport const ATTR_HTTP_RESPONSE_CONTENT_LENGTH = 'http.response_content_length' as const;\n\n/**\n * Deprecated, use `http.response.body.size` instead.\n *\n * @example 5493\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.response.body.size`.\n */\nexport const ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED = 'http.response_content_length_uncompressed' as const;\n\n/**\n * Deprecated, use `url.scheme` instead.\n *\n * @example http\n * @example https\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `url.scheme`.\n */\nexport const ATTR_HTTP_SCHEME = 'http.scheme' as const;\n\n/**\n * Deprecated, use `server.address` instead.\n *\n * @example example.com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address`.\n */\nexport const ATTR_HTTP_SERVER_NAME = 'http.server_name' as const;\n\n/**\n * Deprecated, use `http.response.status_code` instead.\n *\n * @example 200\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `http.response.status_code`.\n */\nexport const ATTR_HTTP_STATUS_CODE = 'http.status_code' as const;\n\n/**\n * Deprecated, use `url.path` and `url.query` instead.\n *\n * @example /search?q=OpenTelemetry#SemConv\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Split to `url.path` and `url.query`.\n */\nexport const ATTR_HTTP_TARGET = 'http.target' as const;\n\n/**\n * Deprecated, use `url.full` instead.\n *\n * @example https://www.foo.bar/search?q=OpenTelemetry#SemConv\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `url.full`.\n */\nexport const ATTR_HTTP_URL = 'http.url' as const;\n\n/**\n * Deprecated, use `user_agent.original` instead.\n *\n * @example CERN-LineMode/2.15 libwww/2.17b3\n * @example Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `user_agent.original`.\n */\nexport const ATTR_HTTP_USER_AGENT = 'http.user_agent' as const;\n\n/**\n * An identifier for the hardware component, unique within the monitored host\n *\n * @example win32battery_battery_testsysa33_1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HW_ID = 'hw.id' as const;\n\n/**\n * An easily-recognizable name for the hardware component\n *\n * @example eth0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HW_NAME = 'hw.name' as const;\n\n/**\n * Unique identifier of the parent component (typically the `hw.id` attribute of the enclosure, or disk controller)\n *\n * @example dellStorage_perc_0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HW_PARENT = 'hw.parent' as const;\n\n/**\n * The current state of the component\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HW_STATE = 'hw.state' as const;\n\n/**\n * Enum value \"degraded\" for attribute {@link ATTR_HW_STATE}.\n */\nexport const HW_STATE_VALUE_DEGRADED = \"degraded\" as const;\n\n/**\n * Enum value \"failed\" for attribute {@link ATTR_HW_STATE}.\n */\nexport const HW_STATE_VALUE_FAILED = \"failed\" as const;\n\n/**\n * Enum value \"ok\" for attribute {@link ATTR_HW_STATE}.\n */\nexport const HW_STATE_VALUE_OK = \"ok\" as const;\n\n/**\n * Type of the component\n *\n * @note Describes the category of the hardware component for which `hw.state` is being reported. For example, `hw.type=temperature` along with `hw.state=degraded` would indicate that the temperature of the hardware component has been reported as `degraded`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_HW_TYPE = 'hw.type' as const;\n\n/**\n * Enum value \"battery\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_BATTERY = \"battery\" as const;\n\n/**\n * Enum value \"cpu\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_CPU = \"cpu\" as const;\n\n/**\n * Enum value \"disk_controller\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_DISK_CONTROLLER = \"disk_controller\" as const;\n\n/**\n * Enum value \"enclosure\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_ENCLOSURE = \"enclosure\" as const;\n\n/**\n * Enum value \"fan\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_FAN = \"fan\" as const;\n\n/**\n * Enum value \"gpu\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_GPU = \"gpu\" as const;\n\n/**\n * Enum value \"logical_disk\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_LOGICAL_DISK = \"logical_disk\" as const;\n\n/**\n * Enum value \"memory\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_MEMORY = \"memory\" as const;\n\n/**\n * Enum value \"network\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_NETWORK = \"network\" as const;\n\n/**\n * Enum value \"physical_disk\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_PHYSICAL_DISK = \"physical_disk\" as const;\n\n/**\n * Enum value \"power_supply\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_POWER_SUPPLY = \"power_supply\" as const;\n\n/**\n * Enum value \"tape_drive\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_TAPE_DRIVE = \"tape_drive\" as const;\n\n/**\n * Enum value \"temperature\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_TEMPERATURE = \"temperature\" as const;\n\n/**\n * Enum value \"voltage\" for attribute {@link ATTR_HW_TYPE}.\n */\nexport const HW_TYPE_VALUE_VOLTAGE = \"voltage\" as const;\n\n/**\n * This attribute represents the state of the application.\n *\n * @note The iOS lifecycle states are defined in the [UIApplicationDelegate documentation](https://developer.apple.com/documentation/uikit/uiapplicationdelegate), and from which the `OS terminology` column values are derived.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_IOS_APP_STATE = 'ios.app.state' as const;\n\n/**\n * Enum value \"active\" for attribute {@link ATTR_IOS_APP_STATE}.\n */\nexport const IOS_APP_STATE_VALUE_ACTIVE = \"active\" as const;\n\n/**\n * Enum value \"background\" for attribute {@link ATTR_IOS_APP_STATE}.\n */\nexport const IOS_APP_STATE_VALUE_BACKGROUND = \"background\" as const;\n\n/**\n * Enum value \"foreground\" for attribute {@link ATTR_IOS_APP_STATE}.\n */\nexport const IOS_APP_STATE_VALUE_FOREGROUND = \"foreground\" as const;\n\n/**\n * Enum value \"inactive\" for attribute {@link ATTR_IOS_APP_STATE}.\n */\nexport const IOS_APP_STATE_VALUE_INACTIVE = \"inactive\" as const;\n\n/**\n * Enum value \"terminate\" for attribute {@link ATTR_IOS_APP_STATE}.\n */\nexport const IOS_APP_STATE_VALUE_TERMINATE = \"terminate\" as const;\n\n/**\n * @note The iOS lifecycle states are defined in the [UIApplicationDelegate documentation](https://developer.apple.com/documentation/uikit/uiapplicationdelegate), and from which the `OS terminology` column values are derived.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by the `ios.app.state` event body field.\n */\nexport const ATTR_IOS_STATE = 'ios.state' as const;\n\n/**\n * Enum value \"active\" for attribute {@link ATTR_IOS_STATE}.\n */\nexport const IOS_STATE_VALUE_ACTIVE = \"active\" as const;\n\n/**\n * Enum value \"background\" for attribute {@link ATTR_IOS_STATE}.\n */\nexport const IOS_STATE_VALUE_BACKGROUND = \"background\" as const;\n\n/**\n * Enum value \"foreground\" for attribute {@link ATTR_IOS_STATE}.\n */\nexport const IOS_STATE_VALUE_FOREGROUND = \"foreground\" as const;\n\n/**\n * Enum value \"inactive\" for attribute {@link ATTR_IOS_STATE}.\n */\nexport const IOS_STATE_VALUE_INACTIVE = \"inactive\" as const;\n\n/**\n * Enum value \"terminate\" for attribute {@link ATTR_IOS_STATE}.\n */\nexport const IOS_STATE_VALUE_TERMINATE = \"terminate\" as const;\n\n/**\n * Name of the buffer pool.\n *\n * @example mapped\n * @example direct\n *\n * @note Pool names are generally obtained via [BufferPoolMXBean#getName()](https://docs.oracle.com/en/java/javase/11/docs/api/java.management/java/lang/management/BufferPoolMXBean.html#getName()).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_JVM_BUFFER_POOL_NAME = 'jvm.buffer.pool.name' as const;\n\n/**\n * Name of the garbage collector cause.\n *\n * @example System.gc()\n * @example Allocation Failure\n *\n * @note Garbage collector cause is generally obtained via [GarbageCollectionNotificationInfo#getGcCause()](https://docs.oracle.com/en/java/javase/11/docs/api/jdk.management/com/sun/management/GarbageCollectionNotificationInfo.html#getGcCause()).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_JVM_GC_CAUSE = 'jvm.gc.cause' as const;\n\n/**\n * The name of the cluster.\n *\n * @example opentelemetry-cluster\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CLUSTER_NAME = 'k8s.cluster.name' as const;\n\n/**\n * A pseudo-ID for the cluster, set to the UID of the `kube-system` namespace.\n *\n * @example 218fc5a9-a5f1-4b54-aa05-46717d0ab26d\n *\n * @note K8s doesn't have support for obtaining a cluster ID. If this is ever\n * added, we will recommend collecting the `k8s.cluster.uid` through the\n * official APIs. In the meantime, we are able to use the `uid` of the\n * `kube-system` namespace as a proxy for cluster ID. Read on for the\n * rationale.\n *\n * Every object created in a K8s cluster is assigned a distinct UID. The\n * `kube-system` namespace is used by Kubernetes itself and will exist\n * for the lifetime of the cluster. Using the `uid` of the `kube-system`\n * namespace is a reasonable proxy for the K8s ClusterID as it will only\n * change if the cluster is rebuilt. Furthermore, Kubernetes UIDs are\n * UUIDs as standardized by\n * [ISO/IEC 9834-8 and ITU-T X.667](https://www.itu.int/ITU-T/studygroups/com17/oid.html).\n * Which states:\n *\n * > If generated according to one of the mechanisms defined in Rec.\n * > ITU-T X.667 | ISO/IEC 9834-8, a UUID is either guaranteed to be\n * > different from all other UUIDs generated before 3603 A.D., or is\n * > extremely likely to be different (depending on the mechanism chosen).\n *\n * Therefore, UIDs between clusters should be extremely unlikely to\n * conflict.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CLUSTER_UID = 'k8s.cluster.uid' as const;\n\n/**\n * The name of the Container from Pod specification, must be unique within a Pod. Container runtime usually uses different globally unique name (`container.name`).\n *\n * @example redis\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CONTAINER_NAME = 'k8s.container.name' as const;\n\n/**\n * Number of times the container was restarted. This attribute can be used to identify a particular container (running or stopped) within a container spec.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CONTAINER_RESTART_COUNT = 'k8s.container.restart_count' as const;\n\n/**\n * Last terminated reason of the Container.\n *\n * @example Evicted\n * @example Error\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CONTAINER_STATUS_LAST_TERMINATED_REASON = 'k8s.container.status.last_terminated_reason' as const;\n\n/**\n * The cronjob annotation placed on the CronJob, the `<key>` being the annotation name, the value being the annotation value.\n *\n * @example 4\n * @example\n *\n * @note Examples:\n *\n *   - An annotation `retries` with value `4` **SHOULD** be recorded as the\n *     `k8s.cronjob.annotation.retries` attribute with value `\"4\"`.\n *   - An annotation `data` with empty string value **SHOULD** be recorded as\n *     the `k8s.cronjob.annotation.data` attribute with value `\"\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CRONJOB_ANNOTATION = (key: string) => `k8s.cronjob.annotation.${key}`;\n\n/**\n * The label placed on the CronJob, the `<key>` being the label name, the value being the label value.\n *\n * @example weekly\n * @example\n *\n * @note Examples:\n *\n *   - A label `type` with value `weekly` **SHOULD** be recorded as the\n *     `k8s.cronjob.label.type` attribute with value `\"weekly\"`.\n *   - A label `automated` with empty string value **SHOULD** be recorded as\n *     the `k8s.cronjob.label.automated` attribute with value `\"\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CRONJOB_LABEL = (key: string) => `k8s.cronjob.label.${key}`;\n\n/**\n * The name of the CronJob.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CRONJOB_NAME = 'k8s.cronjob.name' as const;\n\n/**\n * The UID of the CronJob.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_CRONJOB_UID = 'k8s.cronjob.uid' as const;\n\n/**\n * The annotation key-value pairs placed on the DaemonSet.\n *\n * @example k8s.daemonset.annotation.replicas=1\n * @example k8s.daemonset.annotation.data=\n *\n * @note The `<key>` being the annotation name, the value being the annotation value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DAEMONSET_ANNOTATION = (key: string) => `k8s.daemonset.annotation.${key}`;\n\n/**\n * The label key-value pairs placed on the DaemonSet.\n *\n * @example k8s.daemonset.label.app=guestbook\n * @example k8s.daemonset.label.injected=\n *\n * @note The `<key>` being the label name, the value being the label value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DAEMONSET_LABEL = (key: string) => `k8s.daemonset.label.${key}`;\n\n/**\n * The name of the DaemonSet.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DAEMONSET_NAME = 'k8s.daemonset.name' as const;\n\n/**\n * The UID of the DaemonSet.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DAEMONSET_UID = 'k8s.daemonset.uid' as const;\n\n/**\n * The annotation key-value pairs placed on the Deployment.\n *\n * @example k8s.deployment.annotation.replicas=1\n * @example k8s.deployment.annotation.data=\n *\n * @note The `<key>` being the annotation name, the value being the annotation value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DEPLOYMENT_ANNOTATION = (key: string) => `k8s.deployment.annotation.${key}`;\n\n/**\n * The label key-value pairs placed on the Deployment.\n *\n * @example k8s.deployment.label.app=guestbook\n * @example k8s.deployment.label.injected=\n *\n * @note The `<key>` being the label name, the value being the label value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DEPLOYMENT_LABEL = (key: string) => `k8s.deployment.label.${key}`;\n\n/**\n * The name of the Deployment.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name' as const;\n\n/**\n * The UID of the Deployment.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid' as const;\n\n/**\n * The name of the horizontal pod autoscaler.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_HPA_NAME = 'k8s.hpa.name' as const;\n\n/**\n * The UID of the horizontal pod autoscaler.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_HPA_UID = 'k8s.hpa.uid' as const;\n\n/**\n * The annotation key-value pairs placed on the Job.\n *\n * @example k8s.job.annotation.number=1\n * @example k8s.job.annotation.data=\n *\n * @note The `<key>` being the annotation name, the value being the annotation value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_JOB_ANNOTATION = (key: string) => `k8s.job.annotation.${key}`;\n\n/**\n * The label key-value pairs placed on the Job.\n *\n * @example k8s.job.label.jobtype=ci\n * @example k8s.job.label.automated=\n *\n * @note The `<key>` being the label name, the value being the label value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_JOB_LABEL = (key: string) => `k8s.job.label.${key}`;\n\n/**\n * The name of the Job.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_JOB_NAME = 'k8s.job.name' as const;\n\n/**\n * The UID of the Job.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_JOB_UID = 'k8s.job.uid' as const;\n\n/**\n * The annotation key-value pairs placed on the Namespace.\n *\n * @example k8s.namespace.annotation.ttl=0\n * @example k8s.namespace.annotation.data=\n *\n * @note The `<key>` being the annotation name, the value being the annotation value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NAMESPACE_ANNOTATION = (key: string) => `k8s.namespace.annotation.${key}`;\n\n/**\n * The label key-value pairs placed on the Namespace.\n *\n * @example k8s.namespace.label.kubernetes.io/metadata.name=default\n * @example k8s.namespace.label.data=\n *\n * @note The `<key>` being the label name, the value being the label value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NAMESPACE_LABEL = (key: string) => `k8s.namespace.label.${key}`;\n\n/**\n * The name of the namespace that the pod is running in.\n *\n * @example default\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NAMESPACE_NAME = 'k8s.namespace.name' as const;\n\n/**\n * The phase of the K8s namespace.\n *\n * @example active\n * @example terminating\n *\n * @note This attribute aligns with the `phase` field of the\n * [K8s NamespaceStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.30/#namespacestatus-v1-core)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NAMESPACE_PHASE = 'k8s.namespace.phase' as const;\n\n/**\n * Enum value \"active\" for attribute {@link ATTR_K8S_NAMESPACE_PHASE}.\n */\nexport const K8S_NAMESPACE_PHASE_VALUE_ACTIVE = \"active\" as const;\n\n/**\n * Enum value \"terminating\" for attribute {@link ATTR_K8S_NAMESPACE_PHASE}.\n */\nexport const K8S_NAMESPACE_PHASE_VALUE_TERMINATING = \"terminating\" as const;\n\n/**\n * The annotation placed on the Node, the `<key>` being the annotation name, the value being the annotation value, even if the value is empty.\n *\n * @example 0\n * @example\n *\n * @note Examples:\n *\n *   - An annotation `node.alpha.kubernetes.io/ttl` with value `0` **SHOULD** be recorded as\n *     the `k8s.node.annotation.node.alpha.kubernetes.io/ttl` attribute with value `\"0\"`.\n *   - An annotation `data` with empty string value **SHOULD** be recorded as\n *     the `k8s.node.annotation.data` attribute with value `\"\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NODE_ANNOTATION = (key: string) => `k8s.node.annotation.${key}`;\n\n/**\n * The label placed on the Node, the `<key>` being the label name, the value being the label value, even if the value is empty.\n *\n * @example arm64\n * @example\n *\n * @note Examples:\n *\n *   - A label `kubernetes.io/arch` with value `arm64` **SHOULD** be recorded\n *     as the `k8s.node.label.kubernetes.io/arch` attribute with value `\"arm64\"`.\n *   - A label `data` with empty string value **SHOULD** be recorded as\n *     the `k8s.node.label.data` attribute with value `\"\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NODE_LABEL = (key: string) => `k8s.node.label.${key}`;\n\n/**\n * The name of the Node.\n *\n * @example node-1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NODE_NAME = 'k8s.node.name' as const;\n\n/**\n * The UID of the Node.\n *\n * @example 1eb3a0c6-0477-4080-a9cb-0cb7db65c6a2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_NODE_UID = 'k8s.node.uid' as const;\n\n/**\n * The annotation placed on the Pod, the `<key>` being the annotation name, the value being the annotation value.\n *\n * @example true\n * @example x64\n * @example\n *\n * @note Examples:\n *\n *   - An annotation `kubernetes.io/enforce-mountable-secrets` with value `true` **SHOULD** be recorded as\n *     the `k8s.pod.annotation.kubernetes.io/enforce-mountable-secrets` attribute with value `\"true\"`.\n *   - An annotation `mycompany.io/arch` with value `x64` **SHOULD** be recorded as\n *     the `k8s.pod.annotation.mycompany.io/arch` attribute with value `\"x64\"`.\n *   - An annotation `data` with empty string value **SHOULD** be recorded as\n *     the `k8s.pod.annotation.data` attribute with value `\"\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_POD_ANNOTATION = (key: string) => `k8s.pod.annotation.${key}`;\n\n/**\n * The label placed on the Pod, the `<key>` being the label name, the value being the label value.\n *\n * @example my-app\n * @example x64\n * @example\n *\n * @note Examples:\n *\n *   - A label `app` with value `my-app` **SHOULD** be recorded as\n *     the `k8s.pod.label.app` attribute with value `\"my-app\"`.\n *   - A label `mycompany.io/arch` with value `x64` **SHOULD** be recorded as\n *     the `k8s.pod.label.mycompany.io/arch` attribute with value `\"x64\"`.\n *   - A label `data` with empty string value **SHOULD** be recorded as\n *     the `k8s.pod.label.data` attribute with value `\"\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_POD_LABEL = (key: string) => `k8s.pod.label.${key}`;\n\n/**\n * Deprecated, use `k8s.pod.label` instead.\n *\n * @example my-app\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `k8s.pod.label`.\n */\nexport const ATTR_K8S_POD_LABELS = (key: string) => `k8s.pod.labels.${key}`;\n\n/**\n * The name of the Pod.\n *\n * @example opentelemetry-pod-autoconf\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_POD_NAME = 'k8s.pod.name' as const;\n\n/**\n * The UID of the Pod.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_POD_UID = 'k8s.pod.uid' as const;\n\n/**\n * The annotation key-value pairs placed on the ReplicaSet.\n *\n * @example k8s.replicaset.annotation.replicas=0\n * @example k8s.replicaset.annotation.data=\n *\n * @note The `<key>` being the annotation name, the value being the annotation value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_REPLICASET_ANNOTATION = (key: string) => `k8s.replicaset.annotation.${key}`;\n\n/**\n * The label key-value pairs placed on the ReplicaSet.\n *\n * @example k8s.replicaset.label.app=guestbook\n * @example k8s.replicaset.label.injected=\n *\n * @note The `<key>` being the label name, the value being the label value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_REPLICASET_LABEL = (key: string) => `k8s.replicaset.label.${key}`;\n\n/**\n * The name of the ReplicaSet.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_REPLICASET_NAME = 'k8s.replicaset.name' as const;\n\n/**\n * The UID of the ReplicaSet.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_REPLICASET_UID = 'k8s.replicaset.uid' as const;\n\n/**\n * The name of the replication controller.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_REPLICATIONCONTROLLER_NAME = 'k8s.replicationcontroller.name' as const;\n\n/**\n * The UID of the replication controller.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_REPLICATIONCONTROLLER_UID = 'k8s.replicationcontroller.uid' as const;\n\n/**\n * The name of the resource quota.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_RESOURCEQUOTA_NAME = 'k8s.resourcequota.name' as const;\n\n/**\n * The UID of the resource quota.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_RESOURCEQUOTA_UID = 'k8s.resourcequota.uid' as const;\n\n/**\n * The annotation key-value pairs placed on the StatefulSet.\n *\n * @example k8s.statefulset.annotation.replicas=1\n * @example k8s.statefulset.annotation.data=\n *\n * @note The `<key>` being the annotation name, the value being the annotation value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_STATEFULSET_ANNOTATION = (key: string) => `k8s.statefulset.annotation.${key}`;\n\n/**\n * The label key-value pairs placed on the StatefulSet.\n *\n * @example k8s.statefulset.label.app=guestbook\n * @example k8s.statefulset.label.injected=\n *\n * @note The `<key>` being the label name, the value being the label value, even if the value is empty.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_STATEFULSET_LABEL = (key: string) => `k8s.statefulset.label.${key}`;\n\n/**\n * The name of the StatefulSet.\n *\n * @example opentelemetry\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_STATEFULSET_NAME = 'k8s.statefulset.name' as const;\n\n/**\n * The UID of the StatefulSet.\n *\n * @example 275ecb36-5aa8-4c2a-9c47-d8bb681b9aff\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_STATEFULSET_UID = 'k8s.statefulset.uid' as const;\n\n/**\n * The name of the K8s volume.\n *\n * @example volume0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_VOLUME_NAME = 'k8s.volume.name' as const;\n\n/**\n * The type of the K8s volume.\n *\n * @example emptyDir\n * @example persistentVolumeClaim\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_K8S_VOLUME_TYPE = 'k8s.volume.type' as const;\n\n/**\n * Enum value \"configMap\" for attribute {@link ATTR_K8S_VOLUME_TYPE}.\n */\nexport const K8S_VOLUME_TYPE_VALUE_CONFIG_MAP = \"configMap\" as const;\n\n/**\n * Enum value \"downwardAPI\" for attribute {@link ATTR_K8S_VOLUME_TYPE}.\n */\nexport const K8S_VOLUME_TYPE_VALUE_DOWNWARD_API = \"downwardAPI\" as const;\n\n/**\n * Enum value \"emptyDir\" for attribute {@link ATTR_K8S_VOLUME_TYPE}.\n */\nexport const K8S_VOLUME_TYPE_VALUE_EMPTY_DIR = \"emptyDir\" as const;\n\n/**\n * Enum value \"local\" for attribute {@link ATTR_K8S_VOLUME_TYPE}.\n */\nexport const K8S_VOLUME_TYPE_VALUE_LOCAL = \"local\" as const;\n\n/**\n * Enum value \"persistentVolumeClaim\" for attribute {@link ATTR_K8S_VOLUME_TYPE}.\n */\nexport const K8S_VOLUME_TYPE_VALUE_PERSISTENT_VOLUME_CLAIM = \"persistentVolumeClaim\" as const;\n\n/**\n * Enum value \"secret\" for attribute {@link ATTR_K8S_VOLUME_TYPE}.\n */\nexport const K8S_VOLUME_TYPE_VALUE_SECRET = \"secret\" as const;\n\n/**\n * The Linux Slab memory state\n *\n * @example reclaimable\n * @example unreclaimable\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LINUX_MEMORY_SLAB_STATE = 'linux.memory.slab.state' as const;\n\n/**\n * Enum value \"reclaimable\" for attribute {@link ATTR_LINUX_MEMORY_SLAB_STATE}.\n */\nexport const LINUX_MEMORY_SLAB_STATE_VALUE_RECLAIMABLE = \"reclaimable\" as const;\n\n/**\n * Enum value \"unreclaimable\" for attribute {@link ATTR_LINUX_MEMORY_SLAB_STATE}.\n */\nexport const LINUX_MEMORY_SLAB_STATE_VALUE_UNRECLAIMABLE = \"unreclaimable\" as const;\n\n/**\n * The basename of the file.\n *\n * @example audit.log\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LOG_FILE_NAME = 'log.file.name' as const;\n\n/**\n * The basename of the file, with symlinks resolved.\n *\n * @example uuid.log\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LOG_FILE_NAME_RESOLVED = 'log.file.name_resolved' as const;\n\n/**\n * The full path to the file.\n *\n * @example /var/log/mysql/audit.log\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LOG_FILE_PATH = 'log.file.path' as const;\n\n/**\n * The full path to the file, with symlinks resolved.\n *\n * @example /var/lib/docker/uuid.log\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LOG_FILE_PATH_RESOLVED = 'log.file.path_resolved' as const;\n\n/**\n * The stream associated with the log. See below for a list of well-known values.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LOG_IOSTREAM = 'log.iostream' as const;\n\n/**\n * Enum value \"stderr\" for attribute {@link ATTR_LOG_IOSTREAM}.\n */\nexport const LOG_IOSTREAM_VALUE_STDERR = \"stderr\" as const;\n\n/**\n * Enum value \"stdout\" for attribute {@link ATTR_LOG_IOSTREAM}.\n */\nexport const LOG_IOSTREAM_VALUE_STDOUT = \"stdout\" as const;\n\n/**\n * The complete original Log Record.\n *\n * @example 77 <86>1 2015-08-06T21:58:59.694Z 192.168.2.133 inactive - - - Something happened\n * @example [INFO] 8/3/24 12:34:56 Something happened\n *\n * @note This value **MAY** be added when processing a Log Record which was originally transmitted as a string or equivalent data type AND the Body field of the Log Record does not contain the same value. (e.g. a syslog or a log record read from a file.)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LOG_RECORD_ORIGINAL = 'log.record.original' as const;\n\n/**\n * A unique identifier for the Log Record.\n *\n * @example 01ARZ3NDEKTSV4RRFFQ69G5FAV\n *\n * @note If an id is provided, other log records with the same id will be considered duplicates and can be removed safely. This means, that two distinguishable log records **MUST** have different values.\n * The id **MAY** be an [Universally Unique Lexicographically Sortable Identifier (ULID)](https://github.com/ulid/spec), but other identifiers (e.g. UUID) may be used as needed.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_LOG_RECORD_UID = 'log.record.uid' as const;\n\n/**\n * Deprecated, use `rpc.message.compressed_size` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `rpc.message.compressed_size`.\n */\nexport const ATTR_MESSAGE_COMPRESSED_SIZE = 'message.compressed_size' as const;\n\n/**\n * Deprecated, use `rpc.message.id` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `rpc.message.id`.\n */\nexport const ATTR_MESSAGE_ID = 'message.id' as const;\n\n/**\n * Deprecated, use `rpc.message.type` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `rpc.message.type`.\n */\nexport const ATTR_MESSAGE_TYPE = 'message.type' as const;\n\n/**\n * Enum value \"RECEIVED\" for attribute {@link ATTR_MESSAGE_TYPE}.\n */\nexport const MESSAGE_TYPE_VALUE_RECEIVED = \"RECEIVED\" as const;\n\n/**\n * Enum value \"SENT\" for attribute {@link ATTR_MESSAGE_TYPE}.\n */\nexport const MESSAGE_TYPE_VALUE_SENT = \"SENT\" as const;\n\n/**\n * Deprecated, use `rpc.message.uncompressed_size` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `rpc.message.uncompressed_size`.\n */\nexport const ATTR_MESSAGE_UNCOMPRESSED_SIZE = 'message.uncompressed_size' as const;\n\n/**\n * The number of messages sent, received, or processed in the scope of the batching operation.\n *\n * @example 0\n * @example 1\n * @example 2\n *\n * @note Instrumentations **SHOULD NOT** set `messaging.batch.message_count` on spans that operate with a single message. When a messaging client library supports both batch and single-message API for the same operation, instrumentations **SHOULD** use `messaging.batch.message_count` for batching APIs and **SHOULD NOT** use it for single-message APIs.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_BATCH_MESSAGE_COUNT = 'messaging.batch.message_count' as const;\n\n/**\n * A unique identifier for the client that consumes or produces a message.\n *\n * @example client-5\n * @example myhost@8742@s8083jm\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_CLIENT_ID = 'messaging.client.id' as const;\n\n/**\n * The name of the consumer group with which a consumer is associated.\n *\n * @example my-group\n * @example indexer\n *\n * @note Semantic conventions for individual messaging systems **SHOULD** document whether `messaging.consumer.group.name` is applicable and what it means in the context of that system.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_CONSUMER_GROUP_NAME = 'messaging.consumer.group.name' as const;\n\n/**\n * A boolean that is true if the message destination is anonymous (could be unnamed or have auto-generated name).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_ANONYMOUS = 'messaging.destination.anonymous' as const;\n\n/**\n * The message destination name\n *\n * @example MyQueue\n * @example MyTopic\n *\n * @note Destination name **SHOULD** uniquely identify a specific queue, topic or other entity within the broker. If\n * the broker doesn't have such notion, the destination name **SHOULD** uniquely identify the broker.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_NAME = 'messaging.destination.name' as const;\n\n/**\n * The identifier of the partition messages are sent to or received from, unique within the `messaging.destination.name`.\n *\n * @example \"1\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_PARTITION_ID = 'messaging.destination.partition.id' as const;\n\n/**\n * The name of the destination subscription from which a message is consumed.\n *\n * @example subscription-a\n *\n * @note Semantic conventions for individual messaging systems **SHOULD** document whether `messaging.destination.subscription.name` is applicable and what it means in the context of that system.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_SUBSCRIPTION_NAME = 'messaging.destination.subscription.name' as const;\n\n/**\n * Low cardinality representation of the messaging destination name\n *\n * @example /customers/{customerId}\n *\n * @note Destination names could be constructed from templates. An example would be a destination name involving a user name or product id. Although the destination name in this case is of high cardinality, the underlying template is of low cardinality and can be effectively used for grouping and aggregation.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_TEMPLATE = 'messaging.destination.template' as const;\n\n/**\n * A boolean that is true if the message destination is temporary and might not exist anymore after messages are processed.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_DESTINATION_TEMPORARY = 'messaging.destination.temporary' as const;\n\n/**\n * Deprecated, no replacement at this time.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed. No replacement at this time.\n */\nexport const ATTR_MESSAGING_DESTINATION_PUBLISH_ANONYMOUS = 'messaging.destination_publish.anonymous' as const;\n\n/**\n * Deprecated, no replacement at this time.\n *\n * @example MyQueue\n * @example MyTopic\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed. No replacement at this time.\n */\nexport const ATTR_MESSAGING_DESTINATION_PUBLISH_NAME = 'messaging.destination_publish.name' as const;\n\n/**\n * Deprecated, use `messaging.consumer.group.name` instead.\n *\n * @example \"$Default\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.consumer.group.name`.\n */\nexport const ATTR_MESSAGING_EVENTHUBS_CONSUMER_GROUP = 'messaging.eventhubs.consumer.group' as const;\n\n/**\n * The UTC epoch seconds at which the message has been accepted and stored in the entity.\n *\n * @example 1701393730\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_EVENTHUBS_MESSAGE_ENQUEUED_TIME = 'messaging.eventhubs.message.enqueued_time' as const;\n\n/**\n * The ack deadline in seconds set for the modify ack deadline request.\n *\n * @example 10\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_DEADLINE = 'messaging.gcp_pubsub.message.ack_deadline' as const;\n\n/**\n * The ack id for a given message.\n *\n * @example \"ack_id\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_ID = 'messaging.gcp_pubsub.message.ack_id' as const;\n\n/**\n * The delivery attempt for a given message.\n *\n * @example 2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_DELIVERY_ATTEMPT = 'messaging.gcp_pubsub.message.delivery_attempt' as const;\n\n/**\n * The ordering key for a given message. If the attribute is not present, the message does not have an ordering key.\n *\n * @example \"ordering_key\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ORDERING_KEY = 'messaging.gcp_pubsub.message.ordering_key' as const;\n\n/**\n * Deprecated, use `messaging.consumer.group.name` instead.\n *\n * @example \"my-group\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.consumer.group.name`.\n */\nexport const ATTR_MESSAGING_KAFKA_CONSUMER_GROUP = 'messaging.kafka.consumer.group' as const;\n\n/**\n * Deprecated, use `messaging.destination.partition.id` instead.\n *\n * @example 2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.destination.partition.id`.\n */\nexport const ATTR_MESSAGING_KAFKA_DESTINATION_PARTITION = 'messaging.kafka.destination.partition' as const;\n\n/**\n * Message keys in Kafka are used for grouping alike messages to ensure they're processed on the same partition. They differ from `messaging.message.id` in that they're not unique. If the key is `null`, the attribute **MUST NOT** be set.\n *\n * @example \"myKey\"\n *\n * @note If the key type is not string, it's string representation has to be supplied for the attribute. If the key has no unambiguous, canonical string form, don't include its value.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_KAFKA_MESSAGE_KEY = 'messaging.kafka.message.key' as const;\n\n/**\n * Deprecated, use `messaging.kafka.offset` instead.\n *\n * @example 42\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.kafka.offset`.\n */\nexport const ATTR_MESSAGING_KAFKA_MESSAGE_OFFSET = 'messaging.kafka.message.offset' as const;\n\n/**\n * A boolean that is true if the message is a tombstone.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE = 'messaging.kafka.message.tombstone' as const;\n\n/**\n * The offset of a record in the corresponding Kafka partition.\n *\n * @example 42\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_KAFKA_OFFSET = 'messaging.kafka.offset' as const;\n\n/**\n * The size of the message body in bytes.\n *\n * @example 1439\n *\n * @note This can refer to both the compressed or uncompressed body size. If both sizes are known, the uncompressed\n * body size should be used.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_MESSAGE_BODY_SIZE = 'messaging.message.body.size' as const;\n\n/**\n * The conversation ID identifying the conversation to which the message belongs, represented as a string. Sometimes called \"Correlation ID\".\n *\n * @example \"MyConversationId\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_MESSAGE_CONVERSATION_ID = 'messaging.message.conversation_id' as const;\n\n/**\n * The size of the message body and metadata in bytes.\n *\n * @example 2738\n *\n * @note This can refer to both the compressed or uncompressed size. If both sizes are known, the uncompressed\n * size should be used.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_MESSAGE_ENVELOPE_SIZE = 'messaging.message.envelope.size' as const;\n\n/**\n * A value used by the messaging system as an identifier for the message, represented as a string.\n *\n * @example \"452a7c7c7c7048c2f887f61572b18fc2\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_MESSAGE_ID = 'messaging.message.id' as const;\n\n/**\n * Deprecated, use `messaging.operation.type` instead.\n *\n * @example publish\n * @example create\n * @example process\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.operation.type`.\n */\nexport const ATTR_MESSAGING_OPERATION = 'messaging.operation' as const;\n\n/**\n * The system-specific name of the messaging operation.\n *\n * @example ack\n * @example nack\n * @example send\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_OPERATION_NAME = 'messaging.operation.name' as const;\n\n/**\n * A string identifying the type of the messaging operation.\n *\n * @note If a custom value is used, it **MUST** be of low cardinality.\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_OPERATION_TYPE = 'messaging.operation.type' as const;\n\n/**\n * Enum value \"create\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_CREATE = \"create\" as const;\n\n/**\n * Enum value \"deliver\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_DELIVER = \"deliver\" as const;\n\n/**\n * Enum value \"process\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_PROCESS = \"process\" as const;\n\n/**\n * Enum value \"publish\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_PUBLISH = \"publish\" as const;\n\n/**\n * Enum value \"receive\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_RECEIVE = \"receive\" as const;\n\n/**\n * Enum value \"send\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_SEND = \"send\" as const;\n\n/**\n * Enum value \"settle\" for attribute {@link ATTR_MESSAGING_OPERATION_TYPE}.\n */\nexport const MESSAGING_OPERATION_TYPE_VALUE_SETTLE = \"settle\" as const;\n\n/**\n * RabbitMQ message routing key.\n *\n * @example \"myKey\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_RABBITMQ_DESTINATION_ROUTING_KEY = 'messaging.rabbitmq.destination.routing_key' as const;\n\n/**\n * RabbitMQ message delivery tag\n *\n * @example 123\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_RABBITMQ_MESSAGE_DELIVERY_TAG = 'messaging.rabbitmq.message.delivery_tag' as const;\n\n/**\n * Deprecated, use `messaging.consumer.group.name` instead.\n *\n * @example \"myConsumerGroup\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.consumer.group.name` on the consumer spans. No replacement for producer spans.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_CLIENT_GROUP = 'messaging.rocketmq.client_group' as const;\n\n/**\n * Model of message consumption. This only applies to consumer spans.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL = 'messaging.rocketmq.consumption_model' as const;\n\n/**\n * Enum value \"broadcasting\" for attribute {@link ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL}.\n */\nexport const MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_BROADCASTING = \"broadcasting\" as const;\n\n/**\n * Enum value \"clustering\" for attribute {@link ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL}.\n */\nexport const MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_CLUSTERING = \"clustering\" as const;\n\n/**\n * The delay time level for delay message, which determines the message delay time.\n *\n * @example 3\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELAY_TIME_LEVEL = 'messaging.rocketmq.message.delay_time_level' as const;\n\n/**\n * The timestamp in milliseconds that the delay message is expected to be delivered to consumer.\n *\n * @example 1665987217045\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELIVERY_TIMESTAMP = 'messaging.rocketmq.message.delivery_timestamp' as const;\n\n/**\n * It is essential for FIFO message. Messages that belong to the same message group are always processed one by one within the same consumer group.\n *\n * @example \"myMessageGroup\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_MESSAGE_GROUP = 'messaging.rocketmq.message.group' as const;\n\n/**\n * Key(s) of message, another way to mark message besides message id.\n *\n * @example [\"keyA\", \"keyB\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_MESSAGE_KEYS = 'messaging.rocketmq.message.keys' as const;\n\n/**\n * The secondary classifier of message besides topic.\n *\n * @example \"tagA\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_MESSAGE_TAG = 'messaging.rocketmq.message.tag' as const;\n\n/**\n * Type of message.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE = 'messaging.rocketmq.message.type' as const;\n\n/**\n * Enum value \"delay\" for attribute {@link ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE}.\n */\nexport const MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_DELAY = \"delay\" as const;\n\n/**\n * Enum value \"fifo\" for attribute {@link ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE}.\n */\nexport const MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_FIFO = \"fifo\" as const;\n\n/**\n * Enum value \"normal\" for attribute {@link ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE}.\n */\nexport const MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_NORMAL = \"normal\" as const;\n\n/**\n * Enum value \"transaction\" for attribute {@link ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE}.\n */\nexport const MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_TRANSACTION = \"transaction\" as const;\n\n/**\n * Namespace of RocketMQ resources, resources in different namespaces are individual.\n *\n * @example \"myNamespace\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_ROCKETMQ_NAMESPACE = 'messaging.rocketmq.namespace' as const;\n\n/**\n * Deprecated, use `messaging.destination.subscription.name` instead.\n *\n * @example \"subscription-a\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `messaging.destination.subscription.name`.\n */\nexport const ATTR_MESSAGING_SERVICEBUS_DESTINATION_SUBSCRIPTION_NAME = 'messaging.servicebus.destination.subscription_name' as const;\n\n/**\n * Describes the [settlement type](https://learn.microsoft.com/azure/service-bus-messaging/message-transfers-locks-settlement#peeklock).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS = 'messaging.servicebus.disposition_status' as const;\n\n/**\n * Enum value \"abandon\" for attribute {@link ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS}.\n */\nexport const MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_ABANDON = \"abandon\" as const;\n\n/**\n * Enum value \"complete\" for attribute {@link ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS}.\n */\nexport const MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_COMPLETE = \"complete\" as const;\n\n/**\n * Enum value \"dead_letter\" for attribute {@link ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS}.\n */\nexport const MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEAD_LETTER = \"dead_letter\" as const;\n\n/**\n * Enum value \"defer\" for attribute {@link ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS}.\n */\nexport const MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEFER = \"defer\" as const;\n\n/**\n * Number of deliveries that have been attempted for this message.\n *\n * @example 2\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_SERVICEBUS_MESSAGE_DELIVERY_COUNT = 'messaging.servicebus.message.delivery_count' as const;\n\n/**\n * The UTC epoch seconds at which the message has been accepted and stored in the entity.\n *\n * @example 1701393730\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_SERVICEBUS_MESSAGE_ENQUEUED_TIME = 'messaging.servicebus.message.enqueued_time' as const;\n\n/**\n * The messaging system as identified by the client instrumentation.\n *\n * @note The actual messaging system may differ from the one known by the client. For example, when using Kafka client libraries to communicate with Azure Event Hubs, the `messaging.system` is set to `kafka` based on the instrumentation's best knowledge.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_MESSAGING_SYSTEM = 'messaging.system' as const;\n\n/**\n * Enum value \"activemq\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_ACTIVEMQ = \"activemq\" as const;\n\n/**\n * Enum value \"aws_sqs\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_AWS_SQS = \"aws_sqs\" as const;\n\n/**\n * Enum value \"eventgrid\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_EVENTGRID = \"eventgrid\" as const;\n\n/**\n * Enum value \"eventhubs\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_EVENTHUBS = \"eventhubs\" as const;\n\n/**\n * Enum value \"gcp_pubsub\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_GCP_PUBSUB = \"gcp_pubsub\" as const;\n\n/**\n * Enum value \"jms\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_JMS = \"jms\" as const;\n\n/**\n * Enum value \"kafka\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_KAFKA = \"kafka\" as const;\n\n/**\n * Enum value \"pulsar\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_PULSAR = \"pulsar\" as const;\n\n/**\n * Enum value \"rabbitmq\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_RABBITMQ = \"rabbitmq\" as const;\n\n/**\n * Enum value \"rocketmq\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_ROCKETMQ = \"rocketmq\" as const;\n\n/**\n * Enum value \"servicebus\" for attribute {@link ATTR_MESSAGING_SYSTEM}.\n */\nexport const MESSAGING_SYSTEM_VALUE_SERVICEBUS = \"servicebus\" as const;\n\n/**\n * Deprecated, use `network.local.address`.\n *\n * @example \"***********\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.local.address`.\n */\nexport const ATTR_NET_HOST_IP = 'net.host.ip' as const;\n\n/**\n * Deprecated, use `server.address`.\n *\n * @example example.com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address`.\n */\nexport const ATTR_NET_HOST_NAME = 'net.host.name' as const;\n\n/**\n * Deprecated, use `server.port`.\n *\n * @example 8080\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.port`.\n */\nexport const ATTR_NET_HOST_PORT = 'net.host.port' as const;\n\n/**\n * Deprecated, use `network.peer.address`.\n *\n * @example \"127.0.0.1\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.peer.address`.\n */\nexport const ATTR_NET_PEER_IP = 'net.peer.ip' as const;\n\n/**\n * Deprecated, use `server.address` on client spans and `client.address` on server spans.\n *\n * @example example.com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address` on client spans and `client.address` on server spans.\n */\nexport const ATTR_NET_PEER_NAME = 'net.peer.name' as const;\n\n/**\n * Deprecated, use `server.port` on client spans and `client.port` on server spans.\n *\n * @example 8080\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.port` on client spans and `client.port` on server spans.\n */\nexport const ATTR_NET_PEER_PORT = 'net.peer.port' as const;\n\n/**\n * Deprecated, use `network.protocol.name`.\n *\n * @example amqp\n * @example http\n * @example mqtt\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.protocol.name`.\n */\nexport const ATTR_NET_PROTOCOL_NAME = 'net.protocol.name' as const;\n\n/**\n * Deprecated, use `network.protocol.version`.\n *\n * @example \"3.1.1\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.protocol.version`.\n */\nexport const ATTR_NET_PROTOCOL_VERSION = 'net.protocol.version' as const;\n\n/**\n * Deprecated, use `network.transport` and `network.type`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Split to `network.transport` and `network.type`.\n */\nexport const ATTR_NET_SOCK_FAMILY = 'net.sock.family' as const;\n\n/**\n * Enum value \"inet\" for attribute {@link ATTR_NET_SOCK_FAMILY}.\n */\nexport const NET_SOCK_FAMILY_VALUE_INET = \"inet\" as const;\n\n/**\n * Enum value \"inet6\" for attribute {@link ATTR_NET_SOCK_FAMILY}.\n */\nexport const NET_SOCK_FAMILY_VALUE_INET6 = \"inet6\" as const;\n\n/**\n * Enum value \"unix\" for attribute {@link ATTR_NET_SOCK_FAMILY}.\n */\nexport const NET_SOCK_FAMILY_VALUE_UNIX = \"unix\" as const;\n\n/**\n * Deprecated, use `network.local.address`.\n *\n * @example /var/my.sock\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.local.address`.\n */\nexport const ATTR_NET_SOCK_HOST_ADDR = 'net.sock.host.addr' as const;\n\n/**\n * Deprecated, use `network.local.port`.\n *\n * @example 8080\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.local.port`.\n */\nexport const ATTR_NET_SOCK_HOST_PORT = 'net.sock.host.port' as const;\n\n/**\n * Deprecated, use `network.peer.address`.\n *\n * @example ***********\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.peer.address`.\n */\nexport const ATTR_NET_SOCK_PEER_ADDR = 'net.sock.peer.addr' as const;\n\n/**\n * Deprecated, no replacement at this time.\n *\n * @example /var/my.sock\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Removed. No replacement at this time.\n */\nexport const ATTR_NET_SOCK_PEER_NAME = 'net.sock.peer.name' as const;\n\n/**\n * Deprecated, use `network.peer.port`.\n *\n * @example 65531\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.peer.port`.\n */\nexport const ATTR_NET_SOCK_PEER_PORT = 'net.sock.peer.port' as const;\n\n/**\n * Deprecated, use `network.transport`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.transport`.\n */\nexport const ATTR_NET_TRANSPORT = 'net.transport' as const;\n\n/**\n * Enum value \"inproc\" for attribute {@link ATTR_NET_TRANSPORT}.\n */\nexport const NET_TRANSPORT_VALUE_INPROC = \"inproc\" as const;\n\n/**\n * Enum value \"ip_tcp\" for attribute {@link ATTR_NET_TRANSPORT}.\n */\nexport const NET_TRANSPORT_VALUE_IP_TCP = \"ip_tcp\" as const;\n\n/**\n * Enum value \"ip_udp\" for attribute {@link ATTR_NET_TRANSPORT}.\n */\nexport const NET_TRANSPORT_VALUE_IP_UDP = \"ip_udp\" as const;\n\n/**\n * Enum value \"other\" for attribute {@link ATTR_NET_TRANSPORT}.\n */\nexport const NET_TRANSPORT_VALUE_OTHER = \"other\" as const;\n\n/**\n * Enum value \"pipe\" for attribute {@link ATTR_NET_TRANSPORT}.\n */\nexport const NET_TRANSPORT_VALUE_PIPE = \"pipe\" as const;\n\n/**\n * The ISO 3166-1 alpha-2 2-character country code associated with the mobile carrier network.\n *\n * @example \"DE\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_CARRIER_ICC = 'network.carrier.icc' as const;\n\n/**\n * The mobile carrier country code.\n *\n * @example \"310\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_CARRIER_MCC = 'network.carrier.mcc' as const;\n\n/**\n * The mobile carrier network code.\n *\n * @example \"001\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_CARRIER_MNC = 'network.carrier.mnc' as const;\n\n/**\n * The name of the mobile carrier.\n *\n * @example \"sprint\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_CARRIER_NAME = 'network.carrier.name' as const;\n\n/**\n * The state of network connection\n *\n * @example close_wait\n *\n * @note Connection states are defined as part of the [rfc9293](https://datatracker.ietf.org/doc/html/rfc9293#section-3.3.2)\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_CONNECTION_STATE = 'network.connection.state' as const;\n\n/**\n * Enum value \"close_wait\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_CLOSE_WAIT = \"close_wait\" as const;\n\n/**\n * Enum value \"closed\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_CLOSED = \"closed\" as const;\n\n/**\n * Enum value \"closing\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_CLOSING = \"closing\" as const;\n\n/**\n * Enum value \"established\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_ESTABLISHED = \"established\" as const;\n\n/**\n * Enum value \"fin_wait_1\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_FIN_WAIT_1 = \"fin_wait_1\" as const;\n\n/**\n * Enum value \"fin_wait_2\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_FIN_WAIT_2 = \"fin_wait_2\" as const;\n\n/**\n * Enum value \"last_ack\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_LAST_ACK = \"last_ack\" as const;\n\n/**\n * Enum value \"listen\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_LISTEN = \"listen\" as const;\n\n/**\n * Enum value \"syn_received\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_SYN_RECEIVED = \"syn_received\" as const;\n\n/**\n * Enum value \"syn_sent\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_SYN_SENT = \"syn_sent\" as const;\n\n/**\n * Enum value \"time_wait\" for attribute {@link ATTR_NETWORK_CONNECTION_STATE}.\n */\nexport const NETWORK_CONNECTION_STATE_VALUE_TIME_WAIT = \"time_wait\" as const;\n\n/**\n * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.\n *\n * @example \"LTE\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_CONNECTION_SUBTYPE = 'network.connection.subtype' as const;\n\n/**\n * Enum value \"cdma\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA = \"cdma\" as const;\n\n/**\n * Enum value \"cdma2000_1xrtt\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA2000_1XRTT = \"cdma2000_1xrtt\" as const;\n\n/**\n * Enum value \"edge\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_EDGE = \"edge\" as const;\n\n/**\n * Enum value \"ehrpd\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_EHRPD = \"ehrpd\" as const;\n\n/**\n * Enum value \"evdo_0\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_0 = \"evdo_0\" as const;\n\n/**\n * Enum value \"evdo_a\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_A = \"evdo_a\" as const;\n\n/**\n * Enum value \"evdo_b\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_B = \"evdo_b\" as const;\n\n/**\n * Enum value \"gprs\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_GPRS = \"gprs\" as const;\n\n/**\n * Enum value \"gsm\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_GSM = \"gsm\" as const;\n\n/**\n * Enum value \"hsdpa\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_HSDPA = \"hsdpa\" as const;\n\n/**\n * Enum value \"hspa\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_HSPA = \"hspa\" as const;\n\n/**\n * Enum value \"hspap\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_HSPAP = \"hspap\" as const;\n\n/**\n * Enum value \"hsupa\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_HSUPA = \"hsupa\" as const;\n\n/**\n * Enum value \"iden\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_IDEN = \"iden\" as const;\n\n/**\n * Enum value \"iwlan\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_IWLAN = \"iwlan\" as const;\n\n/**\n * Enum value \"lte\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_LTE = \"lte\" as const;\n\n/**\n * Enum value \"lte_ca\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_LTE_CA = \"lte_ca\" as const;\n\n/**\n * Enum value \"nr\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_NR = \"nr\" as const;\n\n/**\n * Enum value \"nrnsa\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_NRNSA = \"nrnsa\" as const;\n\n/**\n * Enum value \"td_scdma\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_TD_SCDMA = \"td_scdma\" as const;\n\n/**\n * Enum value \"umts\" for attribute {@link ATTR_NETWORK_CONNECTION_SUBTYPE}.\n */\nexport const NETWORK_CONNECTION_SUBTYPE_VALUE_UMTS = \"umts\" as const;\n\n/**\n * The internet connection type.\n *\n * @example \"wifi\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_CONNECTION_TYPE = 'network.connection.type' as const;\n\n/**\n * Enum value \"cell\" for attribute {@link ATTR_NETWORK_CONNECTION_TYPE}.\n */\nexport const NETWORK_CONNECTION_TYPE_VALUE_CELL = \"cell\" as const;\n\n/**\n * Enum value \"unavailable\" for attribute {@link ATTR_NETWORK_CONNECTION_TYPE}.\n */\nexport const NETWORK_CONNECTION_TYPE_VALUE_UNAVAILABLE = \"unavailable\" as const;\n\n/**\n * Enum value \"unknown\" for attribute {@link ATTR_NETWORK_CONNECTION_TYPE}.\n */\nexport const NETWORK_CONNECTION_TYPE_VALUE_UNKNOWN = \"unknown\" as const;\n\n/**\n * Enum value \"wifi\" for attribute {@link ATTR_NETWORK_CONNECTION_TYPE}.\n */\nexport const NETWORK_CONNECTION_TYPE_VALUE_WIFI = \"wifi\" as const;\n\n/**\n * Enum value \"wired\" for attribute {@link ATTR_NETWORK_CONNECTION_TYPE}.\n */\nexport const NETWORK_CONNECTION_TYPE_VALUE_WIRED = \"wired\" as const;\n\n/**\n * The network interface name.\n *\n * @example lo\n * @example eth0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_INTERFACE_NAME = 'network.interface.name' as const;\n\n/**\n * The network IO operation direction.\n *\n * @example transmit\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NETWORK_IO_DIRECTION = 'network.io.direction' as const;\n\n/**\n * Enum value \"receive\" for attribute {@link ATTR_NETWORK_IO_DIRECTION}.\n */\nexport const NETWORK_IO_DIRECTION_VALUE_RECEIVE = \"receive\" as const;\n\n/**\n * Enum value \"transmit\" for attribute {@link ATTR_NETWORK_IO_DIRECTION}.\n */\nexport const NETWORK_IO_DIRECTION_VALUE_TRANSMIT = \"transmit\" as const;\n\n/**\n * The state of event loop time.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_NODEJS_EVENTLOOP_STATE = 'nodejs.eventloop.state' as const;\n\n/**\n * Enum value \"active\" for attribute {@link ATTR_NODEJS_EVENTLOOP_STATE}.\n */\nexport const NODEJS_EVENTLOOP_STATE_VALUE_ACTIVE = \"active\" as const;\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_NODEJS_EVENTLOOP_STATE}.\n */\nexport const NODEJS_EVENTLOOP_STATE_VALUE_IDLE = \"idle\" as const;\n\n/**\n * The digest of the OCI image manifest. For container images specifically is the digest by which the container image is known.\n *\n * @example sha256:e4ca62c0d62f3e886e684806dfe9d4e0cda60d54986898173c1083856cfda0f4\n *\n * @note Follows [OCI Image Manifest Specification](https://github.com/opencontainers/image-spec/blob/main/manifest.md), and specifically the [Digest property](https://github.com/opencontainers/image-spec/blob/main/descriptor.md#digests).\n * An example can be found in [Example Image Manifest](https://github.com/opencontainers/image-spec/blob/main/manifest.md#example-image-manifest).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OCI_MANIFEST_DIGEST = 'oci.manifest.digest' as const;\n\n/**\n * Parent-child Reference type\n *\n * @note The causal relationship between a child Span and a parent Span.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OPENTRACING_REF_TYPE = 'opentracing.ref_type' as const;\n\n/**\n * Enum value \"child_of\" for attribute {@link ATTR_OPENTRACING_REF_TYPE}.\n */\nexport const OPENTRACING_REF_TYPE_VALUE_CHILD_OF = \"child_of\" as const;\n\n/**\n * Enum value \"follows_from\" for attribute {@link ATTR_OPENTRACING_REF_TYPE}.\n */\nexport const OPENTRACING_REF_TYPE_VALUE_FOLLOWS_FROM = \"follows_from\" as const;\n\n/**\n * Unique identifier for a particular build or compilation of the operating system.\n *\n * @example TQ3C.230805.001.B2\n * @example 20E247\n * @example 22621\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OS_BUILD_ID = 'os.build_id' as const;\n\n/**\n * Human readable (not intended to be parsed) OS version information, like e.g. reported by `ver` or `lsb_release -a` commands.\n *\n * @example Microsoft Windows [Version 10.0.18363.778]\n * @example Ubuntu 18.04.1 LTS\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OS_DESCRIPTION = 'os.description' as const;\n\n/**\n * Human readable operating system name.\n *\n * @example iOS\n * @example Android\n * @example Ubuntu\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OS_NAME = 'os.name' as const;\n\n/**\n * The operating system type.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OS_TYPE = 'os.type' as const;\n\n/**\n * Enum value \"aix\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_AIX = \"aix\" as const;\n\n/**\n * Enum value \"darwin\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_DARWIN = \"darwin\" as const;\n\n/**\n * Enum value \"dragonflybsd\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_DRAGONFLYBSD = \"dragonflybsd\" as const;\n\n/**\n * Enum value \"freebsd\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_FREEBSD = \"freebsd\" as const;\n\n/**\n * Enum value \"hpux\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_HPUX = \"hpux\" as const;\n\n/**\n * Enum value \"linux\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_LINUX = \"linux\" as const;\n\n/**\n * Enum value \"netbsd\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_NETBSD = \"netbsd\" as const;\n\n/**\n * Enum value \"openbsd\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_OPENBSD = \"openbsd\" as const;\n\n/**\n * Enum value \"solaris\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_SOLARIS = \"solaris\" as const;\n\n/**\n * Enum value \"windows\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_WINDOWS = \"windows\" as const;\n\n/**\n * Enum value \"z_os\" for attribute {@link ATTR_OS_TYPE}.\n */\nexport const OS_TYPE_VALUE_Z_OS = \"z_os\" as const;\n\n/**\n * The version string of the operating system as defined in [Version Attributes](/docs/resource/README.md#version-attributes).\n *\n * @example 14.2.1\n * @example 18.04.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OS_VERSION = 'os.version' as const;\n\n/**\n * A name uniquely identifying the instance of the OpenTelemetry component within its containing SDK instance.\n *\n * @example otlp_grpc_span_exporter/0\n * @example custom-name\n *\n * @note Implementations **SHOULD** ensure a low cardinality for this attribute, even across application or SDK restarts.\n * E.g. implementations **MUST NOT** use UUIDs as values for this attribute.\n *\n * Implementations **MAY** achieve these goals by following a `<otel.component.type>/<instance-counter>` pattern, e.g. `batching_span_processor/0`.\n * Hereby `otel.component.type` refers to the corresponding attribute value of the component.\n *\n * The value of `instance-counter` **MAY** be automatically assigned by the component and uniqueness within the enclosing SDK instance **MUST** be guaranteed.\n * For example, `<instance-counter>` **MAY** be implemented by using a monotonically increasing counter (starting with `0`), which is incremented every time an\n * instance of the given component type is started.\n *\n * With this implementation, for example the first Batching Span Processor would have `batching_span_processor/0`\n * as `otel.component.name`, the second one `batching_span_processor/1` and so on.\n * These values will therefore be reused in the case of an application restart.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OTEL_COMPONENT_NAME = 'otel.component.name' as const;\n\n/**\n * A name identifying the type of the OpenTelemetry component.\n *\n * @example batching_span_processor\n * @example com.example.MySpanExporter\n *\n * @note If none of the standardized values apply, implementations **SHOULD** use the language-defined name of the type.\n * E.g. for Java the fully qualified classname **SHOULD** be used in this case.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OTEL_COMPONENT_TYPE = 'otel.component.type' as const;\n\n/**\n * Enum value \"batching_log_processor\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_BATCHING_LOG_PROCESSOR = \"batching_log_processor\" as const;\n\n/**\n * Enum value \"batching_span_processor\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_BATCHING_SPAN_PROCESSOR = \"batching_span_processor\" as const;\n\n/**\n * Enum value \"otlp_grpc_log_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_GRPC_LOG_EXPORTER = \"otlp_grpc_log_exporter\" as const;\n\n/**\n * Enum value \"otlp_grpc_metric_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_GRPC_METRIC_EXPORTER = \"otlp_grpc_metric_exporter\" as const;\n\n/**\n * Enum value \"otlp_grpc_span_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_GRPC_SPAN_EXPORTER = \"otlp_grpc_span_exporter\" as const;\n\n/**\n * Enum value \"otlp_http_json_log_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_HTTP_JSON_LOG_EXPORTER = \"otlp_http_json_log_exporter\" as const;\n\n/**\n * Enum value \"otlp_http_json_metric_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_HTTP_JSON_METRIC_EXPORTER = \"otlp_http_json_metric_exporter\" as const;\n\n/**\n * Enum value \"otlp_http_json_span_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_HTTP_JSON_SPAN_EXPORTER = \"otlp_http_json_span_exporter\" as const;\n\n/**\n * Enum value \"otlp_http_log_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_HTTP_LOG_EXPORTER = \"otlp_http_log_exporter\" as const;\n\n/**\n * Enum value \"otlp_http_metric_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_HTTP_METRIC_EXPORTER = \"otlp_http_metric_exporter\" as const;\n\n/**\n * Enum value \"otlp_http_span_exporter\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_OTLP_HTTP_SPAN_EXPORTER = \"otlp_http_span_exporter\" as const;\n\n/**\n * Enum value \"periodic_metric_reader\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_PERIODIC_METRIC_READER = \"periodic_metric_reader\" as const;\n\n/**\n * Enum value \"simple_log_processor\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_SIMPLE_LOG_PROCESSOR = \"simple_log_processor\" as const;\n\n/**\n * Enum value \"simple_span_processor\" for attribute {@link ATTR_OTEL_COMPONENT_TYPE}.\n */\nexport const OTEL_COMPONENT_TYPE_VALUE_SIMPLE_SPAN_PROCESSOR = \"simple_span_processor\" as const;\n\n/**\n * Deprecated. Use the `otel.scope.name` attribute\n *\n * @example io.opentelemetry.contrib.mongodb\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `otel.scope.name`.\n */\nexport const ATTR_OTEL_LIBRARY_NAME = 'otel.library.name' as const;\n\n/**\n * Deprecated. Use the `otel.scope.version` attribute.\n *\n * @example 1.0.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `otel.scope.version`.\n */\nexport const ATTR_OTEL_LIBRARY_VERSION = 'otel.library.version' as const;\n\n/**\n * The result value of the sampler for this span\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_OTEL_SPAN_SAMPLING_RESULT = 'otel.span.sampling_result' as const;\n\n/**\n * Enum value \"DROP\" for attribute {@link ATTR_OTEL_SPAN_SAMPLING_RESULT}.\n */\nexport const OTEL_SPAN_SAMPLING_RESULT_VALUE_DROP = \"DROP\" as const;\n\n/**\n * Enum value \"RECORD_AND_SAMPLE\" for attribute {@link ATTR_OTEL_SPAN_SAMPLING_RESULT}.\n */\nexport const OTEL_SPAN_SAMPLING_RESULT_VALUE_RECORD_AND_SAMPLE = \"RECORD_AND_SAMPLE\" as const;\n\n/**\n * Enum value \"RECORD_ONLY\" for attribute {@link ATTR_OTEL_SPAN_SAMPLING_RESULT}.\n */\nexport const OTEL_SPAN_SAMPLING_RESULT_VALUE_RECORD_ONLY = \"RECORD_ONLY\" as const;\n\n/**\n * The [`service.name`](/docs/resource/README.md#service) of the remote service. **SHOULD** be equal to the actual `service.name` resource attribute of the remote service if any.\n *\n * @example \"AuthTokenCache\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PEER_SERVICE = 'peer.service' as const;\n\n/**\n * Deprecated, use `db.client.connection.pool.name` instead.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.pool.name`.\n */\nexport const ATTR_POOL_NAME = 'pool.name' as const;\n\n/**\n * Length of the process.command_args array\n *\n * @example 4\n *\n * @note This field can be useful for querying or performing bucket analysis on how many arguments were provided to start a process. More arguments may be an indication of suspicious activity.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_ARGS_COUNT = 'process.args_count' as const;\n\n/**\n * The command used to launch the process (i.e. the command name). On Linux based systems, can be set to the zeroth string in `proc/[pid]/cmdline`. On Windows, can be set to the first parameter extracted from `GetCommandLineW`.\n *\n * @example cmd/otelcol\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_COMMAND = 'process.command' as const;\n\n/**\n * All the command arguments (including the command/executable itself) as received by the process. On Linux-based systems (and some other Unixoid systems supporting procfs), can be set according to the list of null-delimited strings extracted from `proc/[pid]/cmdline`. For libc-based executables, this would be the full argv vector passed to `main`. **SHOULD NOT** be collected by default unless there is sanitization that excludes sensitive data.\n *\n * @example [\"cmd/otecol\", \"--config=config.yaml\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_COMMAND_ARGS = 'process.command_args' as const;\n\n/**\n * The full command used to launch the process as a single string representing the full command. On Windows, can be set to the result of `GetCommandLineW`. Do not set this if you have to assemble it just for monitoring; use `process.command_args` instead. **SHOULD NOT** be collected by default unless there is sanitization that excludes sensitive data.\n *\n * @example C:\\\\cmd\\\\otecol --config=\"my directory\\\\config.yaml\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_COMMAND_LINE = 'process.command_line' as const;\n\n/**\n * Specifies whether the context switches for this data point were voluntary or involuntary.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_CONTEXT_SWITCH_TYPE = 'process.context_switch_type' as const;\n\n/**\n * Enum value \"involuntary\" for attribute {@link ATTR_PROCESS_CONTEXT_SWITCH_TYPE}.\n */\nexport const PROCESS_CONTEXT_SWITCH_TYPE_VALUE_INVOLUNTARY = \"involuntary\" as const;\n\n/**\n * Enum value \"voluntary\" for attribute {@link ATTR_PROCESS_CONTEXT_SWITCH_TYPE}.\n */\nexport const PROCESS_CONTEXT_SWITCH_TYPE_VALUE_VOLUNTARY = \"voluntary\" as const;\n\n/**\n * Deprecated, use `cpu.mode` instead.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cpu.mode`.\n */\nexport const ATTR_PROCESS_CPU_STATE = 'process.cpu.state' as const;\n\n/**\n * Enum value \"system\" for attribute {@link ATTR_PROCESS_CPU_STATE}.\n */\nexport const PROCESS_CPU_STATE_VALUE_SYSTEM = \"system\" as const;\n\n/**\n * Enum value \"user\" for attribute {@link ATTR_PROCESS_CPU_STATE}.\n */\nexport const PROCESS_CPU_STATE_VALUE_USER = \"user\" as const;\n\n/**\n * Enum value \"wait\" for attribute {@link ATTR_PROCESS_CPU_STATE}.\n */\nexport const PROCESS_CPU_STATE_VALUE_WAIT = \"wait\" as const;\n\n/**\n * The date and time the process was created, in ISO 8601 format.\n *\n * @example 2023-11-21T09:25:34.853Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_CREATION_TIME = 'process.creation.time' as const;\n\n/**\n * Process environment variables, <key> being the environment variable name, the value being the environment variable value.\n *\n * @example ubuntu\n * @example /usr/local/bin:/usr/bin\n *\n * @note Examples:\n *\n *   - an environment variable `USER` with value `\"ubuntu\"` **SHOULD** be recorded\n *     as the `process.environment_variable.USER` attribute with value `\"ubuntu\"`.\n *   - an environment variable `PATH` with value `\"/usr/local/bin:/usr/bin\"`\n *     **SHOULD** be recorded as the `process.environment_variable.PATH` attribute\n *     with value `\"/usr/local/bin:/usr/bin\"`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_ENVIRONMENT_VARIABLE = (key: string) => `process.environment_variable.${key}`;\n\n/**\n * The GNU build ID as found in the `.note.gnu.build-id` ELF section (hex string).\n *\n * @example c89b11207f6479603b0d49bf291c092c2b719293\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_BUILD_ID_GNU = 'process.executable.build_id.gnu' as const;\n\n/**\n * The Go build ID as retrieved by `go tool buildid <go executable>`.\n *\n * @example foh3mEXu7BLZjsN9pOwG/kATcXlYVCDEFouRMQed_/WwRFB1hPo9LBkekthSPG/x8hMC8emW2cCjXD0_1aY\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_BUILD_ID_GO = 'process.executable.build_id.go' as const;\n\n/**\n * Profiling specific build ID for executables. See the OTel specification for Profiles for more information.\n *\n * @example 600DCAFE4A110000F2BF38C493F5FB92\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_BUILD_ID_HTLHASH = 'process.executable.build_id.htlhash' as const;\n\n/**\n * \"Deprecated, use `process.executable.build_id.htlhash` instead.\"\n *\n * @example 600DCAFE4A110000F2BF38C493F5FB92\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `process.executable.build_id.htlhash`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_BUILD_ID_PROFILING = 'process.executable.build_id.profiling' as const;\n\n/**\n * The name of the process executable. On Linux based systems, this **SHOULD** be set to the base name of the target of `/proc/[pid]/exe`. On Windows, this **SHOULD** be set to the base name of `GetProcessImageFileNameW`.\n *\n * @example otelcol\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_NAME = 'process.executable.name' as const;\n\n/**\n * The full path to the process executable. On Linux based systems, can be set to the target of `proc/[pid]/exe`. On Windows, can be set to the result of `GetProcessImageFileNameW`.\n *\n * @example /usr/bin/cmd/otelcol\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXECUTABLE_PATH = 'process.executable.path' as const;\n\n/**\n * The exit code of the process.\n *\n * @example 127\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXIT_CODE = 'process.exit.code' as const;\n\n/**\n * The date and time the process exited, in ISO 8601 format.\n *\n * @example 2023-11-21T09:26:12.315Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_EXIT_TIME = 'process.exit.time' as const;\n\n/**\n * The PID of the process's group leader. This is also the process group ID (PGID) of the process.\n *\n * @example 23\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_GROUP_LEADER_PID = 'process.group_leader.pid' as const;\n\n/**\n * Whether the process is connected to an interactive shell.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_INTERACTIVE = 'process.interactive' as const;\n\n/**\n * The control group associated with the process.\n *\n * @example 1:name=systemd:/user.slice/user-1000.slice/session-3.scope\n * @example 0::/user.slice/user-1000.slice/<EMAIL>/tmux-spawn-0267755b-4639-4a27-90ed-f19f88e53748.scope\n *\n * @note Control groups (cgroups) are a kernel feature used to organize and manage process resources. This attribute provides the path(s) to the cgroup(s) associated with the process, which should match the contents of the [/proc/[PID]/cgroup](https://man7.org/linux/man-pages/man7/cgroups.7.html) file.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_LINUX_CGROUP = 'process.linux.cgroup' as const;\n\n/**\n * The username of the user that owns the process.\n *\n * @example root\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_OWNER = 'process.owner' as const;\n\n/**\n * The type of page fault for this data point. Type `major` is for major/hard page faults, and `minor` is for minor/soft page faults.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_PAGING_FAULT_TYPE = 'process.paging.fault_type' as const;\n\n/**\n * Enum value \"major\" for attribute {@link ATTR_PROCESS_PAGING_FAULT_TYPE}.\n */\nexport const PROCESS_PAGING_FAULT_TYPE_VALUE_MAJOR = \"major\" as const;\n\n/**\n * Enum value \"minor\" for attribute {@link ATTR_PROCESS_PAGING_FAULT_TYPE}.\n */\nexport const PROCESS_PAGING_FAULT_TYPE_VALUE_MINOR = \"minor\" as const;\n\n/**\n * Parent Process identifier (PPID).\n *\n * @example 111\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_PARENT_PID = 'process.parent_pid' as const;\n\n/**\n * Process identifier (PID).\n *\n * @example 1234\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_PID = 'process.pid' as const;\n\n/**\n * The real user ID (RUID) of the process.\n *\n * @example 1000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_REAL_USER_ID = 'process.real_user.id' as const;\n\n/**\n * The username of the real user of the process.\n *\n * @example operator\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_REAL_USER_NAME = 'process.real_user.name' as const;\n\n/**\n * An additional description about the runtime of the process, for example a specific vendor customization of the runtime environment.\n *\n * @example \"Eclipse OpenJ9 Eclipse OpenJ9 VM openj9-0.21.0\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description' as const;\n\n/**\n * The name of the runtime of this process.\n *\n * @example OpenJDK Runtime Environment\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_RUNTIME_NAME = 'process.runtime.name' as const;\n\n/**\n * The version of the runtime of this process, as returned by the runtime without modification.\n *\n * @example \"14.0.2\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_RUNTIME_VERSION = 'process.runtime.version' as const;\n\n/**\n * The saved user ID (SUID) of the process.\n *\n * @example 1002\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_SAVED_USER_ID = 'process.saved_user.id' as const;\n\n/**\n * The username of the saved user.\n *\n * @example operator\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_SAVED_USER_NAME = 'process.saved_user.name' as const;\n\n/**\n * The PID of the process's session leader. This is also the session ID (SID) of the process.\n *\n * @example 14\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_SESSION_LEADER_PID = 'process.session_leader.pid' as const;\n\n/**\n * Process title (proctitle)\n *\n * @example cat /etc/hostname\n * @example xfce4-session\n * @example bash\n *\n * @note In many Unix-like systems, process title (proctitle), is the string that represents the name or command line of a running process, displayed by system monitoring tools like ps, top, and htop.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_TITLE = 'process.title' as const;\n\n/**\n * The effective user ID (EUID) of the process.\n *\n * @example 1001\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_USER_ID = 'process.user.id' as const;\n\n/**\n * The username of the effective user of the process.\n *\n * @example root\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_USER_NAME = 'process.user.name' as const;\n\n/**\n * Virtual process identifier.\n *\n * @example 12\n *\n * @note The process ID within a PID namespace. This is not necessarily unique across all processes on the host but it is unique within the process namespace that the process exists within.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_VPID = 'process.vpid' as const;\n\n/**\n * The working directory of the process.\n *\n * @example /root\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROCESS_WORKING_DIRECTORY = 'process.working_directory' as const;\n\n/**\n * Describes the interpreter or compiler of a single frame.\n *\n * @example cpython\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_PROFILE_FRAME_TYPE = 'profile.frame.type' as const;\n\n/**\n * Enum value \"beam\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_BEAM = \"beam\" as const;\n\n/**\n * Enum value \"cpython\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_CPYTHON = \"cpython\" as const;\n\n/**\n * Enum value \"dotnet\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_DOTNET = \"dotnet\" as const;\n\n/**\n * Enum value \"go\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_GO = \"go\" as const;\n\n/**\n * Enum value \"jvm\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_JVM = \"jvm\" as const;\n\n/**\n * Enum value \"kernel\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_KERNEL = \"kernel\" as const;\n\n/**\n * Enum value \"native\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_NATIVE = \"native\" as const;\n\n/**\n * Enum value \"perl\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_PERL = \"perl\" as const;\n\n/**\n * Enum value \"php\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_PHP = \"php\" as const;\n\n/**\n * Enum value \"ruby\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_RUBY = \"ruby\" as const;\n\n/**\n * Enum value \"rust\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_RUST = \"rust\" as const;\n\n/**\n * Enum value \"v8js\" for attribute {@link ATTR_PROFILE_FRAME_TYPE}.\n */\nexport const PROFILE_FRAME_TYPE_VALUE_V8JS = \"v8js\" as const;\n\n/**\n * The [error codes](https://connectrpc.com//docs/protocol/#error-codes) of the Connect request. Error codes are always string values.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_CONNECT_RPC_ERROR_CODE = 'rpc.connect_rpc.error_code' as const;\n\n/**\n * Enum value \"aborted\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_ABORTED = \"aborted\" as const;\n\n/**\n * Enum value \"already_exists\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_ALREADY_EXISTS = \"already_exists\" as const;\n\n/**\n * Enum value \"cancelled\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_CANCELLED = \"cancelled\" as const;\n\n/**\n * Enum value \"data_loss\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_DATA_LOSS = \"data_loss\" as const;\n\n/**\n * Enum value \"deadline_exceeded\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_DEADLINE_EXCEEDED = \"deadline_exceeded\" as const;\n\n/**\n * Enum value \"failed_precondition\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_FAILED_PRECONDITION = \"failed_precondition\" as const;\n\n/**\n * Enum value \"internal\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_INTERNAL = \"internal\" as const;\n\n/**\n * Enum value \"invalid_argument\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_INVALID_ARGUMENT = \"invalid_argument\" as const;\n\n/**\n * Enum value \"not_found\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_NOT_FOUND = \"not_found\" as const;\n\n/**\n * Enum value \"out_of_range\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_OUT_OF_RANGE = \"out_of_range\" as const;\n\n/**\n * Enum value \"permission_denied\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_PERMISSION_DENIED = \"permission_denied\" as const;\n\n/**\n * Enum value \"resource_exhausted\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_RESOURCE_EXHAUSTED = \"resource_exhausted\" as const;\n\n/**\n * Enum value \"unauthenticated\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAUTHENTICATED = \"unauthenticated\" as const;\n\n/**\n * Enum value \"unavailable\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAVAILABLE = \"unavailable\" as const;\n\n/**\n * Enum value \"unimplemented\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNIMPLEMENTED = \"unimplemented\" as const;\n\n/**\n * Enum value \"unknown\" for attribute {@link ATTR_RPC_CONNECT_RPC_ERROR_CODE}.\n */\nexport const RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNKNOWN = \"unknown\" as const;\n\n/**\n * Connect request metadata, `<key>` being the normalized Connect Metadata key (lowercase), the value being the metadata values.\n *\n * @example [\"1.2.3.4\", \"1.2.3.5\"]\n *\n * @note Instrumentations **SHOULD** require an explicit configuration of which metadata values are to be captured.\n * Including all request metadata values can be a security risk - explicit configuration helps avoid leaking sensitive information.\n *\n * For example, a property `my-custom-key` with value `[\"1.2.3.4\", \"1.2.3.5\"]` **SHOULD** be recorded as\n * the `rpc.connect_rpc.request.metadata.my-custom-key` attribute with value `[\"1.2.3.4\", \"1.2.3.5\"]`\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_CONNECT_RPC_REQUEST_METADATA = (key: string) => `rpc.connect_rpc.request.metadata.${key}`;\n\n/**\n * Connect response metadata, `<key>` being the normalized Connect Metadata key (lowercase), the value being the metadata values.\n *\n * @example [\"attribute_value\"]\n *\n * @note Instrumentations **SHOULD** require an explicit configuration of which metadata values are to be captured.\n * Including all response metadata values can be a security risk - explicit configuration helps avoid leaking sensitive information.\n *\n * For example, a property `my-custom-key` with value `\"attribute_value\"` **SHOULD** be recorded as\n * the `rpc.connect_rpc.response.metadata.my-custom-key` attribute with value `[\"attribute_value\"]`\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_CONNECT_RPC_RESPONSE_METADATA = (key: string) => `rpc.connect_rpc.response.metadata.${key}`;\n\n/**\n * gRPC request metadata, `<key>` being the normalized gRPC Metadata key (lowercase), the value being the metadata values.\n *\n * @example [\"1.2.3.4\", \"1.2.3.5\"]\n *\n * @note Instrumentations **SHOULD** require an explicit configuration of which metadata values are to be captured.\n * Including all request metadata values can be a security risk - explicit configuration helps avoid leaking sensitive information.\n *\n * For example, a property `my-custom-key` with value `[\"1.2.3.4\", \"1.2.3.5\"]` **SHOULD** be recorded as\n * `rpc.grpc.request.metadata.my-custom-key` attribute with value `[\"1.2.3.4\", \"1.2.3.5\"]`\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_GRPC_REQUEST_METADATA = (key: string) => `rpc.grpc.request.metadata.${key}`;\n\n/**\n * gRPC response metadata, `<key>` being the normalized gRPC Metadata key (lowercase), the value being the metadata values.\n *\n * @example [\"attribute_value\"]\n *\n * @note Instrumentations **SHOULD** require an explicit configuration of which metadata values are to be captured.\n * Including all response metadata values can be a security risk - explicit configuration helps avoid leaking sensitive information.\n *\n * For example, a property `my-custom-key` with value `[\"attribute_value\"]` **SHOULD** be recorded as\n * the `rpc.grpc.response.metadata.my-custom-key` attribute with value `[\"attribute_value\"]`\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_GRPC_RESPONSE_METADATA = (key: string) => `rpc.grpc.response.metadata.${key}`;\n\n/**\n * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_GRPC_STATUS_CODE = 'rpc.grpc.status_code' as const;\n\n/**\n * Enum value 0 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_OK = 0 as const;\n\n/**\n * Enum value 1 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_CANCELLED = 1 as const;\n\n/**\n * Enum value 2 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_UNKNOWN = 2 as const;\n\n/**\n * Enum value 3 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_INVALID_ARGUMENT = 3 as const;\n\n/**\n * Enum value 4 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_DEADLINE_EXCEEDED = 4 as const;\n\n/**\n * Enum value 5 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_NOT_FOUND = 5 as const;\n\n/**\n * Enum value 6 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_ALREADY_EXISTS = 6 as const;\n\n/**\n * Enum value 7 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_PERMISSION_DENIED = 7 as const;\n\n/**\n * Enum value 8 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_RESOURCE_EXHAUSTED = 8 as const;\n\n/**\n * Enum value 9 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_FAILED_PRECONDITION = 9 as const;\n\n/**\n * Enum value 10 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_ABORTED = 10 as const;\n\n/**\n * Enum value 11 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_OUT_OF_RANGE = 11 as const;\n\n/**\n * Enum value 12 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_UNIMPLEMENTED = 12 as const;\n\n/**\n * Enum value 13 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_INTERNAL = 13 as const;\n\n/**\n * Enum value 14 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_UNAVAILABLE = 14 as const;\n\n/**\n * Enum value 15 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_DATA_LOSS = 15 as const;\n\n/**\n * Enum value 16 for attribute {@link ATTR_RPC_GRPC_STATUS_CODE}.\n */\nexport const RPC_GRPC_STATUS_CODE_VALUE_UNAUTHENTICATED = 16 as const;\n\n/**\n * `error.code` property of response if it is an error response.\n *\n * @example -32700\n * @example 100\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_JSONRPC_ERROR_CODE = 'rpc.jsonrpc.error_code' as const;\n\n/**\n * `error.message` property of response if it is an error response.\n *\n * @example Parse error\n * @example User already exists\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_JSONRPC_ERROR_MESSAGE = 'rpc.jsonrpc.error_message' as const;\n\n/**\n * `id` property of request or response. Since protocol allows id to be int, string, `null` or missing (for notifications), value is expected to be cast to string for simplicity. Use empty string in case of `null` value. Omit entirely if this is a notification.\n *\n * @example 10\n * @example request-7\n * @example\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_JSONRPC_REQUEST_ID = 'rpc.jsonrpc.request_id' as const;\n\n/**\n * Protocol version as in `jsonrpc` property of request/response. Since JSON-RPC 1.0 doesn't specify this, the value can be omitted.\n *\n * @example 2.0\n * @example 1.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_JSONRPC_VERSION = 'rpc.jsonrpc.version' as const;\n\n/**\n * Compressed size of the message in bytes.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_MESSAGE_COMPRESSED_SIZE = 'rpc.message.compressed_size' as const;\n\n/**\n * **MUST** be calculated as two different counters starting from `1` one for sent messages and one for received message.\n *\n * @note This way we guarantee that the values will be consistent between different implementations.\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_MESSAGE_ID = 'rpc.message.id' as const;\n\n/**\n * Whether this is a received or sent message.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_MESSAGE_TYPE = 'rpc.message.type' as const;\n\n/**\n * Enum value \"RECEIVED\" for attribute {@link ATTR_RPC_MESSAGE_TYPE}.\n */\nexport const RPC_MESSAGE_TYPE_VALUE_RECEIVED = \"RECEIVED\" as const;\n\n/**\n * Enum value \"SENT\" for attribute {@link ATTR_RPC_MESSAGE_TYPE}.\n */\nexport const RPC_MESSAGE_TYPE_VALUE_SENT = \"SENT\" as const;\n\n/**\n * Uncompressed size of the message in bytes.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_MESSAGE_UNCOMPRESSED_SIZE = 'rpc.message.uncompressed_size' as const;\n\n/**\n * The name of the (logical) method being called, must be equal to the $method part in the span name.\n *\n * @example \"exampleMethod\"\n *\n * @note This is the logical name of the method from the RPC interface perspective, which can be different from the name of any implementing method/function. The `code.function.name` attribute may be used to store the latter (e.g., method actually executing the call on the server side, RPC client stub method on the client side).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_METHOD = 'rpc.method' as const;\n\n/**\n * The full (logical) name of the service being called, including its package name, if applicable.\n *\n * @example \"myservice.EchoService\"\n *\n * @note This is the logical name of the service from the RPC interface perspective, which can be different from the name of any implementing class. The `code.namespace` attribute may be used to store the latter (despite the attribute name, it may include a class name; e.g., class with method actually executing the call on the server side, RPC client stub class on the client side).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_SERVICE = 'rpc.service' as const;\n\n/**\n * A string identifying the remoting system. See below for a list of well-known identifiers.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_RPC_SYSTEM = 'rpc.system' as const;\n\n/**\n * Enum value \"apache_dubbo\" for attribute {@link ATTR_RPC_SYSTEM}.\n */\nexport const RPC_SYSTEM_VALUE_APACHE_DUBBO = \"apache_dubbo\" as const;\n\n/**\n * Enum value \"connect_rpc\" for attribute {@link ATTR_RPC_SYSTEM}.\n */\nexport const RPC_SYSTEM_VALUE_CONNECT_RPC = \"connect_rpc\" as const;\n\n/**\n * Enum value \"dotnet_wcf\" for attribute {@link ATTR_RPC_SYSTEM}.\n */\nexport const RPC_SYSTEM_VALUE_DOTNET_WCF = \"dotnet_wcf\" as const;\n\n/**\n * Enum value \"grpc\" for attribute {@link ATTR_RPC_SYSTEM}.\n */\nexport const RPC_SYSTEM_VALUE_GRPC = \"grpc\" as const;\n\n/**\n * Enum value \"java_rmi\" for attribute {@link ATTR_RPC_SYSTEM}.\n */\nexport const RPC_SYSTEM_VALUE_JAVA_RMI = \"java_rmi\" as const;\n\n/**\n * A categorization value keyword used by the entity using the rule for detection of this event\n *\n * @example Attempted Information Leak\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_CATEGORY = 'security_rule.category' as const;\n\n/**\n * The description of the rule generating the event.\n *\n * @example Block requests to public DNS over HTTPS / TLS protocols\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_DESCRIPTION = 'security_rule.description' as const;\n\n/**\n * Name of the license under which the rule used to generate this event is made available.\n *\n * @example Apache 2.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_LICENSE = 'security_rule.license' as const;\n\n/**\n * The name of the rule or signature generating the event.\n *\n * @example BLOCK_DNS_over_TLS\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_NAME = 'security_rule.name' as const;\n\n/**\n * Reference URL to additional information about the rule used to generate this event.\n *\n * @example https://en.wikipedia.org/wiki/DNS_over_TLS\n *\n * @note The URL can point to the vendor’s documentation about the rule. If that’s not available, it can also be a link to a more general page describing this type of alert.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_REFERENCE = 'security_rule.reference' as const;\n\n/**\n * Name of the ruleset, policy, group, or parent category in which the rule used to generate this event is a member.\n *\n * @example Standard_Protocol_Filters\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_RULESET_NAME = 'security_rule.ruleset.name' as const;\n\n/**\n * A rule ID that is unique within the scope of a set or group of agents, observers, or other entities using the rule for detection of this event.\n *\n * @example 550e8400-e29b-41d4-a716-************\n * @example 1100110011\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_UUID = 'security_rule.uuid' as const;\n\n/**\n * The version / revision of the rule being used for analysis.\n *\n * @example 1.0.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SECURITY_RULE_VERSION = 'security_rule.version' as const;\n\n/**\n * The string ID of the service instance.\n *\n * @example 627cc493-f310-47de-96bd-71410b7dec09\n *\n * @note **MUST** be unique for each instance of the same `service.namespace,service.name` pair (in other words\n * `service.namespace,service.name,service.instance.id` triplet **MUST** be globally unique). The ID helps to\n * distinguish instances of the same service that exist at the same time (e.g. instances of a horizontally scaled\n * service).\n *\n * Implementations, such as SDKs, are recommended to generate a random Version 1 or Version 4 [RFC\n * 4122](https://www.ietf.org/rfc/rfc4122.txt) UUID, but are free to use an inherent unique ID as the source of\n * this value if stability is desirable. In that case, the ID **SHOULD** be used as source of a UUID Version 5 and\n * **SHOULD** use the following UUID as the namespace: `4d63009a-8d0f-11ee-aad7-4c796ed8e320`.\n *\n * UUIDs are typically recommended, as only an opaque value for the purposes of identifying a service instance is\n * needed. Similar to what can be seen in the man page for the\n * [`/etc/machine-id`](https://www.freedesktop.org/software/systemd/man/latest/machine-id.html) file, the underlying\n * data, such as pod name and namespace should be treated as confidential, being the user's choice to expose it\n * or not via another resource attribute.\n *\n * For applications running behind an application server (like unicorn), we do not recommend using one identifier\n * for all processes participating in the application. Instead, it's recommended each division (e.g. a worker\n * thread in unicorn) to have its own instance.id.\n *\n * It's not recommended for a Collector to set `service.instance.id` if it can't unambiguously determine the\n * service instance that is generating that telemetry. For instance, creating an UUID based on `pod.name` will\n * likely be wrong, as the Collector might not know from which container within that pod the telemetry originated.\n * However, Collectors can set the `service.instance.id` if they can unambiguously determine the service instance\n * for that telemetry. This is typically the case for scraping receivers, as they know the target address and\n * port.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SERVICE_INSTANCE_ID = 'service.instance.id' as const;\n\n/**\n * A namespace for `service.name`.\n *\n * @example Shop\n *\n * @note A string value having a meaning that helps to distinguish a group of services, for example the team name that owns a group of services. `service.name` is expected to be unique within the same namespace. If `service.namespace` is not specified in the Resource then `service.name` is expected to be unique for all services that have no explicit namespace defined (so the empty/unspecified namespace is simply one more valid namespace). Zero-length namespace string is assumed equal to unspecified namespace.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SERVICE_NAMESPACE = 'service.namespace' as const;\n\n/**\n * A unique id to identify a session.\n *\n * @example \"00112233-4455-6677-8899-aabbccddeeff\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SESSION_ID = 'session.id' as const;\n\n/**\n * The previous `session.id` for this user, when known.\n *\n * @example \"00112233-4455-6677-8899-aabbccddeeff\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SESSION_PREVIOUS_ID = 'session.previous_id' as const;\n\n/**\n * Source address - domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n *\n * @example source.example.com\n * @example *********\n * @example /tmp/my.sock\n *\n * @note When observed from the destination side, and when communicating through an intermediary, `source.address` **SHOULD** represent the source address behind any intermediaries, for example proxies, if it's available.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SOURCE_ADDRESS = 'source.address' as const;\n\n/**\n * Source port number\n *\n * @example 3389\n * @example 2888\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SOURCE_PORT = 'source.port' as const;\n\n/**\n * Deprecated, use `db.client.connection.state` instead.\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `db.client.connection.state`.\n */\nexport const ATTR_STATE = 'state' as const;\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_STATE}.\n */\nexport const STATE_VALUE_IDLE = \"idle\" as const;\n\n/**\n * Enum value \"used\" for attribute {@link ATTR_STATE}.\n */\nexport const STATE_VALUE_USED = \"used\" as const;\n\n/**\n * Deprecated, use `cpu.logical_number` instead.\n *\n * @example 1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_CPU_LOGICAL_NUMBER = 'system.cpu.logical_number' as const;\n\n/**\n * Deprecated, use `cpu.mode` instead.\n *\n * @example idle\n * @example interrupt\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `cpu.mode`.\n */\nexport const ATTR_SYSTEM_CPU_STATE = 'system.cpu.state' as const;\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_SYSTEM_CPU_STATE}.\n */\nexport const SYSTEM_CPU_STATE_VALUE_IDLE = \"idle\" as const;\n\n/**\n * Enum value \"interrupt\" for attribute {@link ATTR_SYSTEM_CPU_STATE}.\n */\nexport const SYSTEM_CPU_STATE_VALUE_INTERRUPT = \"interrupt\" as const;\n\n/**\n * Enum value \"iowait\" for attribute {@link ATTR_SYSTEM_CPU_STATE}.\n */\nexport const SYSTEM_CPU_STATE_VALUE_IOWAIT = \"iowait\" as const;\n\n/**\n * Enum value \"nice\" for attribute {@link ATTR_SYSTEM_CPU_STATE}.\n */\nexport const SYSTEM_CPU_STATE_VALUE_NICE = \"nice\" as const;\n\n/**\n * Enum value \"steal\" for attribute {@link ATTR_SYSTEM_CPU_STATE}.\n */\nexport const SYSTEM_CPU_STATE_VALUE_STEAL = \"steal\" as const;\n\n/**\n * Enum value \"system\" for attribute {@link ATTR_SYSTEM_CPU_STATE}.\n */\nexport const SYSTEM_CPU_STATE_VALUE_SYSTEM = \"system\" as const;\n\n/**\n * Enum value \"user\" for attribute {@link ATTR_SYSTEM_CPU_STATE}.\n */\nexport const SYSTEM_CPU_STATE_VALUE_USER = \"user\" as const;\n\n/**\n * The device identifier\n *\n * @example (identifier)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_DEVICE = 'system.device' as const;\n\n/**\n * The filesystem mode\n *\n * @example rw, ro\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_FILESYSTEM_MODE = 'system.filesystem.mode' as const;\n\n/**\n * The filesystem mount path\n *\n * @example /mnt/data\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_FILESYSTEM_MOUNTPOINT = 'system.filesystem.mountpoint' as const;\n\n/**\n * The filesystem state\n *\n * @example used\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_FILESYSTEM_STATE = 'system.filesystem.state' as const;\n\n/**\n * Enum value \"free\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_STATE}.\n */\nexport const SYSTEM_FILESYSTEM_STATE_VALUE_FREE = \"free\" as const;\n\n/**\n * Enum value \"reserved\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_STATE}.\n */\nexport const SYSTEM_FILESYSTEM_STATE_VALUE_RESERVED = \"reserved\" as const;\n\n/**\n * Enum value \"used\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_STATE}.\n */\nexport const SYSTEM_FILESYSTEM_STATE_VALUE_USED = \"used\" as const;\n\n/**\n * The filesystem type\n *\n * @example ext4\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_FILESYSTEM_TYPE = 'system.filesystem.type' as const;\n\n/**\n * Enum value \"exfat\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_TYPE}.\n */\nexport const SYSTEM_FILESYSTEM_TYPE_VALUE_EXFAT = \"exfat\" as const;\n\n/**\n * Enum value \"ext4\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_TYPE}.\n */\nexport const SYSTEM_FILESYSTEM_TYPE_VALUE_EXT4 = \"ext4\" as const;\n\n/**\n * Enum value \"fat32\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_TYPE}.\n */\nexport const SYSTEM_FILESYSTEM_TYPE_VALUE_FAT32 = \"fat32\" as const;\n\n/**\n * Enum value \"hfsplus\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_TYPE}.\n */\nexport const SYSTEM_FILESYSTEM_TYPE_VALUE_HFSPLUS = \"hfsplus\" as const;\n\n/**\n * Enum value \"ntfs\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_TYPE}.\n */\nexport const SYSTEM_FILESYSTEM_TYPE_VALUE_NTFS = \"ntfs\" as const;\n\n/**\n * Enum value \"refs\" for attribute {@link ATTR_SYSTEM_FILESYSTEM_TYPE}.\n */\nexport const SYSTEM_FILESYSTEM_TYPE_VALUE_REFS = \"refs\" as const;\n\n/**\n * The memory state\n *\n * @example free\n * @example cached\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_MEMORY_STATE = 'system.memory.state' as const;\n\n/**\n * Enum value \"buffers\" for attribute {@link ATTR_SYSTEM_MEMORY_STATE}.\n */\nexport const SYSTEM_MEMORY_STATE_VALUE_BUFFERS = \"buffers\" as const;\n\n/**\n * Enum value \"cached\" for attribute {@link ATTR_SYSTEM_MEMORY_STATE}.\n */\nexport const SYSTEM_MEMORY_STATE_VALUE_CACHED = \"cached\" as const;\n\n/**\n * Enum value \"free\" for attribute {@link ATTR_SYSTEM_MEMORY_STATE}.\n */\nexport const SYSTEM_MEMORY_STATE_VALUE_FREE = \"free\" as const;\n\n/**\n * Enum value \"shared\" for attribute {@link ATTR_SYSTEM_MEMORY_STATE}.\n */\nexport const SYSTEM_MEMORY_STATE_VALUE_SHARED = \"shared\" as const;\n\n/**\n * Enum value \"used\" for attribute {@link ATTR_SYSTEM_MEMORY_STATE}.\n */\nexport const SYSTEM_MEMORY_STATE_VALUE_USED = \"used\" as const;\n\n/**\n * Deprecated, use `network.connection.state` instead.\n *\n * @example close_wait\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `network.connection.state`.\n */\nexport const ATTR_SYSTEM_NETWORK_STATE = 'system.network.state' as const;\n\n/**\n * Enum value \"close\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_CLOSE = \"close\" as const;\n\n/**\n * Enum value \"close_wait\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_CLOSE_WAIT = \"close_wait\" as const;\n\n/**\n * Enum value \"closing\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_CLOSING = \"closing\" as const;\n\n/**\n * Enum value \"delete\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_DELETE = \"delete\" as const;\n\n/**\n * Enum value \"established\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_ESTABLISHED = \"established\" as const;\n\n/**\n * Enum value \"fin_wait_1\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_1 = \"fin_wait_1\" as const;\n\n/**\n * Enum value \"fin_wait_2\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_2 = \"fin_wait_2\" as const;\n\n/**\n * Enum value \"last_ack\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_LAST_ACK = \"last_ack\" as const;\n\n/**\n * Enum value \"listen\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_LISTEN = \"listen\" as const;\n\n/**\n * Enum value \"syn_recv\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_SYN_RECV = \"syn_recv\" as const;\n\n/**\n * Enum value \"syn_sent\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_SYN_SENT = \"syn_sent\" as const;\n\n/**\n * Enum value \"time_wait\" for attribute {@link ATTR_SYSTEM_NETWORK_STATE}.\n */\nexport const SYSTEM_NETWORK_STATE_VALUE_TIME_WAIT = \"time_wait\" as const;\n\n/**\n * The paging access direction\n *\n * @example in\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_PAGING_DIRECTION = 'system.paging.direction' as const;\n\n/**\n * Enum value \"in\" for attribute {@link ATTR_SYSTEM_PAGING_DIRECTION}.\n */\nexport const SYSTEM_PAGING_DIRECTION_VALUE_IN = \"in\" as const;\n\n/**\n * Enum value \"out\" for attribute {@link ATTR_SYSTEM_PAGING_DIRECTION}.\n */\nexport const SYSTEM_PAGING_DIRECTION_VALUE_OUT = \"out\" as const;\n\n/**\n * The memory paging state\n *\n * @example free\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_PAGING_STATE = 'system.paging.state' as const;\n\n/**\n * Enum value \"free\" for attribute {@link ATTR_SYSTEM_PAGING_STATE}.\n */\nexport const SYSTEM_PAGING_STATE_VALUE_FREE = \"free\" as const;\n\n/**\n * Enum value \"used\" for attribute {@link ATTR_SYSTEM_PAGING_STATE}.\n */\nexport const SYSTEM_PAGING_STATE_VALUE_USED = \"used\" as const;\n\n/**\n * The memory paging type\n *\n * @example minor\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_PAGING_TYPE = 'system.paging.type' as const;\n\n/**\n * Enum value \"major\" for attribute {@link ATTR_SYSTEM_PAGING_TYPE}.\n */\nexport const SYSTEM_PAGING_TYPE_VALUE_MAJOR = \"major\" as const;\n\n/**\n * Enum value \"minor\" for attribute {@link ATTR_SYSTEM_PAGING_TYPE}.\n */\nexport const SYSTEM_PAGING_TYPE_VALUE_MINOR = \"minor\" as const;\n\n/**\n * The process state, e.g., [Linux Process State Codes](https://man7.org/linux/man-pages/man1/ps.1.html#PROCESS_STATE_CODES)\n *\n * @example running\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_SYSTEM_PROCESS_STATUS = 'system.process.status' as const;\n\n/**\n * Enum value \"defunct\" for attribute {@link ATTR_SYSTEM_PROCESS_STATUS}.\n */\nexport const SYSTEM_PROCESS_STATUS_VALUE_DEFUNCT = \"defunct\" as const;\n\n/**\n * Enum value \"running\" for attribute {@link ATTR_SYSTEM_PROCESS_STATUS}.\n */\nexport const SYSTEM_PROCESS_STATUS_VALUE_RUNNING = \"running\" as const;\n\n/**\n * Enum value \"sleeping\" for attribute {@link ATTR_SYSTEM_PROCESS_STATUS}.\n */\nexport const SYSTEM_PROCESS_STATUS_VALUE_SLEEPING = \"sleeping\" as const;\n\n/**\n * Enum value \"stopped\" for attribute {@link ATTR_SYSTEM_PROCESS_STATUS}.\n */\nexport const SYSTEM_PROCESS_STATUS_VALUE_STOPPED = \"stopped\" as const;\n\n/**\n * Deprecated, use `system.process.status` instead.\n *\n * @example running\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `system.process.status`.\n */\nexport const ATTR_SYSTEM_PROCESSES_STATUS = 'system.processes.status' as const;\n\n/**\n * Enum value \"defunct\" for attribute {@link ATTR_SYSTEM_PROCESSES_STATUS}.\n */\nexport const SYSTEM_PROCESSES_STATUS_VALUE_DEFUNCT = \"defunct\" as const;\n\n/**\n * Enum value \"running\" for attribute {@link ATTR_SYSTEM_PROCESSES_STATUS}.\n */\nexport const SYSTEM_PROCESSES_STATUS_VALUE_RUNNING = \"running\" as const;\n\n/**\n * Enum value \"sleeping\" for attribute {@link ATTR_SYSTEM_PROCESSES_STATUS}.\n */\nexport const SYSTEM_PROCESSES_STATUS_VALUE_SLEEPING = \"sleeping\" as const;\n\n/**\n * Enum value \"stopped\" for attribute {@link ATTR_SYSTEM_PROCESSES_STATUS}.\n */\nexport const SYSTEM_PROCESSES_STATUS_VALUE_STOPPED = \"stopped\" as const;\n\n/**\n * The name of the auto instrumentation agent or distribution, if used.\n *\n * @example parts-unlimited-java\n *\n * @note Official auto instrumentation agents and distributions **SHOULD** set the `telemetry.distro.name` attribute to\n * a string starting with `opentelemetry-`, e.g. `opentelemetry-java-instrumentation`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TELEMETRY_DISTRO_NAME = 'telemetry.distro.name' as const;\n\n/**\n * The version string of the auto instrumentation agent or distribution, if used.\n *\n * @example 1.2.3\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TELEMETRY_DISTRO_VERSION = 'telemetry.distro.version' as const;\n\n/**\n * The fully qualified human readable name of the [test case](https://wikipedia.org/wiki/Test_case).\n *\n * @example org.example.TestCase1.test1\n * @example example/tests/TestCase1.test1\n * @example ExampleTestCase1_test1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TEST_CASE_NAME = 'test.case.name' as const;\n\n/**\n * The status of the actual test case result from test execution.\n *\n * @example pass\n * @example fail\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TEST_CASE_RESULT_STATUS = 'test.case.result.status' as const;\n\n/**\n * Enum value \"fail\" for attribute {@link ATTR_TEST_CASE_RESULT_STATUS}.\n */\nexport const TEST_CASE_RESULT_STATUS_VALUE_FAIL = \"fail\" as const;\n\n/**\n * Enum value \"pass\" for attribute {@link ATTR_TEST_CASE_RESULT_STATUS}.\n */\nexport const TEST_CASE_RESULT_STATUS_VALUE_PASS = \"pass\" as const;\n\n/**\n * The human readable name of a [test suite](https://wikipedia.org/wiki/Test_suite).\n *\n * @example TestSuite1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TEST_SUITE_NAME = 'test.suite.name' as const;\n\n/**\n * The status of the test suite run.\n *\n * @example success\n * @example failure\n * @example skipped\n * @example aborted\n * @example timed_out\n * @example in_progress\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TEST_SUITE_RUN_STATUS = 'test.suite.run.status' as const;\n\n/**\n * Enum value \"aborted\" for attribute {@link ATTR_TEST_SUITE_RUN_STATUS}.\n */\nexport const TEST_SUITE_RUN_STATUS_VALUE_ABORTED = \"aborted\" as const;\n\n/**\n * Enum value \"failure\" for attribute {@link ATTR_TEST_SUITE_RUN_STATUS}.\n */\nexport const TEST_SUITE_RUN_STATUS_VALUE_FAILURE = \"failure\" as const;\n\n/**\n * Enum value \"in_progress\" for attribute {@link ATTR_TEST_SUITE_RUN_STATUS}.\n */\nexport const TEST_SUITE_RUN_STATUS_VALUE_IN_PROGRESS = \"in_progress\" as const;\n\n/**\n * Enum value \"skipped\" for attribute {@link ATTR_TEST_SUITE_RUN_STATUS}.\n */\nexport const TEST_SUITE_RUN_STATUS_VALUE_SKIPPED = \"skipped\" as const;\n\n/**\n * Enum value \"success\" for attribute {@link ATTR_TEST_SUITE_RUN_STATUS}.\n */\nexport const TEST_SUITE_RUN_STATUS_VALUE_SUCCESS = \"success\" as const;\n\n/**\n * Enum value \"timed_out\" for attribute {@link ATTR_TEST_SUITE_RUN_STATUS}.\n */\nexport const TEST_SUITE_RUN_STATUS_VALUE_TIMED_OUT = \"timed_out\" as const;\n\n/**\n * Current \"managed\" thread ID (as opposed to OS thread ID).\n *\n * @example 42\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_THREAD_ID = 'thread.id' as const;\n\n/**\n * Current thread name.\n *\n * @example \"main\"\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_THREAD_NAME = 'thread.name' as const;\n\n/**\n * String indicating the [cipher](https://datatracker.ietf.org/doc/html/rfc5246#appendix-A.5) used during the current connection.\n *\n * @example TLS_RSA_WITH_3DES_EDE_CBC_SHA\n * @example TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256\n *\n * @note The values allowed for `tls.cipher` **MUST** be one of the `Descriptions` of the [registered TLS Cipher Suits](https://www.iana.org/assignments/tls-parameters/tls-parameters.xhtml#table-tls-parameters-4).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CIPHER = 'tls.cipher' as const;\n\n/**\n * PEM-encoded stand-alone certificate offered by the client. This is usually mutually-exclusive of `client.certificate_chain` since this value also exists in that list.\n *\n * @example MII...\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_CERTIFICATE = 'tls.client.certificate' as const;\n\n/**\n * Array of PEM-encoded certificates that make up the certificate chain offered by the client. This is usually mutually-exclusive of `client.certificate` since that value should be the first certificate in the chain.\n *\n * @example [\"MII...\", \"MI...\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_CERTIFICATE_CHAIN = 'tls.client.certificate_chain' as const;\n\n/**\n * Certificate fingerprint using the MD5 digest of DER-encoded version of certificate offered by the client. For consistency with other hash values, this value should be formatted as an uppercase hash.\n *\n * @example 0F76C7F2C55BFD7D8E8B8F4BFBF0C9EC\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_HASH_MD5 = 'tls.client.hash.md5' as const;\n\n/**\n * Certificate fingerprint using the SHA1 digest of DER-encoded version of certificate offered by the client. For consistency with other hash values, this value should be formatted as an uppercase hash.\n *\n * @example 9E393D93138888D288266C2D915214D1D1CCEB2A\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_HASH_SHA1 = 'tls.client.hash.sha1' as const;\n\n/**\n * Certificate fingerprint using the SHA256 digest of DER-encoded version of certificate offered by the client. For consistency with other hash values, this value should be formatted as an uppercase hash.\n *\n * @example 0687F666A054EF17A08E2F2162EAB4CBC0D265E1D7875BE74BF3C712CA92DAF0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_HASH_SHA256 = 'tls.client.hash.sha256' as const;\n\n/**\n * Distinguished name of [subject](https://datatracker.ietf.org/doc/html/rfc5280#section-4.1.2.6) of the issuer of the x.509 certificate presented by the client.\n *\n * @example CN=Example Root CA, OU=Infrastructure Team, DC=example, DC=com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_ISSUER = 'tls.client.issuer' as const;\n\n/**\n * A hash that identifies clients based on how they perform an SSL/TLS handshake.\n *\n * @example d4e5b18d6b55c71272893221c96ba240\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_JA3 = 'tls.client.ja3' as const;\n\n/**\n * Date/Time indicating when client certificate is no longer considered valid.\n *\n * @example 2021-01-01T00:00:00.000Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_NOT_AFTER = 'tls.client.not_after' as const;\n\n/**\n * Date/Time indicating when client certificate is first considered valid.\n *\n * @example 1970-01-01T00:00:00.000Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_NOT_BEFORE = 'tls.client.not_before' as const;\n\n/**\n * Deprecated, use `server.address` instead.\n *\n * @example opentelemetry.io\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `server.address`.\n */\nexport const ATTR_TLS_CLIENT_SERVER_NAME = 'tls.client.server_name' as const;\n\n/**\n * Distinguished name of subject of the x.509 certificate presented by the client.\n *\n * @example CN=myclient, OU=Documentation Team, DC=example, DC=com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_SUBJECT = 'tls.client.subject' as const;\n\n/**\n * Array of ciphers offered by the client during the client hello.\n *\n * @example [\"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384\", \"TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CLIENT_SUPPORTED_CIPHERS = 'tls.client.supported_ciphers' as const;\n\n/**\n * String indicating the curve used for the given cipher, when applicable\n *\n * @example secp256r1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_CURVE = 'tls.curve' as const;\n\n/**\n * Boolean flag indicating if the TLS negotiation was successful and transitioned to an encrypted tunnel.\n *\n * @example true\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_ESTABLISHED = 'tls.established' as const;\n\n/**\n * String indicating the protocol being tunneled. Per the values in the [IANA registry](https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids), this string should be lower case.\n *\n * @example http/1.1\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_NEXT_PROTOCOL = 'tls.next_protocol' as const;\n\n/**\n * Normalized lowercase protocol name parsed from original string of the negotiated [SSL/TLS protocol version](https://docs.openssl.org/1.1.1/man3/SSL_get_version/#return-values)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_PROTOCOL_NAME = 'tls.protocol.name' as const;\n\n/**\n * Enum value \"ssl\" for attribute {@link ATTR_TLS_PROTOCOL_NAME}.\n */\nexport const TLS_PROTOCOL_NAME_VALUE_SSL = \"ssl\" as const;\n\n/**\n * Enum value \"tls\" for attribute {@link ATTR_TLS_PROTOCOL_NAME}.\n */\nexport const TLS_PROTOCOL_NAME_VALUE_TLS = \"tls\" as const;\n\n/**\n * Numeric part of the version parsed from the original string of the negotiated [SSL/TLS protocol version](https://docs.openssl.org/1.1.1/man3/SSL_get_version/#return-values)\n *\n * @example 1.2\n * @example 3\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_PROTOCOL_VERSION = 'tls.protocol.version' as const;\n\n/**\n * Boolean flag indicating if this TLS connection was resumed from an existing TLS negotiation.\n *\n * @example true\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_RESUMED = 'tls.resumed' as const;\n\n/**\n * PEM-encoded stand-alone certificate offered by the server. This is usually mutually-exclusive of `server.certificate_chain` since this value also exists in that list.\n *\n * @example MII...\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_CERTIFICATE = 'tls.server.certificate' as const;\n\n/**\n * Array of PEM-encoded certificates that make up the certificate chain offered by the server. This is usually mutually-exclusive of `server.certificate` since that value should be the first certificate in the chain.\n *\n * @example [\"MII...\", \"MI...\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_CERTIFICATE_CHAIN = 'tls.server.certificate_chain' as const;\n\n/**\n * Certificate fingerprint using the MD5 digest of DER-encoded version of certificate offered by the server. For consistency with other hash values, this value should be formatted as an uppercase hash.\n *\n * @example 0F76C7F2C55BFD7D8E8B8F4BFBF0C9EC\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_HASH_MD5 = 'tls.server.hash.md5' as const;\n\n/**\n * Certificate fingerprint using the SHA1 digest of DER-encoded version of certificate offered by the server. For consistency with other hash values, this value should be formatted as an uppercase hash.\n *\n * @example 9E393D93138888D288266C2D915214D1D1CCEB2A\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_HASH_SHA1 = 'tls.server.hash.sha1' as const;\n\n/**\n * Certificate fingerprint using the SHA256 digest of DER-encoded version of certificate offered by the server. For consistency with other hash values, this value should be formatted as an uppercase hash.\n *\n * @example 0687F666A054EF17A08E2F2162EAB4CBC0D265E1D7875BE74BF3C712CA92DAF0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_HASH_SHA256 = 'tls.server.hash.sha256' as const;\n\n/**\n * Distinguished name of [subject](https://datatracker.ietf.org/doc/html/rfc5280#section-4.1.2.6) of the issuer of the x.509 certificate presented by the client.\n *\n * @example CN=Example Root CA, OU=Infrastructure Team, DC=example, DC=com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_ISSUER = 'tls.server.issuer' as const;\n\n/**\n * A hash that identifies servers based on how they perform an SSL/TLS handshake.\n *\n * @example d4e5b18d6b55c71272893221c96ba240\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_JA3S = 'tls.server.ja3s' as const;\n\n/**\n * Date/Time indicating when server certificate is no longer considered valid.\n *\n * @example 2021-01-01T00:00:00.000Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_NOT_AFTER = 'tls.server.not_after' as const;\n\n/**\n * Date/Time indicating when server certificate is first considered valid.\n *\n * @example 1970-01-01T00:00:00.000Z\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_NOT_BEFORE = 'tls.server.not_before' as const;\n\n/**\n * Distinguished name of subject of the x.509 certificate presented by the server.\n *\n * @example CN=myserver, OU=Documentation Team, DC=example, DC=com\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_TLS_SERVER_SUBJECT = 'tls.server.subject' as const;\n\n/**\n * Domain extracted from the `url.full`, such as \"opentelemetry.io\".\n *\n * @example www.foo.bar\n * @example opentelemetry.io\n * @example **********\n * @example [1080:0:0:0:8:800:200C:417A]\n *\n * @note In some cases a URL may refer to an IP and/or port directly, without a domain name. In this case, the IP address would go to the domain field. If the URL contains a [literal IPv6 address](https://www.rfc-editor.org/rfc/rfc2732#section-2) enclosed by `[` and `]`, the `[` and `]` characters should also be captured in the domain field.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_DOMAIN = 'url.domain' as const;\n\n/**\n * The file extension extracted from the `url.full`, excluding the leading dot.\n *\n * @example png\n * @example gz\n *\n * @note The file extension is only set if it exists, as not every url has a file extension. When the file name has multiple extensions `example.tar.gz`, only the last one should be captured `gz`, not `tar.gz`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_EXTENSION = 'url.extension' as const;\n\n/**\n * Unmodified original URL as seen in the event source.\n *\n * @example https://www.foo.bar/search?q=OpenTelemetry#SemConv\n * @example search?q=OpenTelemetry\n *\n * @note In network monitoring, the observed URL may be a full URL, whereas in access logs, the URL is often just represented as a path. This field is meant to represent the URL as it was observed, complete or not.\n * `url.original` might contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case password and username **SHOULD NOT** be redacted and attribute's value **SHOULD** remain the same.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_ORIGINAL = 'url.original' as const;\n\n/**\n * Port extracted from the `url.full`\n *\n * @example 443\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_PORT = 'url.port' as const;\n\n/**\n * The highest registered url domain, stripped of the subdomain.\n *\n * @example example.com\n * @example foo.co.uk\n *\n * @note This value can be determined precisely with the [public suffix list](https://publicsuffix.org/). For example, the registered domain for `foo.example.com` is `example.com`. Trying to approximate this by simply taking the last two labels will not work well for TLDs such as `co.uk`.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_REGISTERED_DOMAIN = 'url.registered_domain' as const;\n\n/**\n * The subdomain portion of a fully qualified domain name includes all of the names except the host name under the registered_domain. In a partially qualified domain, or if the qualification level of the full name cannot be determined, subdomain contains all of the names below the registered domain.\n *\n * @example east\n * @example sub2.sub1\n *\n * @note The subdomain portion of `www.east.mydomain.co.uk` is `east`. If the domain has multiple levels of subdomain, such as `sub2.sub1.example.com`, the subdomain field should contain `sub2.sub1`, with no trailing period.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_SUBDOMAIN = 'url.subdomain' as const;\n\n/**\n * The low-cardinality template of an [absolute path reference](https://www.rfc-editor.org/rfc/rfc3986#section-4.2).\n *\n * @example /users/{id}\n * @example /users/:id\n * @example /users?id={id}\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_TEMPLATE = 'url.template' as const;\n\n/**\n * The effective top level domain (eTLD), also known as the domain suffix, is the last part of the domain name. For example, the top level domain for example.com is `com`.\n *\n * @example com\n * @example co.uk\n *\n * @note This value can be determined precisely with the [public suffix list](https://publicsuffix.org/).\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_URL_TOP_LEVEL_DOMAIN = 'url.top_level_domain' as const;\n\n/**\n * User email address.\n *\n * @example <EMAIL>\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_EMAIL = 'user.email' as const;\n\n/**\n * User's full name\n *\n * @example Albert Einstein\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_FULL_NAME = 'user.full_name' as const;\n\n/**\n * Unique user hash to correlate information for a user in anonymized form.\n *\n * @example 364fc68eaf4c8acec74a4e52d7d1feaa\n *\n * @note Useful if `user.id` or `user.name` contain confidential information and cannot be used.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_HASH = 'user.hash' as const;\n\n/**\n * Unique identifier of the user.\n *\n * @example S-1-5-21-202424912787-2692429404-2351956786-1000\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_ID = 'user.id' as const;\n\n/**\n * Short name or login/username of the user.\n *\n * @example a.einstein\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_NAME = 'user.name' as const;\n\n/**\n * Array of user roles at the time of the event.\n *\n * @example [\"admin\", \"reporting_user\"]\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_ROLES = 'user.roles' as const;\n\n/**\n * Name of the user-agent extracted from original. Usually refers to the browser's name.\n *\n * @example Safari\n * @example YourApp\n *\n * @note [Example](https://www.whatsmyua.info) of extracting browser's name from original string. In the case of using a user-agent for non-browser products, such as microservices with multiple names/versions inside the `user_agent.original`, the most significant name **SHOULD** be selected. In such a scenario it should align with `user_agent.version`\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_AGENT_NAME = 'user_agent.name' as const;\n\n/**\n * Human readable operating system name.\n *\n * @example iOS\n * @example Android\n * @example Ubuntu\n *\n * @note For mapping user agent strings to OS names, libraries such as [ua-parser](https://github.com/ua-parser) can be utilized.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_AGENT_OS_NAME = 'user_agent.os.name' as const;\n\n/**\n * The version string of the operating system as defined in [Version Attributes](/docs/resource/README.md#version-attributes).\n *\n * @example 14.2.1\n * @example 18.04.1\n *\n * @note For mapping user agent strings to OS versions, libraries such as [ua-parser](https://github.com/ua-parser) can be utilized.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_AGENT_OS_VERSION = 'user_agent.os.version' as const;\n\n/**\n * Specifies the category of synthetic traffic, such as tests or bots.\n *\n * @note This attribute **MAY** be derived from the contents of the `user_agent.original` attribute. Components that populate the attribute are responsible for determining what they consider to be synthetic bot or test traffic. This attribute can either be set for self-identification purposes, or on telemetry detected to be generated as a result of a synthetic request. This attribute is useful for distinguishing between genuine client traffic and synthetic traffic generated by bots or tests.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_AGENT_SYNTHETIC_TYPE = 'user_agent.synthetic.type' as const;\n\n/**\n * Enum value \"bot\" for attribute {@link ATTR_USER_AGENT_SYNTHETIC_TYPE}.\n */\nexport const USER_AGENT_SYNTHETIC_TYPE_VALUE_BOT = \"bot\" as const;\n\n/**\n * Enum value \"test\" for attribute {@link ATTR_USER_AGENT_SYNTHETIC_TYPE}.\n */\nexport const USER_AGENT_SYNTHETIC_TYPE_VALUE_TEST = \"test\" as const;\n\n/**\n * Version of the user-agent extracted from original. Usually refers to the browser's version\n *\n * @example 14.1.2\n * @example 1.0.0\n *\n * @note [Example](https://www.whatsmyua.info) of extracting browser's version from original string. In the case of using a user-agent for non-browser products, such as microservices with multiple names/versions inside the `user_agent.original`, the most significant version **SHOULD** be selected. In such a scenario it should align with `user_agent.name`\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_USER_AGENT_VERSION = 'user_agent.version' as const;\n\n/**\n * The type of garbage collection.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_V8JS_GC_TYPE = 'v8js.gc.type' as const;\n\n/**\n * Enum value \"incremental\" for attribute {@link ATTR_V8JS_GC_TYPE}.\n */\nexport const V8JS_GC_TYPE_VALUE_INCREMENTAL = \"incremental\" as const;\n\n/**\n * Enum value \"major\" for attribute {@link ATTR_V8JS_GC_TYPE}.\n */\nexport const V8JS_GC_TYPE_VALUE_MAJOR = \"major\" as const;\n\n/**\n * Enum value \"minor\" for attribute {@link ATTR_V8JS_GC_TYPE}.\n */\nexport const V8JS_GC_TYPE_VALUE_MINOR = \"minor\" as const;\n\n/**\n * Enum value \"weakcb\" for attribute {@link ATTR_V8JS_GC_TYPE}.\n */\nexport const V8JS_GC_TYPE_VALUE_WEAKCB = \"weakcb\" as const;\n\n/**\n * The name of the space type of heap memory.\n *\n * @note Value can be retrieved from value `space_name` of [`v8.getHeapSpaceStatistics()`](https://nodejs.org/api/v8.html#v8getheapspacestatistics)\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_V8JS_HEAP_SPACE_NAME = 'v8js.heap.space.name' as const;\n\n/**\n * Enum value \"code_space\" for attribute {@link ATTR_V8JS_HEAP_SPACE_NAME}.\n */\nexport const V8JS_HEAP_SPACE_NAME_VALUE_CODE_SPACE = \"code_space\" as const;\n\n/**\n * Enum value \"large_object_space\" for attribute {@link ATTR_V8JS_HEAP_SPACE_NAME}.\n */\nexport const V8JS_HEAP_SPACE_NAME_VALUE_LARGE_OBJECT_SPACE = \"large_object_space\" as const;\n\n/**\n * Enum value \"map_space\" for attribute {@link ATTR_V8JS_HEAP_SPACE_NAME}.\n */\nexport const V8JS_HEAP_SPACE_NAME_VALUE_MAP_SPACE = \"map_space\" as const;\n\n/**\n * Enum value \"new_space\" for attribute {@link ATTR_V8JS_HEAP_SPACE_NAME}.\n */\nexport const V8JS_HEAP_SPACE_NAME_VALUE_NEW_SPACE = \"new_space\" as const;\n\n/**\n * Enum value \"old_space\" for attribute {@link ATTR_V8JS_HEAP_SPACE_NAME}.\n */\nexport const V8JS_HEAP_SPACE_NAME_VALUE_OLD_SPACE = \"old_space\" as const;\n\n/**\n * The ID of the change (pull request/merge request/changelist) if applicable. This is usually a unique (within repository) identifier generated by the VCS system.\n *\n * @example 123\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_CHANGE_ID = 'vcs.change.id' as const;\n\n/**\n * The state of the change (pull request/merge request/changelist).\n *\n * @example open\n * @example closed\n * @example merged\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_CHANGE_STATE = 'vcs.change.state' as const;\n\n/**\n * Enum value \"closed\" for attribute {@link ATTR_VCS_CHANGE_STATE}.\n */\nexport const VCS_CHANGE_STATE_VALUE_CLOSED = \"closed\" as const;\n\n/**\n * Enum value \"merged\" for attribute {@link ATTR_VCS_CHANGE_STATE}.\n */\nexport const VCS_CHANGE_STATE_VALUE_MERGED = \"merged\" as const;\n\n/**\n * Enum value \"open\" for attribute {@link ATTR_VCS_CHANGE_STATE}.\n */\nexport const VCS_CHANGE_STATE_VALUE_OPEN = \"open\" as const;\n\n/**\n * Enum value \"wip\" for attribute {@link ATTR_VCS_CHANGE_STATE}.\n */\nexport const VCS_CHANGE_STATE_VALUE_WIP = \"wip\" as const;\n\n/**\n * The human readable title of the change (pull request/merge request/changelist). This title is often a brief summary of the change and may get merged in to a ref as the commit summary.\n *\n * @example Fixes broken thing\n * @example feat: add my new feature\n * @example [chore] update dependency\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_CHANGE_TITLE = 'vcs.change.title' as const;\n\n/**\n * The type of line change being measured on a branch or change.\n *\n * @example added\n * @example removed\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_LINE_CHANGE_TYPE = 'vcs.line_change.type' as const;\n\n/**\n * Enum value \"added\" for attribute {@link ATTR_VCS_LINE_CHANGE_TYPE}.\n */\nexport const VCS_LINE_CHANGE_TYPE_VALUE_ADDED = \"added\" as const;\n\n/**\n * Enum value \"removed\" for attribute {@link ATTR_VCS_LINE_CHANGE_TYPE}.\n */\nexport const VCS_LINE_CHANGE_TYPE_VALUE_REMOVED = \"removed\" as const;\n\n/**\n * The group owner within the version control system.\n *\n * @example my-org\n * @example myteam\n * @example business-unit\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_OWNER_NAME = 'vcs.owner.name' as const;\n\n/**\n * The name of the version control system provider.\n *\n * @example github\n * @example gitlab\n * @example gitea\n * @example bitbucket\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_PROVIDER_NAME = 'vcs.provider.name' as const;\n\n/**\n * Enum value \"bitbucket\" for attribute {@link ATTR_VCS_PROVIDER_NAME}.\n */\nexport const VCS_PROVIDER_NAME_VALUE_BITBUCKET = \"bitbucket\" as const;\n\n/**\n * Enum value \"gitea\" for attribute {@link ATTR_VCS_PROVIDER_NAME}.\n */\nexport const VCS_PROVIDER_NAME_VALUE_GITEA = \"gitea\" as const;\n\n/**\n * Enum value \"github\" for attribute {@link ATTR_VCS_PROVIDER_NAME}.\n */\nexport const VCS_PROVIDER_NAME_VALUE_GITHUB = \"github\" as const;\n\n/**\n * Enum value \"gitlab\" for attribute {@link ATTR_VCS_PROVIDER_NAME}.\n */\nexport const VCS_PROVIDER_NAME_VALUE_GITLAB = \"gitlab\" as const;\n\n/**\n * Enum value \"gittea\" for attribute {@link ATTR_VCS_PROVIDER_NAME}.\n */\nexport const VCS_PROVIDER_NAME_VALUE_GITTEA = \"gittea\" as const;\n\n/**\n * The name of the [reference](https://git-scm.com/docs/gitglossary#def_ref) such as **branch** or **tag** in the repository.\n *\n * @example my-feature-branch\n * @example tag-1-test\n *\n * @note `base` refers to the starting point of a change. For example, `main`\n * would be the base reference of type branch if you've created a new\n * reference of type branch from it and created new commits.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REF_BASE_NAME = 'vcs.ref.base.name' as const;\n\n/**\n * The revision, literally [revised version](https://www.merriam-webster.com/dictionary/revision), The revision most often refers to a commit object in Git, or a revision number in SVN.\n *\n * @example 9d59409acf479dfa0df1aa568182e43e43df8bbe28d60fcf2bc52e30068802cc\n * @example main\n * @example 123\n * @example HEAD\n *\n * @note `base` refers to the starting point of a change. For example, `main`\n * would be the base reference of type branch if you've created a new\n * reference of type branch from it and created new commits. The\n * revision can be a full [hash value (see\n * glossary)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-5.pdf),\n * of the recorded change to a ref within a repository pointing to a\n * commit [commit](https://git-scm.com/docs/git-commit) object. It does\n * not necessarily have to be a hash; it can simply define a [revision\n * number](https://svnbook.red-bean.com/en/1.7/svn.tour.revs.specifiers.html)\n * which is an integer that is monotonically increasing. In cases where\n * it is identical to the `ref.base.name`, it **SHOULD** still be included.\n * It is up to the implementer to decide which value to set as the\n * revision based on the VCS system and situational context.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REF_BASE_REVISION = 'vcs.ref.base.revision' as const;\n\n/**\n * The type of the [reference](https://git-scm.com/docs/gitglossary#def_ref) in the repository.\n *\n * @example branch\n * @example tag\n *\n * @note `base` refers to the starting point of a change. For example, `main`\n * would be the base reference of type branch if you've created a new\n * reference of type branch from it and created new commits.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REF_BASE_TYPE = 'vcs.ref.base.type' as const;\n\n/**\n * Enum value \"branch\" for attribute {@link ATTR_VCS_REF_BASE_TYPE}.\n */\nexport const VCS_REF_BASE_TYPE_VALUE_BRANCH = \"branch\" as const;\n\n/**\n * Enum value \"tag\" for attribute {@link ATTR_VCS_REF_BASE_TYPE}.\n */\nexport const VCS_REF_BASE_TYPE_VALUE_TAG = \"tag\" as const;\n\n/**\n * The name of the [reference](https://git-scm.com/docs/gitglossary#def_ref) such as **branch** or **tag** in the repository.\n *\n * @example my-feature-branch\n * @example tag-1-test\n *\n * @note `head` refers to where you are right now; the current reference at a\n * given time.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REF_HEAD_NAME = 'vcs.ref.head.name' as const;\n\n/**\n * The revision, literally [revised version](https://www.merriam-webster.com/dictionary/revision), The revision most often refers to a commit object in Git, or a revision number in SVN.\n *\n * @example 9d59409acf479dfa0df1aa568182e43e43df8bbe28d60fcf2bc52e30068802cc\n * @example main\n * @example 123\n * @example HEAD\n *\n * @note `head` refers to where you are right now; the current reference at a\n * given time.The revision can be a full [hash value (see\n * glossary)](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.186-5.pdf),\n * of the recorded change to a ref within a repository pointing to a\n * commit [commit](https://git-scm.com/docs/git-commit) object. It does\n * not necessarily have to be a hash; it can simply define a [revision\n * number](https://svnbook.red-bean.com/en/1.7/svn.tour.revs.specifiers.html)\n * which is an integer that is monotonically increasing. In cases where\n * it is identical to the `ref.head.name`, it **SHOULD** still be included.\n * It is up to the implementer to decide which value to set as the\n * revision based on the VCS system and situational context.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REF_HEAD_REVISION = 'vcs.ref.head.revision' as const;\n\n/**\n * The type of the [reference](https://git-scm.com/docs/gitglossary#def_ref) in the repository.\n *\n * @example branch\n * @example tag\n *\n * @note `head` refers to where you are right now; the current reference at a\n * given time.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REF_HEAD_TYPE = 'vcs.ref.head.type' as const;\n\n/**\n * Enum value \"branch\" for attribute {@link ATTR_VCS_REF_HEAD_TYPE}.\n */\nexport const VCS_REF_HEAD_TYPE_VALUE_BRANCH = \"branch\" as const;\n\n/**\n * Enum value \"tag\" for attribute {@link ATTR_VCS_REF_HEAD_TYPE}.\n */\nexport const VCS_REF_HEAD_TYPE_VALUE_TAG = \"tag\" as const;\n\n/**\n * The type of the [reference](https://git-scm.com/docs/gitglossary#def_ref) in the repository.\n *\n * @example branch\n * @example tag\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REF_TYPE = 'vcs.ref.type' as const;\n\n/**\n * Enum value \"branch\" for attribute {@link ATTR_VCS_REF_TYPE}.\n */\nexport const VCS_REF_TYPE_VALUE_BRANCH = \"branch\" as const;\n\n/**\n * Enum value \"tag\" for attribute {@link ATTR_VCS_REF_TYPE}.\n */\nexport const VCS_REF_TYPE_VALUE_TAG = \"tag\" as const;\n\n/**\n * Deprecated, use `vcs.change.id` instead.\n *\n * @example 123\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `vcs.change.id`.\n */\nexport const ATTR_VCS_REPOSITORY_CHANGE_ID = 'vcs.repository.change.id' as const;\n\n/**\n * Deprecated, use `vcs.change.title` instead.\n *\n * @example Fixes broken thing\n * @example feat: add my new feature\n * @example [chore] update dependency\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `vcs.change.title`.\n */\nexport const ATTR_VCS_REPOSITORY_CHANGE_TITLE = 'vcs.repository.change.title' as const;\n\n/**\n * The human readable name of the repository. It **SHOULD NOT** include any additional identifier like Group/SubGroup in GitLab or organization in GitHub.\n *\n * @example semantic-conventions\n * @example my-cool-repo\n *\n * @note Due to it only being the name, it can clash with forks of the same\n * repository if collecting telemetry across multiple orgs or groups in\n * the same backends.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REPOSITORY_NAME = 'vcs.repository.name' as const;\n\n/**\n * Deprecated, use `vcs.ref.head.name` instead.\n *\n * @example my-feature-branch\n * @example tag-1-test\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `vcs.ref.head.name`.\n */\nexport const ATTR_VCS_REPOSITORY_REF_NAME = 'vcs.repository.ref.name' as const;\n\n/**\n * Deprecated, use `vcs.ref.head.revision` instead.\n *\n * @example 9d59409acf479dfa0df1aa568182e43e43df8bbe28d60fcf2bc52e30068802cc\n * @example main\n * @example 123\n * @example HEAD\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `vcs.ref.head.revision`.\n */\nexport const ATTR_VCS_REPOSITORY_REF_REVISION = 'vcs.repository.ref.revision' as const;\n\n/**\n * Deprecated, use `vcs.ref.head.type` instead.\n *\n * @example branch\n * @example tag\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n *\n * @deprecated Replaced by `vcs.ref.head.type`.\n */\nexport const ATTR_VCS_REPOSITORY_REF_TYPE = 'vcs.repository.ref.type' as const;\n\n/**\n * Enum value \"branch\" for attribute {@link ATTR_VCS_REPOSITORY_REF_TYPE}.\n */\nexport const VCS_REPOSITORY_REF_TYPE_VALUE_BRANCH = \"branch\" as const;\n\n/**\n * Enum value \"tag\" for attribute {@link ATTR_VCS_REPOSITORY_REF_TYPE}.\n */\nexport const VCS_REPOSITORY_REF_TYPE_VALUE_TAG = \"tag\" as const;\n\n/**\n * The [canonical URL](https://support.google.com/webmasters/answer/10347851?hl=en#:~:text=A%20canonical%20URL%20is%20the,Google%20chooses%20one%20as%20canonical.) of the repository providing the complete HTTP(S) address in order to locate and identify the repository through a browser.\n *\n * @example https://github.com/opentelemetry/open-telemetry-collector-contrib\n * @example https://gitlab.com/my-org/my-project/my-projects-project/repo\n *\n * @note In Git Version Control Systems, the canonical URL **SHOULD NOT** include\n * the `.git` extension.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REPOSITORY_URL_FULL = 'vcs.repository.url.full' as const;\n\n/**\n * The type of revision comparison.\n *\n * @example ahead\n * @example behind\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_VCS_REVISION_DELTA_DIRECTION = 'vcs.revision_delta.direction' as const;\n\n/**\n * Enum value \"ahead\" for attribute {@link ATTR_VCS_REVISION_DELTA_DIRECTION}.\n */\nexport const VCS_REVISION_DELTA_DIRECTION_VALUE_AHEAD = \"ahead\" as const;\n\n/**\n * Enum value \"behind\" for attribute {@link ATTR_VCS_REVISION_DELTA_DIRECTION}.\n */\nexport const VCS_REVISION_DELTA_DIRECTION_VALUE_BEHIND = \"behind\" as const;\n\n/**\n * Additional description of the web engine (e.g. detailed version and edition information).\n *\n * @example WildFly Full 21.0.0.Final (WildFly Core 13.0.1.Final) - 2.2.2.Final\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_WEBENGINE_DESCRIPTION = 'webengine.description' as const;\n\n/**\n * The name of the web engine.\n *\n * @example WildFly\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_WEBENGINE_NAME = 'webengine.name' as const;\n\n/**\n * The version of the web engine.\n *\n * @example 21.0.0\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_WEBENGINE_VERSION = 'webengine.version' as const;\n\n"]}