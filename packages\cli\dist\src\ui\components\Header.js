import { Fragment, jsx, jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Box, Text } from "ink";
import { Colors } from "../colors.js";
const Header = ({
  config,
  settings,
  onShowHelp,
  onShowTheme,
  onShowAuth
}) => {
  const modelName = config.getModelName() || "Unknown";
  const authType = settings.getSelectedAuthType() || "none";
  const sandboxEnabled = Boolean(settings.getSandbox());
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [
    /* @__PURE__ */ jsxs(Box, { justifyContent: "space-between", alignItems: "center", children: [
      /* @__PURE__ */ jsxs(Box, { alignItems: "center", children: [
        /* @__PURE__ */ jsx(Text, { color: Colors.AccentBlue, bold: true, children: "Arien CLI" }),
        /* @__PURE__ */ jsxs(Text, { color: Colors.Gray, dimColor: true, children: [
          " ",
          "v1.0.0"
        ] })
      ] }),
      /* @__PURE__ */ jsxs(Box, { alignItems: "center", children: [
        /* @__PURE__ */ jsx(Text, { color: Colors.Gray, dimColor: true, children: "Model:" }),
        /* @__PURE__ */ jsxs(Text, { color: Colors.AccentGreen, children: [
          " ",
          modelName
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs(Box, { justifyContent: "space-between", alignItems: "center", children: [
      /* @__PURE__ */ jsxs(Box, { alignItems: "center", children: [
        /* @__PURE__ */ jsx(Text, { color: Colors.Gray, dimColor: true, children: "Auth:" }),
        /* @__PURE__ */ jsxs(Text, { color: authType === "none" ? Colors.AccentRed : Colors.AccentGreen, children: [
          " ",
          authType
        ] }),
        sandboxEnabled && /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsxs(Text, { color: Colors.Gray, dimColor: true, children: [
            " ",
            "| Sandbox:"
          ] }),
          /* @__PURE__ */ jsxs(Text, { color: Colors.AccentYellow, children: [
            " ",
            "enabled"
          ] })
        ] })
      ] }),
      /* @__PURE__ */ jsx(Box, { alignItems: "center", children: /* @__PURE__ */ jsx(Text, { color: Colors.Gray, dimColor: true, children: "F1: Help | F2: Theme | F3: Auth" }) })
    ] }),
    /* @__PURE__ */ jsx(Box, { children: /* @__PURE__ */ jsx(Text, { color: Colors.Gray, children: "\u2500".repeat(80) }) })
  ] });
};
export {
  Header
};
//# sourceMappingURL=Header.js.map
