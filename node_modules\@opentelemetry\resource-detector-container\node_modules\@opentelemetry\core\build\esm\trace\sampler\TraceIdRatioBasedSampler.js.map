{"version": 3, "file": "TraceIdRatioBasedSampler.js", "sourceRoot": "", "sources": ["../../../../src/trace/sampler/TraceIdRatioBasedSampler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAEL,gBAAgB,EAEhB,cAAc,GACf,MAAM,oBAAoB,CAAC;AAE5B;;;GAGG;AACH;IAGE,kCAA6B,MAAkB;QAAlB,uBAAA,EAAA,UAAkB;QAAlB,WAAM,GAAN,MAAM,CAAY;QAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,+CAAY,GAAZ,UAAa,OAAgB,EAAE,OAAe;QAC5C,OAAO;YACL,QAAQ,EACN,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW;gBACrE,CAAC,CAAC,gBAAgB,CAAC,kBAAkB;gBACrC,CAAC,CAAC,gBAAgB,CAAC,UAAU;SAClC,CAAC;IACJ,CAAC;IAED,2CAAQ,GAAR;QACE,OAAO,uBAAqB,IAAI,CAAC,MAAM,MAAG,CAAC;IAC7C,CAAC;IAEO,6CAAU,GAAlB,UAAmB,KAAa;QAC9B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACxD,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjD,CAAC;IAEO,8CAAW,GAAnB,UAAoB,OAAe;QACjC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAClB,IAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACvD,YAAY,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5C;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IACH,+BAAC;AAAD,CAAC,AAnCD,IAmCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Sampler,\n  SamplingDecision,\n  SamplingResult,\n  isValidTraceId,\n} from '@opentelemetry/api';\n\n/**\n * @deprecated Use the one defined in @opentelemetry/sdk-trace-base instead.\n * Sampler that samples a given fraction of traces based of trace id deterministically.\n */\nexport class TraceIdRatioBasedSampler implements Sampler {\n  private _upperBound: number;\n\n  constructor(private readonly _ratio: number = 0) {\n    this._ratio = this._normalize(_ratio);\n    this._upperBound = Math.floor(this._ratio * 0xffffffff);\n  }\n\n  shouldSample(context: unknown, traceId: string): SamplingResult {\n    return {\n      decision:\n        isValidTraceId(traceId) && this._accumulate(traceId) < this._upperBound\n          ? SamplingDecision.RECORD_AND_SAMPLED\n          : SamplingDecision.NOT_RECORD,\n    };\n  }\n\n  toString(): string {\n    return `TraceIdRatioBased{${this._ratio}}`;\n  }\n\n  private _normalize(ratio: number): number {\n    if (typeof ratio !== 'number' || isNaN(ratio)) return 0;\n    return ratio >= 1 ? 1 : ratio <= 0 ? 0 : ratio;\n  }\n\n  private _accumulate(traceId: string): number {\n    let accumulation = 0;\n    for (let i = 0; i < traceId.length / 8; i++) {\n      const pos = i * 8;\n      const part = parseInt(traceId.slice(pos, pos + 8), 16);\n      accumulation = (accumulation ^ part) >>> 0;\n    }\n    return accumulation;\n  }\n}\n"]}