/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
function parseError(error) {
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: error.stack,
      type: "error"
    };
  }
  if (typeof error === "string") {
    return {
      message: error,
      type: "error"
    };
  }
  if (typeof error === "object" && error !== null) {
    const obj = error;
    return {
      message: String(obj.message || obj.toString?.() || "Unknown error"),
      stack: typeof obj.stack === "string" ? obj.stack : void 0,
      code: typeof obj.code === "string" ? obj.code : void 0,
      type: "error",
      file: typeof obj.file === "string" ? obj.file : void 0,
      line: typeof obj.line === "number" ? obj.line : void 0,
      column: typeof obj.column === "number" ? obj.column : void 0
    };
  }
  return {
    message: "Unknown error occurred",
    type: "error"
  };
}
function formatError(error) {
  let formatted = error.message;
  if (error.file) {
    formatted += ` (${error.file}`;
    if (error.line) {
      formatted += `:${error.line}`;
      if (error.column) {
        formatted += `:${error.column}`;
      }
    }
    formatted += ")";
  }
  if (error.code) {
    formatted += ` [${error.code}]`;
  }
  return formatted;
}
function getErrorMessage(error) {
  const parsed = parseError(error);
  return formatError(parsed);
}
function parseAndFormatApiError(error) {
  return getErrorMessage(error);
}
export {
  formatError,
  getErrorMessage,
  parseAndFormatApiError,
  parseError
};
//# sourceMappingURL=errorParsing.js.map
