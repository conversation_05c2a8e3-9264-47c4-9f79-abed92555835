/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import {
  logToolCall
} from "../index.js";
async function executeToolCall(config, toolCallRequest, toolRegistry, abortSignal) {
  const tool = await toolRegistry.getTool(toolCallRequest.name);
  const startTime = Date.now();
  if (!tool) {
    const error = new Error(
      `Tool "${toolCallRequest.name}" not found in registry.`
    );
    const durationMs = Date.now() - startTime;
    logToolCall(config, {
      "event.name": "tool_call",
      "event.timestamp": (/* @__PURE__ */ new Date()).toISOString(),
      function_name: toolCallRequest.name,
      function_args: toolCallRequest.args,
      duration_ms: durationMs,
      success: false,
      error: error.message
    });
    return {
      name: toolCallRequest.name,
      response: { error: error.message }
    };
  }
  try {
    const effectiveAbortSignal = abortSignal ?? new AbortController().signal;
    const toolResult = await tool.execute(
      toolCallRequest.args,
      config,
      effectiveAbortSignal
    );
    const durationMs = Date.now() - startTime;
    logToolCall(config, {
      "event.name": "tool_call",
      "event.timestamp": (/* @__PURE__ */ new Date()).toISOString(),
      function_name: toolCallRequest.name,
      function_args: toolCallRequest.args,
      duration_ms: durationMs,
      success: true
    });
    return {
      name: toolCallRequest.name,
      response: toolResult
    };
  } catch (e) {
    const error = e instanceof Error ? e : new Error(String(e));
    const durationMs = Date.now() - startTime;
    logToolCall(config, {
      "event.name": "tool_call",
      "event.timestamp": (/* @__PURE__ */ new Date()).toISOString(),
      function_name: toolCallRequest.name,
      function_args: toolCallRequest.args,
      duration_ms: durationMs,
      success: false,
      error: error.message
    });
    return {
      name: toolCallRequest.name,
      response: { error: error.message }
    };
  }
}
export {
  executeToolCall
};
//# sourceMappingURL=nonInteractiveToolExecutor.js.map
