/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { Config } from '../config/config.js';
import { logger } from '../core/logger.js';
import {
  Tool,
  ToolDefinition,
  ToolParams,
  ToolResult,
  ToolExecutionContext,
  ToolUsageTracker,
} from './tools.js';
import { ToolExecutionError } from '../utils/errors.js';

export interface RegisteredTool {
  tool: Tool;
  enabled: boolean;
  category?: string;
  metadata?: Record<string, any>;
}

export interface ToolExecutionOptions {
  timeout?: number;
  retries?: number;
  trackUsage?: boolean;
}

export class ToolRegistry {
  private tools: Map<string, RegisteredTool> = new Map();
  private config: Config;
  private usageTracker: ToolUsageTracker;
  private initialized: boolean = false;

  constructor(config: Config) {
    this.config = config;
    this.usageTracker = new ToolUsageTracker();
  }

  /**
   * Initialize the registry with built-in tools
   * This is called automatically when tools are first accessed
   */
  private async initializeBuiltInTools(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Import tool classes dynamically to avoid circular dependencies
      const { ReadFileTool } = await import('./read-file.js');
      const { WriteFileTool } = await import('./write-file.js');
      const { ShellTool } = await import('./shell.js');
      const { LsTool } = await import('./ls.js');
      const { GrepTool } = await import('./grep.js');
      const { GlobTool } = await import('./glob.js');
      const { EditTool } = await import('./edit.js');
      const { WebFetchTool } = await import('./web-fetch.js');
      const { WebSearchTool } = await import('./web-search.js');
      const { MemoryTool } = await import('./memory.js');
      const { ReadManyFilesTool } = await import('./read-many-files.js');
      const { MCPClientTool } = await import('./mcp-client.js');

      // Register built-in tools
      const builtInTools = [
        { tool: new ReadFileTool(this.config), category: 'file-system' },
        { tool: new WriteFileTool(this.config), category: 'file-system' },
        { tool: new ShellTool(this.config), category: 'system' },
        { tool: new LsTool(this.config), category: 'file-system' },
        { tool: new GrepTool(this.config), category: 'file-system' },
        { tool: new GlobTool(this.config), category: 'file-system' },
        { tool: new EditTool(this.config), category: 'file-system' },
        { tool: new WebFetchTool(this.config), category: 'web' },
        { tool: new WebSearchTool(this.config), category: 'web' },
        { tool: new MemoryTool(this.config), category: 'utility' },
        { tool: new ReadManyFilesTool(this.config), category: 'file-system' },
        { tool: new MCPClientTool(), category: 'mcp' },
      ];

      for (const { tool, category } of builtInTools) {
        this.register(tool, { category });
      }

      this.initialized = true;
      logger.debug(`Initialized ToolRegistry with ${builtInTools.length} built-in tools`);
    } catch (error) {
      logger.error('Failed to initialize built-in tools:', error);
      this.initialized = true; // Prevent retry loops
    }
  }

  register(
    tool: Tool,
    options: {
      enabled?: boolean;
      category?: string;
      metadata?: Record<string, any>;
    } = {},
  ): void {
    const { enabled = true, category, metadata } = options;

    this.tools.set(tool.name, {
      tool,
      enabled,
      category,
      metadata,
    });

    logger.debug(`Registered tool: ${tool.name}`, {
      enabled,
      category,
      description: tool.description,
    });
  }

  unregister(toolName: string): boolean {
    const removed = this.tools.delete(toolName);
    if (removed) {
      logger.debug(`Unregistered tool: ${toolName}`);
    }
    return removed;
  }

  async get(toolName: string): Promise<Tool | undefined> {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    return registered?.enabled ? registered.tool : undefined;
  }

  /**
   * Alias for get() method for backward compatibility
   */
  async getTool(toolName: string): Promise<Tool | undefined> {
    return this.get(toolName);
  }

  async has(toolName: string): Promise<boolean> {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    return registered?.enabled || false;
  }

  async list(): Promise<string[]> {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.entries())
      .filter(([, registered]) => registered.enabled)
      .map(([name]) => name);
  }

  async listAll(): Promise<string[]> {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.keys());
  }

  async getDefinitions(): Promise<ToolDefinition[]> {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.entries())
      .filter(([, registered]) => registered.enabled)
      .map(([, registered]) => registered.tool.getDefinition());
  }

  async getDefinition(toolName: string): Promise<ToolDefinition | undefined> {
    const tool = await this.get(toolName);
    return tool?.getDefinition();
  }

  async enable(toolName: string): Promise<boolean> {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    if (registered) {
      registered.enabled = true;
      logger.debug(`Enabled tool: ${toolName}`);
      return true;
    }
    return false;
  }

  async disable(toolName: string): Promise<boolean> {
    await this.initializeBuiltInTools();
    const registered = this.tools.get(toolName);
    if (registered) {
      registered.enabled = false;
      logger.debug(`Disabled tool: ${toolName}`);
      return true;
    }
    return false;
  }

  async execute(
    toolName: string,
    params: ToolParams,
    context: ToolExecutionContext,
    options: ToolExecutionOptions = {},
  ): Promise<ToolResult> {
    const { timeout = 30000, retries = 0, trackUsage = true } = options;

    const tool = await this.get(toolName);
    if (!tool) {
      throw new ToolExecutionError(
        `Tool not found or disabled: ${toolName}`,
        toolName,
      );
    }

    const startTime = Date.now();
    let lastError: Error | undefined;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        logger.debug(`Executing tool: ${toolName}`, {
          attempt: attempt + 1,
          maxAttempts: retries + 1,
          params,
        });

        const result = await this.executeWithTimeout(
          tool,
          params,
          context,
          timeout,
        );

        const executionTime = Date.now() - startTime;

        if (trackUsage) {
          this.usageTracker.recordExecution(
            toolName,
            result.success,
            executionTime,
          );
        }

        logger.debug(`Tool execution completed: ${toolName}`, {
          success: result.success,
          executionTime,
          attempt: attempt + 1,
        });

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        logger.warn(`Tool execution failed: ${toolName}`, {
          attempt: attempt + 1,
          maxAttempts: retries + 1,
          error: lastError.message,
        });

        if (attempt === retries) {
          break;
        }

        // Wait before retry
        await new Promise(resolve =>
          setTimeout(resolve, Math.pow(2, attempt) * 1000),
        );
      }
    }

    const executionTime = Date.now() - startTime;

    if (trackUsage) {
      this.usageTracker.recordExecution(toolName, false, executionTime);
    }

    throw new ToolExecutionError(
      `Tool execution failed after ${retries + 1} attempts: ${lastError?.message || 'Unknown error'}`,
      toolName,
      { originalError: lastError?.message, attempts: retries + 1 },
    );
  }

  private async executeWithTimeout(
    tool: Tool,
    params: ToolParams,
    context: ToolExecutionContext,
    timeout: number,
  ): Promise<ToolResult> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${timeout}ms`));
      }, timeout);

      tool
        .execute(params, context)
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  getUsageStats(toolName?: string) {
    if (toolName) {
      return this.usageTracker.getStats(toolName);
    }
    return this.usageTracker.getAllStats();
  }

  getTopTools(limit?: number) {
    return this.usageTracker.getTopTools(limit);
  }

  resetUsageStats(): void {
    this.usageTracker.reset();
  }

  async getToolsByCategory(category: string): Promise<string[]> {
    await this.initializeBuiltInTools();
    return Array.from(this.tools.entries())
      .filter(
        ([, registered]) =>
          registered.enabled && registered.category === category,
      )
      .map(([name]) => name);
  }

  async getCategories(): Promise<string[]> {
    await this.initializeBuiltInTools();
    const categories = new Set<string>();
    for (const [, registered] of this.tools) {
      if (registered.enabled && registered.category) {
        categories.add(registered.category);
      }
    }
    return Array.from(categories);
  }

  async validateTool(
    toolName: string,
    params: ToolParams,
  ): Promise<{ valid: boolean; errors: string[] }> {
    const tool = await this.get(toolName);
    if (!tool) {
      return { valid: false, errors: [`Tool not found: ${toolName}`] };
    }

    const definition = tool.getDefinition();
    const errors: string[] = [];

    // Check required parameters
    const required = definition.parameters.required || [];
    for (const requiredParam of required) {
      if (!(requiredParam in params)) {
        errors.push(`Missing required parameter: ${requiredParam}`);
      }
    }

    // Check parameter types (basic validation)
    for (const [paramName, paramValue] of Object.entries(params)) {
      const paramDef = definition.parameters.properties[paramName];
      if (!paramDef) {
        errors.push(`Unknown parameter: ${paramName}`);
        continue;
      }

      // Basic type checking
      if (paramDef.type === 'string' && typeof paramValue !== 'string') {
        errors.push(`Parameter ${paramName} must be a string`);
      } else if (paramDef.type === 'number' && typeof paramValue !== 'number') {
        errors.push(`Parameter ${paramName} must be a number`);
      } else if (
        paramDef.type === 'boolean' &&
        typeof paramValue !== 'boolean'
      ) {
        errors.push(`Parameter ${paramName} must be a boolean`);
      } else if (paramDef.type === 'array' && !Array.isArray(paramValue)) {
        errors.push(`Parameter ${paramName} must be an array`);
      }

      // Check enum values
      if (paramDef.enum && !paramDef.enum.includes(paramValue)) {
        errors.push(
          `Parameter ${paramName} must be one of: ${paramDef.enum.join(', ')}`,
        );
      }
    }

    return { valid: errors.length === 0, errors };
  }

  // Bulk operations
  enableAll(): void {
    for (const [toolName, registered] of this.tools) {
      registered.enabled = true;
    }
    logger.debug('Enabled all tools');
  }

  disableAll(): void {
    for (const [toolName, registered] of this.tools) {
      registered.enabled = false;
    }
    logger.debug('Disabled all tools');
  }

  enableCategory(category: string): void {
    for (const [toolName, registered] of this.tools) {
      if (registered.category === category) {
        registered.enabled = true;
      }
    }
    logger.debug(`Enabled tools in category: ${category}`);
  }

  disableCategory(category: string): void {
    for (const [toolName, registered] of this.tools) {
      if (registered.category === category) {
        registered.enabled = false;
      }
    }
    logger.debug(`Disabled tools in category: ${category}`);
  }
}
