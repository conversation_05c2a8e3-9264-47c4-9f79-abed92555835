/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { Config } from '@arien/arien-cli-core';

interface HeaderProps {
  config: Config;
  settings: Config; // Now using unified Config instead of LoadedSettings
  onShowHelp?: () => void;
  onShowTheme?: () => void;
  onShowAuth?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  config,
  settings,
  onShowHelp,
  onShowTheme,
  onShowAuth,
}) => {
  const modelName = config.getModelName() || 'Unknown';
  const authType = settings.getSelectedAuthType() || 'none';
  const sandboxEnabled = Boolean(settings.getSandbox());

  return (
    <Box flexDirection="column" marginBottom={1}>
      {/* Title bar */}
      <Box justifyContent="space-between" alignItems="center">
        <Box alignItems="center">
          <Text color={Colors.AccentBlue} bold>
            Arien CLI
          </Text>
          <Text color={Colors.Gray} dimColor>
            {' '}v1.0.0
          </Text>
        </Box>
        <Box alignItems="center">
          <Text color={Colors.Gray} dimColor>
            Model: 
          </Text>
          <Text color={Colors.AccentGreen}>
            {' '}{modelName}
          </Text>
        </Box>
      </Box>

      {/* Status bar */}
      <Box justifyContent="space-between" alignItems="center">
        <Box alignItems="center">
          <Text color={Colors.Gray} dimColor>
            Auth: 
          </Text>
          <Text color={authType === 'none' ? Colors.AccentRed : Colors.AccentGreen}>
            {' '}{authType}
          </Text>
          {sandboxEnabled && (
            <>
              <Text color={Colors.Gray} dimColor>
                {' '}| Sandbox: 
              </Text>
              <Text color={Colors.AccentYellow}>
                {' '}enabled
              </Text>
            </>
          )}
        </Box>
        <Box alignItems="center">
          <Text color={Colors.Gray} dimColor>
            F1: Help | F2: Theme | F3: Auth
          </Text>
        </Box>
      </Box>

      {/* Separator */}
      <Box>
        <Text color={Colors.Gray}>
          {'─'.repeat(80)}
        </Text>
      </Box>
    </Box>
  );
};
