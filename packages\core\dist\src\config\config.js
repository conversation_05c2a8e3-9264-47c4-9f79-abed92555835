/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import * as fs from "fs";
import * as path from "path";
import { homedir } from "os";
import {
  createContentGenerator,
  createContentGeneratorConfig
} from "../core/contentGenerator.js";
import { FileDiscoveryService } from "../services/fileDiscoveryService.js";
import { GitService } from "../services/gitService.js";
import { ToolRegistry } from "../tools/tool-registry.js";
import { DEFAULT_GEMINI_MODEL } from "./models.js";
import { getOauthClient } from "../code_assist/oauth2.js";
import stripJsonComments from "strip-json-comments";
const SETTINGS_DIRECTORY_NAME = ".arien";
const USER_SETTINGS_DIR = path.join(homedir(), SETTINGS_DIRECTORY_NAME);
const USER_SETTINGS_PATH = path.join(USER_SETTINGS_DIR, "settings.json");
var AuthType = /* @__PURE__ */ ((AuthType2) => {
  AuthType2["LOGIN_WITH_GOOGLE"] = "oauth-personal";
  AuthType2["USE_GEMINI"] = "gemini-api-key";
  AuthType2["USE_VERTEX_AI"] = "vertex-ai";
  return AuthType2;
})(AuthType || {});
var ApprovalMode = /* @__PURE__ */ ((ApprovalMode2) => {
  ApprovalMode2["ALWAYS"] = "always";
  ApprovalMode2["NEVER"] = "never";
  ApprovalMode2["ONCE"] = "once";
  return ApprovalMode2;
})(ApprovalMode || {});
var TelemetryTarget = /* @__PURE__ */ ((TelemetryTarget2) => {
  TelemetryTarget2["CONSOLE"] = "console";
  TelemetryTarget2["OTLP"] = "otlp";
  return TelemetryTarget2;
})(TelemetryTarget || {});
var SettingScope = /* @__PURE__ */ ((SettingScope2) => {
  SettingScope2["User"] = "User";
  SettingScope2["Workspace"] = "Workspace";
  return SettingScope2;
})(SettingScope || {});
class Config {
  options;
  contentGenerator;
  fileService;
  gitService;
  toolRegistry;
  userSettingsFile;
  workspaceSettingsFile;
  settingsErrors = [];
  constructor(options = {}) {
    this.options = {
      model: DEFAULT_GEMINI_MODEL,
      debug: false,
      allFiles: false,
      workspaceRoot: process.cwd(),
      approvalMode: "once" /* ONCE */,
      sandbox: false,
      coreTools: [],
      excludeTools: [],
      contextFileName: "ARIEN.md",
      checkpointingEnabled: true,
      ...options
    };
  }
  /**
   * Create a Config instance with hierarchical settings loading
   */
  static async createWithSettings(workspaceRoot, additionalOptions = {}) {
    const config = new Config({ workspaceRoot: workspaceRoot || process.cwd(), ...additionalOptions });
    await config.loadSettings();
    return config;
  }
  /**
   * Load settings from user and workspace settings files
   */
  async loadSettings() {
    const errors = [];
    this.userSettingsFile = this.loadSettingsFile(USER_SETTINGS_PATH, errors);
    const workspaceSettingsPath = path.join(this.getWorkspaceRoot(), SETTINGS_DIRECTORY_NAME, "settings.json");
    this.workspaceSettingsFile = this.loadSettingsFile(workspaceSettingsPath, errors);
    this.settingsErrors = errors;
    const mergedSettings = {
      ...this.userSettingsFile.settings,
      ...this.workspaceSettingsFile.settings
    };
    this.options = { ...this.options, ...mergedSettings };
  }
  /**
   * Load a settings file
   */
  loadSettingsFile(filePath, errors) {
    const defaultSettings = {};
    if (!fs.existsSync(filePath)) {
      return { settings: defaultSettings, path: filePath };
    }
    try {
      const content = fs.readFileSync(filePath, "utf8");
      const cleanedContent = stripJsonComments(content);
      const settings = JSON.parse(cleanedContent);
      return { settings, path: filePath };
    } catch (error) {
      errors.push({
        message: `Failed to parse settings file: ${error instanceof Error ? error.message : String(error)}`,
        path: filePath
      });
      return { settings: defaultSettings, path: filePath };
    }
  }
  /**
   * Set a configuration value and optionally persist it to settings
   */
  setValue(scope, key, value) {
    this.options[key] = value;
    const targetFile = scope === "User" /* User */ ? this.userSettingsFile : this.workspaceSettingsFile;
    if (targetFile) {
      targetFile.settings[key] = value;
      try {
        fs.mkdirSync(path.dirname(targetFile.path), { recursive: true });
        fs.writeFileSync(
          targetFile.path,
          JSON.stringify(targetFile.settings, null, 2)
        );
      } catch (error) {
        console.error(`Failed to save settings to ${targetFile.path}:`, error);
      }
    }
  }
  /**
   * Get a configuration value
   */
  getValue(key) {
    return this.options[key];
  }
  /**
   * Get settings errors from loading
   */
  getSettingsErrors() {
    return this.settingsErrors;
  }
  /**
   * Create default user settings if they don't exist
   */
  static createUserSettingsIfNotExists() {
    if (!fs.existsSync(USER_SETTINGS_PATH)) {
      const defaultSettings = {
        theme: "default-dark",
        autoConfigureMaxOldSpaceSize: true,
        fileFiltering: {
          respectGitIgnore: true,
          enableRecursiveFileSearch: true
        },
        telemetry: {
          enabled: true
        },
        checkpointing: {
          enabled: true
        },
        accessibility: {
          disableLoadingPhrases: false
        }
      };
      try {
        fs.mkdirSync(path.dirname(USER_SETTINGS_PATH), { recursive: true });
        fs.writeFileSync(
          USER_SETTINGS_PATH,
          JSON.stringify(defaultSettings, null, 2)
        );
      } catch (error) {
        console.error("Failed to create default user settings:", error);
      }
    }
  }
  // Getters
  getModel() {
    return this.options.model || DEFAULT_GEMINI_MODEL;
  }
  getDebugMode() {
    return this.options.debug || this.options.debugMode || false;
  }
  getDebug() {
    return this.getDebugMode();
  }
  getAllFiles() {
    return this.options.allFiles || this.options.includeAllFiles || false;
  }
  getQuestion() {
    return this.options.question || "";
  }
  getWorkspaceRoot() {
    return this.options.workspaceRoot || process.cwd();
  }
  getApprovalMode() {
    return this.options.approvalMode || "once" /* ONCE */;
  }
  getSandbox() {
    return this.options.sandbox || false;
  }
  getCoreTools() {
    return this.options.coreTools || [];
  }
  getExcludeTools() {
    return this.options.excludeTools || [];
  }
  getToolDiscoveryCommand() {
    return this.options.toolDiscoveryCommand;
  }
  getToolCallCommand() {
    return this.options.toolCallCommand;
  }
  getMcpServerCommand() {
    return this.options.mcpServerCommand;
  }
  getMcpServers() {
    return this.options.mcpServers || {};
  }
  getContextFileName() {
    return this.options.contextFileName || "ARIEN.md";
  }
  getCheckpointingEnabled() {
    return this.options.checkpointingEnabled !== false;
  }
  getUserMemory() {
    return this.options.userMemory || "";
  }
  getContentGeneratorConfig() {
    return this.options.contentGeneratorConfig;
  }
  getTelemetrySettings() {
    const base = this.options.telemetrySettings || { enabled: false };
    return {
      ...base,
      enabled: base.enabled ?? this.options.telemetryEnabled ?? false,
      target: base.target ?? this.options.telemetryTarget,
      otlpEndpoint: base.otlpEndpoint ?? this.options.telemetryOtlpEndpoint,
      logPrompts: base.logPrompts ?? this.options.telemetryLogPrompts
    };
  }
  getBugCommandSettings() {
    return this.options.bugCommandSettings || this.options.bugCommand || { enabled: false };
  }
  // Additional getters for Settings properties
  getTheme() {
    return this.options.theme;
  }
  getSelectedAuthType() {
    return this.options.selectedAuthType;
  }
  getEmbeddingModel() {
    return this.options.embeddingModel;
  }
  getShowMemoryUsage() {
    return this.options.showMemoryUsage || false;
  }
  getIncludeTools() {
    return this.options.includeTools || [];
  }
  getGeminiMdFilename() {
    return this.options.geminiMdFilename;
  }
  getAccessibilitySettings() {
    return this.options.accessibility || { disableLoadingPhrases: false };
  }
  getUsageStatisticsEnabled() {
    return this.options.usageStatisticsEnabled || false;
  }
  getPreferredEditor() {
    return this.options.preferredEditor;
  }
  getCheckpointingSettings() {
    return this.options.checkpointing || { enabled: true };
  }
  getAutoConfigureMaxOldSpaceSize() {
    return this.options.autoConfigureMaxOldSpaceSize || false;
  }
  getFileFiltering() {
    return this.options.fileFiltering || { respectGitIgnore: true, enableRecursiveFileSearch: true };
  }
  getHideWindowTitle() {
    return this.options.hideWindowTitle || false;
  }
  getHideTips() {
    return this.options.hideTips || false;
  }
  getSessionId() {
    return this.options.sessionId;
  }
  // Service getters with lazy initialization
  async getContentGenerator() {
    if (!this.contentGenerator) {
      const config = this.getContentGeneratorConfig();
      if (!config) {
        throw new Error("No content generator configuration found");
      }
      const generatorConfig = await createContentGeneratorConfig(
        this.getModel(),
        config.apiKey,
        config.authType
        // Type conversion needed due to enum mismatch
      );
      this.contentGenerator = await createContentGenerator(generatorConfig);
    }
    return this.contentGenerator;
  }
  getFileService() {
    if (!this.fileService) {
      this.fileService = new FileDiscoveryService(this);
    }
    return this.fileService;
  }
  getGitService() {
    if (!this.gitService) {
      this.gitService = new GitService(this);
    }
    return this.gitService;
  }
  getToolRegistry() {
    if (!this.toolRegistry) {
      this.toolRegistry = new ToolRegistry(this);
    }
    return this.toolRegistry;
  }
  // Setters
  setModel(model) {
    this.options.model = model;
  }
  setDebugMode(debug) {
    this.options.debug = debug;
  }
  setApprovalMode(mode) {
    this.options.approvalMode = mode;
  }
  setUserMemory(memory) {
    this.options.userMemory = memory;
  }
  setContentGeneratorConfig(config) {
    this.options.contentGeneratorConfig = config;
    this.contentGenerator = void 0;
  }
  setTelemetrySettings(settings) {
    this.options.telemetrySettings = settings;
  }
  setBugCommandSettings(settings) {
    this.options.bugCommandSettings = settings;
  }
  setMcpServers(servers) {
    this.options.mcpServers = servers;
  }
  setContextFileName(fileName) {
    this.options.contextFileName = fileName;
  }
  setCheckpointingEnabled(enabled) {
    this.options.checkpointingEnabled = enabled;
  }
  // Additional setters for Settings properties
  setTheme(theme) {
    this.options.theme = theme;
  }
  setSelectedAuthType(authType) {
    this.options.selectedAuthType = authType;
  }
  setEmbeddingModel(model) {
    this.options.embeddingModel = model;
  }
  setIncludeAllFiles(include) {
    this.options.includeAllFiles = include;
    this.options.allFiles = include;
  }
  setShowMemoryUsage(show) {
    this.options.showMemoryUsage = show;
  }
  setTelemetryEnabled(enabled) {
    this.options.telemetryEnabled = enabled;
    if (!this.options.telemetrySettings) {
      this.options.telemetrySettings = {};
    }
    this.options.telemetrySettings.enabled = enabled;
  }
  setTelemetryTarget(target) {
    this.options.telemetryTarget = target;
  }
  setTelemetryOtlpEndpoint(endpoint) {
    this.options.telemetryOtlpEndpoint = endpoint;
  }
  setTelemetryLogPrompts(log) {
    this.options.telemetryLogPrompts = log;
  }
  setIncludeTools(tools) {
    this.options.includeTools = tools;
  }
  setGeminiMdFilename(filename) {
    this.options.geminiMdFilename = filename;
  }
  setAccessibilitySettings(settings) {
    this.options.accessibility = settings;
  }
  setUsageStatisticsEnabled(enabled) {
    this.options.usageStatisticsEnabled = enabled;
  }
  setPreferredEditor(editor) {
    this.options.preferredEditor = editor;
  }
  setCheckpointingSettings(settings) {
    this.options.checkpointing = settings;
  }
  setAutoConfigureMaxOldSpaceSize(configure) {
    this.options.autoConfigureMaxOldSpaceSize = configure;
  }
  setFileFiltering(settings) {
    this.options.fileFiltering = settings;
  }
  setHideWindowTitle(hide) {
    this.options.hideWindowTitle = hide;
  }
  setHideTips(hide) {
    this.options.hideTips = hide;
  }
  setSessionId(sessionId2) {
    this.options.sessionId = sessionId2;
  }
  // Convenience setters that handle both old and new property names
  setQuestion(question) {
    this.options.question = question;
  }
  setSandbox(sandbox) {
    this.options.sandbox = sandbox;
  }
  setMemory(memory) {
    this.options.userMemory = memory;
  }
  // Utility methods
  isInteractive() {
    return process.stdin.isTTY && !this.getQuestion();
  }
  shouldUseAllFiles() {
    return this.getAllFiles();
  }
  shouldUseSandbox() {
    return Boolean(this.getSandbox());
  }
  clone() {
    return new Config({ ...this.options });
  }
  /**
   * Refresh authentication based on the selected auth type
   * This method handles OAuth2 authentication for LOGIN_WITH_GOOGLE
   * and validates environment variables for other auth types
   */
  async refreshAuth(authType) {
    switch (authType) {
      case "oauth-personal" /* LOGIN_WITH_GOOGLE */:
        try {
          await getOauthClient();
        } catch (error) {
          throw new Error(`OAuth2 authentication failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        break;
      case "gemini-api-key" /* USE_GEMINI */:
        if (!process.env.GEMINI_API_KEY) {
          throw new Error("GEMINI_API_KEY environment variable not found. Add that to your .env and try again, no reload needed!");
        }
        break;
      case "vertex-ai" /* USE_VERTEX_AI */:
        const hasVertexProjectLocationConfig = !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;
        const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
        if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {
          throw new Error(
            "Must specify GOOGLE_GENAI_USE_VERTEXAI=true and either:\n\u2022 GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\n\u2022 GOOGLE_API_KEY environment variable (if using express mode).\nUpdate your .env and try again, no reload needed!"
          );
        }
        break;
      default:
        throw new Error(`Invalid auth type: ${authType}`);
    }
  }
}
import { sessionId } from "../utils/session.js";
function logUserPrompt(prompt) {
  if (process.env.DEBUG) {
    console.log(`[${sessionId}] User prompt: ${prompt}`);
  }
}
function getErrorMessage(error) {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}
export {
  ApprovalMode,
  AuthType,
  Config,
  SETTINGS_DIRECTORY_NAME,
  SettingScope,
  TelemetryTarget,
  USER_SETTINGS_DIR,
  USER_SETTINGS_PATH,
  getErrorMessage,
  logUserPrompt,
  sessionId
};
//# sourceMappingURL=config.js.map
