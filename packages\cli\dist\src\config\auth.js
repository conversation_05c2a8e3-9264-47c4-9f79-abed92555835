/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { AuthType } from "@arien/arien-cli-core";
const mapAuthMethodToAuthType = (method) => {
  switch (method) {
    case "google":
      return AuthType.LOGIN_WITH_GOOGLE;
    case "api_key":
      return AuthType.USE_GEMINI;
    case "vertex":
      return AuthType.USE_VERTEX_AI;
    default:
      return AuthType.USE_GEMINI;
  }
};
const mapAuthTypeToAuthMethod = (authType) => {
  switch (authType) {
    case AuthType.LOGIN_WITH_GOOGLE:
      return "google";
    case AuthType.USE_GEMINI:
      return "api_key";
    case AuthType.USE_VERTEX_AI:
      return "vertex";
    default:
      return "api_key";
  }
};
const validateAuthMethod = (authMethod) => {
  if (authMethod === AuthType.LOGIN_WITH_GOOGLE) {
    return null;
  }
  if (authMethod === AuthType.USE_GEMINI) {
    if (!process.env.GEMINI_API_KEY) {
      return "GEMINI_API_KEY environment variable not found. Add that to your .env and try again, no reload needed!";
    }
    return null;
  }
  if (authMethod === AuthType.USE_VERTEX_AI) {
    const hasVertexProjectLocationConfig = !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;
    const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
    if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {
      return "Must specify GOOGLE_GENAI_USE_VERTEXAI=true and either:\n\u2022 GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\n\u2022 GOOGLE_API_KEY environment variable (if using express mode).\nUpdate your .env and try again, no reload needed!";
    }
    return null;
  }
  return "Invalid auth method selected.";
};
export {
  mapAuthMethodToAuthType,
  mapAuthTypeToAuthMethod,
  validateAuthMethod
};
//# sourceMappingURL=auth.js.map
