{"version": 3, "sources": ["../../../src/hooks/useChat.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { useState, useCallback, useRef, useEffect } from 'react';\nimport {\n  Config,\n  ContentGenerator,\n  Turn,\n  createContentGenerator,\n  createContentGeneratorConfig,\n  AuthType,\n} from '@arien/arien-cli-core';\n\nexport interface ChatMessage {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: Date;\n  turn?: Turn;\n  error?: string;\n}\n\nexport interface UseChatReturn {\n  messages: ChatMessage[];\n  isGenerating: boolean;\n  sendMessage: (content: string) => Promise<void>;\n  clearChat: () => void;\n  error?: string;\n}\n\nexport const useChat = (config: Config): UseChatReturn => {\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n\n  const contentGeneratorRef = useRef<ContentGenerator | null>(null);\n  const turnCounterRef = useRef(0);\n\n  // Initialize content generator\n  useEffect(() => {\n    const initializeGenerator = async () => {\n      try {\n        const configData = config.getContentGeneratorConfig();\n        if (!configData) {\n          throw new Error('No content generator configuration found');\n        }\n\n        // Use the authType directly since it's now using the unified enum values\n        const mappedAuthType: AuthType = configData.authType || AuthType.USE_GEMINI;\n\n        const generatorConfig = await createContentGeneratorConfig(\n          config.getModel(),\n          configData.apiKey,\n          mappedAuthType,\n        );\n\n        const generator = await createContentGenerator(generatorConfig);\n        contentGeneratorRef.current = generator;\n        setError(undefined);\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error\n            ? err.message\n            : 'Failed to initialize content generator';\n        setError(errorMessage);\n        console.error('Failed to initialize content generator:', err);\n      }\n    };\n\n    initializeGenerator();\n  }, [config]);\n\n  const generateId = useCallback(() => {\n    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n  }, []);\n\n  const sendMessage = useCallback(\n    async (content: string) => {\n      if (!content.trim() || isGenerating) {\n        return;\n      }\n\n      if (!contentGeneratorRef.current) {\n        setError('Content generator not initialized');\n        return;\n      }\n\n      // Create user message\n      const userMessage: ChatMessage = {\n        id: generateId(),\n        role: 'user',\n        content: content.trim(),\n        timestamp: new Date(),\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setIsGenerating(true);\n      setError(undefined);\n\n      try {\n        // Create a new turn\n        const turn = new Turn();\n        turnCounterRef.current += 1;\n\n        // Generate response\n        const response = await contentGeneratorRef.current.generateContent({\n          model: config.getModel(),\n          contents: [\n            {\n              role: 'user',\n              parts: [{ text: content.trim() }],\n            },\n          ],\n        });\n\n        if (response.candidates && response.candidates.length > 0) {\n          const candidate = response.candidates[0];\n          const responseText =\n            candidate.content?.parts\n              ?.map((part: any) => ('text' in part ? part.text : ''))\n              .join('') || 'No response generated';\n\n          // Create assistant message\n          const assistantMessage: ChatMessage = {\n            id: generateId(),\n            role: 'assistant',\n            content: responseText,\n            timestamp: new Date(),\n            turn,\n          };\n\n          setMessages(prev => [...prev, assistantMessage]);\n        } else {\n          throw new Error('No response candidates received');\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : 'Failed to generate response';\n        setError(errorMessage);\n\n        // Add error message to chat\n        const errorChatMessage: ChatMessage = {\n          id: generateId(),\n          role: 'assistant',\n          content: `❌ Error: ${errorMessage}`,\n          timestamp: new Date(),\n          error: errorMessage,\n        };\n\n        setMessages(prev => [...prev, errorChatMessage]);\n      } finally {\n        setIsGenerating(false);\n      }\n    },\n    [isGenerating, generateId],\n  );\n\n  const clearChat = useCallback(() => {\n    setMessages([]);\n    setError(undefined);\n    turnCounterRef.current = 0;\n  }, []);\n\n  return {\n    messages,\n    isGenerating,\n    sendMessage,\n    clearChat,\n    error,\n  };\n};\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,UAAU,aAAa,QAAQ,iBAAiB;AACzD;AAAA,EAGE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAmBA,MAAM,UAAU,CAAC,WAAkC;AACxD,QAAM,CAAC,UAAU,WAAW,IAAI,SAAwB,CAAC,CAAC;AAC1D,QAAM,CAAC,cAAc,eAAe,IAAI,SAAS,KAAK;AACtD,QAAM,CAAC,OAAO,QAAQ,IAAI,SAA6B;AAEvD,QAAM,sBAAsB,OAAgC,IAAI;AAChE,QAAM,iBAAiB,OAAO,CAAC;AAG/B,YAAU,MAAM;AACd,UAAM,sBAAsB,YAAY;AACtC,UAAI;AACF,cAAM,aAAa,OAAO,0BAA0B;AACpD,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM,0CAA0C;AAAA,QAC5D;AAGA,cAAM,iBAA2B,WAAW,YAAY,SAAS;AAEjE,cAAM,kBAAkB,MAAM;AAAA,UAC5B,OAAO,SAAS;AAAA,UAChB,WAAW;AAAA,UACX;AAAA,QACF;AAEA,cAAM,YAAY,MAAM,uBAAuB,eAAe;AAC9D,4BAAoB,UAAU;AAC9B,iBAAS,MAAS;AAAA,MACpB,SAAS,KAAK;AACZ,cAAM,eACJ,eAAe,QACX,IAAI,UACJ;AACN,iBAAS,YAAY;AACrB,gBAAQ,MAAM,2CAA2C,GAAG;AAAA,MAC9D;AAAA,IACF;AAEA,wBAAoB;AAAA,EACtB,GAAG,CAAC,MAAM,CAAC;AAEX,QAAM,aAAa,YAAY,MAAM;AACnC,WAAO,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC;AAAA,EACzE,GAAG,CAAC,CAAC;AAEL,QAAM,cAAc;AAAA,IAClB,OAAO,YAAoB;AACzB,UAAI,CAAC,QAAQ,KAAK,KAAK,cAAc;AACnC;AAAA,MACF;AAEA,UAAI,CAAC,oBAAoB,SAAS;AAChC,iBAAS,mCAAmC;AAC5C;AAAA,MACF;AAGA,YAAM,cAA2B;AAAA,QAC/B,IAAI,WAAW;AAAA,QACf,MAAM;AAAA,QACN,SAAS,QAAQ,KAAK;AAAA,QACtB,WAAW,oBAAI,KAAK;AAAA,MACtB;AAEA,kBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAC1C,sBAAgB,IAAI;AACpB,eAAS,MAAS;AAElB,UAAI;AAEF,cAAM,OAAO,IAAI,KAAK;AACtB,uBAAe,WAAW;AAG1B,cAAM,WAAW,MAAM,oBAAoB,QAAQ,gBAAgB;AAAA,UACjE,OAAO,OAAO,SAAS;AAAA,UACvB,UAAU;AAAA,YACR;AAAA,cACE,MAAM;AAAA,cACN,OAAO,CAAC,EAAE,MAAM,QAAQ,KAAK,EAAE,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,QACF,CAAC;AAED,YAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACzD,gBAAM,YAAY,SAAS,WAAW,CAAC;AACvC,gBAAM,eACJ,UAAU,SAAS,OACf,IAAI,CAAC,SAAe,UAAU,OAAO,KAAK,OAAO,EAAG,EACrD,KAAK,EAAE,KAAK;AAGjB,gBAAM,mBAAgC;AAAA,YACpC,IAAI,WAAW;AAAA,YACf,MAAM;AAAA,YACN,SAAS;AAAA,YACT,WAAW,oBAAI,KAAK;AAAA,YACpB;AAAA,UACF;AAEA,sBAAY,UAAQ,CAAC,GAAG,MAAM,gBAAgB,CAAC;AAAA,QACjD,OAAO;AACL,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AAAA,MACF,SAAS,KAAK;AACZ,cAAM,eACJ,eAAe,QAAQ,IAAI,UAAU;AACvC,iBAAS,YAAY;AAGrB,cAAM,mBAAgC;AAAA,UACpC,IAAI,WAAW;AAAA,UACf,MAAM;AAAA,UACN,SAAS,iBAAY,YAAY;AAAA,UACjC,WAAW,oBAAI,KAAK;AAAA,UACpB,OAAO;AAAA,QACT;AAEA,oBAAY,UAAQ,CAAC,GAAG,MAAM,gBAAgB,CAAC;AAAA,MACjD,UAAE;AACA,wBAAgB,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,CAAC,cAAc,UAAU;AAAA,EAC3B;AAEA,QAAM,YAAY,YAAY,MAAM;AAClC,gBAAY,CAAC,CAAC;AACd,aAAS,MAAS;AAClB,mBAAe,UAAU;AAAA,EAC3B,GAAG,CAAC,CAAC;AAEL,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}