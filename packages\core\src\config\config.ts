/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import { homedir } from 'os';
import { GoogleAuth } from 'google-auth-library';
import {
  ContentGenerator,
  createContentGenerator,
  createContentGeneratorConfig,
} from '../core/contentGenerator.js';
import { FileDiscoveryService } from '../services/fileDiscoveryService.js';
import { GitService } from '../services/gitService.js';
import { ToolRegistry } from '../tools/tool-registry.js';
import { DEFAULT_GEMINI_MODEL } from './models.js';
import { getOauthClient } from '../code_assist/oauth2.js';
import stripJsonComments from 'strip-json-comments';

// Settings constants
export const SETTINGS_DIRECTORY_NAME = '.arien';
export const USER_SETTINGS_DIR = path.join(homedir(), SETTINGS_DIRECTORY_NAME);
export const USER_SETTINGS_PATH = path.join(USER_SETTINGS_DIR, 'settings.json');

export enum AuthType {
  LOGIN_WITH_GOOGLE = 'oauth-personal',
  USE_GEMINI = 'gemini-api-key',
  USE_VERTEX_AI = 'vertex-ai',
}

export enum ApprovalMode {
  ALWAYS = 'always',
  NEVER = 'never',
  ONCE = 'once',
}

export enum TelemetryTarget {
  CONSOLE = 'console',
  OTLP = 'otlp',
}

export enum SettingScope {
  User = 'User',
  Workspace = 'Workspace',
}

export interface MCPServerConfig {
  command: string;
  args?: string[];
  env?: Record<string, string>;
}

export interface BugCommandSettings {
  enabled?: boolean;
  command?: string;
}

export interface TelemetrySettings {
  enabled?: boolean;
  endpoint?: string;
  target?: TelemetryTarget;
  otlpEndpoint?: string;
  logPrompts?: boolean;
}

export interface AccessibilitySettings {
  disableLoadingPhrases?: boolean;
}

export interface CheckpointingSettings {
  enabled?: boolean;
}

export interface SettingsError {
  message: string;
  path: string;
}

export interface SettingsFile {
  settings: ConfigOptions;
  path: string;
}

export interface ContentGeneratorConfig {
  authType?: AuthType;
  apiKey?: string;
  projectId?: string;
  location?: string;
  credentials?: any;
}

export interface ConfigOptions {
  // Core configuration
  model?: string;
  debug?: boolean;
  allFiles?: boolean;
  question?: string;
  workspaceRoot?: string;
  approvalMode?: ApprovalMode;
  sandbox?: boolean | string;
  coreTools?: string[];
  excludeTools?: string[];
  toolDiscoveryCommand?: string;
  toolCallCommand?: string;
  mcpServerCommand?: string;
  mcpServers?: Record<string, MCPServerConfig>;
  contextFileName?: string | string[];
  checkpointingEnabled?: boolean;
  userMemory?: string;
  contentGeneratorConfig?: ContentGeneratorConfig;
  telemetrySettings?: TelemetrySettings;
  bugCommandSettings?: BugCommandSettings;

  // Extended settings from Settings interface
  theme?: string;
  selectedAuthType?: AuthType;
  embeddingModel?: string;
  debugMode?: boolean; // Alias for debug
  includeAllFiles?: boolean; // Alias for allFiles
  showMemoryUsage?: boolean;
  telemetryEnabled?: boolean;
  telemetryTarget?: TelemetryTarget;
  telemetryOtlpEndpoint?: string;
  telemetryLogPrompts?: boolean;
  includeTools?: string[];
  geminiMdFilename?: string;
  accessibility?: AccessibilitySettings;
  telemetry?: TelemetrySettings;
  usageStatisticsEnabled?: boolean;
  preferredEditor?: string;
  bugCommand?: BugCommandSettings;
  checkpointing?: CheckpointingSettings;
  autoConfigureMaxOldSpaceSize?: boolean;
  fileFiltering?: {
    respectGitIgnore?: boolean;
    enableRecursiveFileSearch?: boolean;
  };
  hideWindowTitle?: boolean;
  hideTips?: boolean;

  // Session-specific options
  sessionId?: string;
}

export class Config {
  private options: ConfigOptions;
  private contentGenerator?: ContentGenerator;
  private fileService?: FileDiscoveryService;
  private gitService?: GitService;
  private toolRegistry?: ToolRegistry;
  private userSettingsFile?: SettingsFile;
  private workspaceSettingsFile?: SettingsFile;
  private settingsErrors: SettingsError[] = [];

  constructor(options: ConfigOptions = {}) {
    this.options = {
      model: DEFAULT_GEMINI_MODEL,
      debug: false,
      allFiles: false,
      workspaceRoot: process.cwd(),
      approvalMode: ApprovalMode.ONCE,
      sandbox: false,
      coreTools: [],
      excludeTools: [],
      contextFileName: 'ARIEN.md',
      checkpointingEnabled: true,
      ...options,
    };
  }

  /**
   * Create a Config instance with hierarchical settings loading
   */
  static async createWithSettings(workspaceRoot?: string, additionalOptions: ConfigOptions = {}): Promise<Config> {
    const config = new Config({ workspaceRoot: workspaceRoot || process.cwd(), ...additionalOptions });
    await config.loadSettings();
    return config;
  }

  /**
   * Load settings from user and workspace settings files
   */
  private async loadSettings(): Promise<void> {
    const errors: SettingsError[] = [];

    // Load user settings
    this.userSettingsFile = this.loadSettingsFile(USER_SETTINGS_PATH, errors);

    // Load workspace settings
    const workspaceSettingsPath = path.join(this.getWorkspaceRoot(), SETTINGS_DIRECTORY_NAME, 'settings.json');
    this.workspaceSettingsFile = this.loadSettingsFile(workspaceSettingsPath, errors);

    this.settingsErrors = errors;

    // Merge settings into options (workspace overrides user)
    const mergedSettings = {
      ...this.userSettingsFile.settings,
      ...this.workspaceSettingsFile.settings,
    };

    // Apply merged settings to options
    this.options = { ...this.options, ...mergedSettings };
  }

  /**
   * Load a settings file
   */
  private loadSettingsFile(filePath: string, errors: SettingsError[]): SettingsFile {
    const defaultSettings: ConfigOptions = {};

    if (!fs.existsSync(filePath)) {
      return { settings: defaultSettings, path: filePath };
    }

    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const cleanedContent = stripJsonComments(content);
      const settings = JSON.parse(cleanedContent) as ConfigOptions;
      return { settings, path: filePath };
    } catch (error) {
      errors.push({
        message: `Failed to parse settings file: ${error instanceof Error ? error.message : String(error)}`,
        path: filePath,
      });
      return { settings: defaultSettings, path: filePath };
    }
  }

  /**
   * Set a configuration value and optionally persist it to settings
   */
  setValue(scope: SettingScope, key: keyof ConfigOptions, value: any): void {
    // Update runtime options
    (this.options as any)[key] = value;

    // Update appropriate settings file
    const targetFile = scope === SettingScope.User ? this.userSettingsFile : this.workspaceSettingsFile;
    if (targetFile) {
      (targetFile.settings as any)[key] = value;

      // Write to file
      try {
        fs.mkdirSync(path.dirname(targetFile.path), { recursive: true });
        fs.writeFileSync(
          targetFile.path,
          JSON.stringify(targetFile.settings, null, 2),
        );
      } catch (error) {
        console.error(`Failed to save settings to ${targetFile.path}:`, error);
      }
    }
  }

  /**
   * Get a configuration value
   */
  getValue(key: keyof ConfigOptions): any {
    return (this.options as any)[key];
  }

  /**
   * Get settings errors from loading
   */
  getSettingsErrors(): SettingsError[] {
    return this.settingsErrors;
  }

  /**
   * Create default user settings if they don't exist
   */
  static createUserSettingsIfNotExists(): void {
    if (!fs.existsSync(USER_SETTINGS_PATH)) {
      const defaultSettings: ConfigOptions = {
        theme: 'default-dark',
        autoConfigureMaxOldSpaceSize: true,
        fileFiltering: {
          respectGitIgnore: true,
          enableRecursiveFileSearch: true,
        },
        telemetry: {
          enabled: true,
        },
        checkpointing: {
          enabled: true,
        },
        accessibility: {
          disableLoadingPhrases: false,
        },
      };

      try {
        fs.mkdirSync(path.dirname(USER_SETTINGS_PATH), { recursive: true });
        fs.writeFileSync(
          USER_SETTINGS_PATH,
          JSON.stringify(defaultSettings, null, 2),
        );
      } catch (error) {
        console.error('Failed to create default user settings:', error);
      }
    }
  }

  // Getters
  getModel(): string {
    return this.options.model || DEFAULT_GEMINI_MODEL;
  }

  getDebugMode(): boolean {
    // Handle both debug and debugMode properties (Settings compatibility)
    return this.options.debug || this.options.debugMode || false;
  }

  getDebug(): boolean {
    return this.getDebugMode();
  }

  getAllFiles(): boolean {
    // Handle both allFiles and includeAllFiles properties (Settings compatibility)
    return this.options.allFiles || this.options.includeAllFiles || false;
  }

  getQuestion(): string {
    return this.options.question || '';
  }

  getWorkspaceRoot(): string {
    return this.options.workspaceRoot || process.cwd();
  }

  getApprovalMode(): ApprovalMode {
    return this.options.approvalMode || ApprovalMode.ONCE;
  }

  getSandbox(): boolean | string {
    return this.options.sandbox || false;
  }

  getCoreTools(): string[] {
    return this.options.coreTools || [];
  }

  getExcludeTools(): string[] {
    return this.options.excludeTools || [];
  }

  getToolDiscoveryCommand(): string | undefined {
    return this.options.toolDiscoveryCommand;
  }

  getToolCallCommand(): string | undefined {
    return this.options.toolCallCommand;
  }

  getMcpServerCommand(): string | undefined {
    return this.options.mcpServerCommand;
  }

  getMcpServers(): Record<string, MCPServerConfig> {
    return this.options.mcpServers || {};
  }

  getContextFileName(): string | string[] {
    return this.options.contextFileName || 'ARIEN.md';
  }

  getCheckpointingEnabled(): boolean {
    return this.options.checkpointingEnabled !== false;
  }

  getUserMemory(): string {
    return this.options.userMemory || '';
  }

  getContentGeneratorConfig(): ContentGeneratorConfig | undefined {
    return this.options.contentGeneratorConfig;
  }

  getTelemetrySettings(): TelemetrySettings {
    // Merge telemetrySettings with individual telemetry properties for Settings compatibility
    const base = this.options.telemetrySettings || { enabled: false };
    return {
      ...base,
      enabled: base.enabled ?? this.options.telemetryEnabled ?? false,
      target: base.target ?? this.options.telemetryTarget,
      otlpEndpoint: base.otlpEndpoint ?? this.options.telemetryOtlpEndpoint,
      logPrompts: base.logPrompts ?? this.options.telemetryLogPrompts,
    };
  }

  getBugCommandSettings(): BugCommandSettings {
    return this.options.bugCommandSettings || this.options.bugCommand || { enabled: false };
  }

  // Additional getters for Settings properties
  getTheme(): string | undefined {
    return this.options.theme;
  }

  getSelectedAuthType(): AuthType | undefined {
    return this.options.selectedAuthType;
  }

  getEmbeddingModel(): string | undefined {
    return this.options.embeddingModel;
  }

  getShowMemoryUsage(): boolean {
    return this.options.showMemoryUsage || false;
  }

  getIncludeTools(): string[] {
    return this.options.includeTools || [];
  }

  getGeminiMdFilename(): string | undefined {
    return this.options.geminiMdFilename;
  }

  getAccessibilitySettings(): AccessibilitySettings {
    return this.options.accessibility || { disableLoadingPhrases: false };
  }

  getUsageStatisticsEnabled(): boolean {
    return this.options.usageStatisticsEnabled || false;
  }

  getPreferredEditor(): string | undefined {
    return this.options.preferredEditor;
  }

  getCheckpointingSettings(): CheckpointingSettings {
    return this.options.checkpointing || { enabled: true };
  }

  getAutoConfigureMaxOldSpaceSize(): boolean {
    return this.options.autoConfigureMaxOldSpaceSize || false;
  }

  getFileFiltering(): { respectGitIgnore?: boolean; enableRecursiveFileSearch?: boolean } {
    return this.options.fileFiltering || { respectGitIgnore: true, enableRecursiveFileSearch: true };
  }

  getHideWindowTitle(): boolean {
    return this.options.hideWindowTitle || false;
  }

  getHideTips(): boolean {
    return this.options.hideTips || false;
  }

  getSessionId(): string | undefined {
    return this.options.sessionId;
  }

  // Service getters with lazy initialization
  async getContentGenerator(): Promise<ContentGenerator> {
    if (!this.contentGenerator) {
      const config = this.getContentGeneratorConfig();
      if (!config) {
        throw new Error('No content generator configuration found');
      }

      const generatorConfig = await createContentGeneratorConfig(
        this.getModel(),
        config.apiKey,
        config.authType as any, // Type conversion needed due to enum mismatch
      );

      this.contentGenerator = await createContentGenerator(generatorConfig);
    }
    return this.contentGenerator;
  }

  getFileService(): FileDiscoveryService {
    if (!this.fileService) {
      this.fileService = new FileDiscoveryService(this);
    }
    return this.fileService;
  }

  getGitService(): GitService {
    if (!this.gitService) {
      this.gitService = new GitService(this);
    }
    return this.gitService;
  }

  getToolRegistry(): ToolRegistry {
    if (!this.toolRegistry) {
      this.toolRegistry = new ToolRegistry(this);
    }
    return this.toolRegistry;
  }

  // Setters
  setModel(model: string): void {
    this.options.model = model;
  }

  setDebugMode(debug: boolean): void {
    this.options.debug = debug;
  }

  setApprovalMode(mode: ApprovalMode): void {
    this.options.approvalMode = mode;
  }

  setUserMemory(memory: string): void {
    this.options.userMemory = memory;
  }

  setContentGeneratorConfig(config: ContentGeneratorConfig): void {
    this.options.contentGeneratorConfig = config;
    // Reset content generator to pick up new config
    this.contentGenerator = undefined;
  }

  setTelemetrySettings(settings: TelemetrySettings): void {
    this.options.telemetrySettings = settings;
  }

  setBugCommandSettings(settings: BugCommandSettings): void {
    this.options.bugCommandSettings = settings;
  }

  setMcpServers(servers: Record<string, MCPServerConfig>): void {
    this.options.mcpServers = servers;
  }

  setContextFileName(fileName: string | string[]): void {
    this.options.contextFileName = fileName;
  }

  setCheckpointingEnabled(enabled: boolean): void {
    this.options.checkpointingEnabled = enabled;
  }

  // Additional setters for Settings properties
  setTheme(theme: string): void {
    this.options.theme = theme;
  }

  setSelectedAuthType(authType: AuthType): void {
    this.options.selectedAuthType = authType;
  }

  setEmbeddingModel(model: string): void {
    this.options.embeddingModel = model;
  }

  setIncludeAllFiles(include: boolean): void {
    this.options.includeAllFiles = include;
    // Also set allFiles for backward compatibility
    this.options.allFiles = include;
  }

  setShowMemoryUsage(show: boolean): void {
    this.options.showMemoryUsage = show;
  }

  setTelemetryEnabled(enabled: boolean): void {
    this.options.telemetryEnabled = enabled;
    // Also update telemetrySettings for consistency
    if (!this.options.telemetrySettings) {
      this.options.telemetrySettings = {};
    }
    this.options.telemetrySettings.enabled = enabled;
  }

  setTelemetryTarget(target: TelemetryTarget): void {
    this.options.telemetryTarget = target;
  }

  setTelemetryOtlpEndpoint(endpoint: string): void {
    this.options.telemetryOtlpEndpoint = endpoint;
  }

  setTelemetryLogPrompts(log: boolean): void {
    this.options.telemetryLogPrompts = log;
  }

  setIncludeTools(tools: string[]): void {
    this.options.includeTools = tools;
  }

  setGeminiMdFilename(filename: string): void {
    this.options.geminiMdFilename = filename;
  }

  setAccessibilitySettings(settings: AccessibilitySettings): void {
    this.options.accessibility = settings;
  }

  setUsageStatisticsEnabled(enabled: boolean): void {
    this.options.usageStatisticsEnabled = enabled;
  }

  setPreferredEditor(editor: string): void {
    this.options.preferredEditor = editor;
  }

  setCheckpointingSettings(settings: CheckpointingSettings): void {
    this.options.checkpointing = settings;
  }

  setAutoConfigureMaxOldSpaceSize(configure: boolean): void {
    this.options.autoConfigureMaxOldSpaceSize = configure;
  }

  setFileFiltering(settings: { respectGitIgnore?: boolean; enableRecursiveFileSearch?: boolean }): void {
    this.options.fileFiltering = settings;
  }

  setHideWindowTitle(hide: boolean): void {
    this.options.hideWindowTitle = hide;
  }

  setHideTips(hide: boolean): void {
    this.options.hideTips = hide;
  }

  setSessionId(sessionId: string): void {
    this.options.sessionId = sessionId;
  }

  // Convenience setters that handle both old and new property names
  setQuestion(question: string): void {
    this.options.question = question;
  }

  setSandbox(sandbox: boolean | string): void {
    this.options.sandbox = sandbox;
  }

  setMemory(memory: string): void {
    this.options.userMemory = memory;
  }

  // Utility methods
  isInteractive(): boolean {
    return process.stdin.isTTY && !this.getQuestion();
  }

  shouldUseAllFiles(): boolean {
    return this.getAllFiles();
  }

  shouldUseSandbox(): boolean {
    return Boolean(this.getSandbox());
  }

  clone(): Config {
    return new Config({ ...this.options });
  }

  /**
   * Refresh authentication based on the selected auth type
   * This method handles OAuth2 authentication for LOGIN_WITH_GOOGLE
   * and validates environment variables for other auth types
   */
  async refreshAuth(authType: AuthType): Promise<void> {
    switch (authType) {
      case AuthType.LOGIN_WITH_GOOGLE:
        // Initialize OAuth2 client - this will handle authentication flow
        try {
          await getOauthClient();
        } catch (error) {
          throw new Error(`OAuth2 authentication failed: ${error instanceof Error ? error.message : String(error)}`);
        }
        break;

      case AuthType.USE_GEMINI:
        // Validate Gemini API key
        if (!process.env.GEMINI_API_KEY) {
          throw new Error('GEMINI_API_KEY environment variable not found. Add that to your .env and try again, no reload needed!');
        }
        break;

      case AuthType.USE_VERTEX_AI:
        // Validate Vertex AI configuration
        const hasVertexProjectLocationConfig =
          !!process.env.GOOGLE_CLOUD_PROJECT && !!process.env.GOOGLE_CLOUD_LOCATION;
        const hasGoogleApiKey = !!process.env.GOOGLE_API_KEY;
        if (!hasVertexProjectLocationConfig && !hasGoogleApiKey) {
          throw new Error(
            'Must specify GOOGLE_GENAI_USE_VERTEXAI=true and either:\n' +
            '• GOOGLE_CLOUD_PROJECT and GOOGLE_CLOUD_LOCATION environment variables.\n' +
            '• GOOGLE_API_KEY environment variable (if using express mode).\n' +
            'Update your .env and try again, no reload needed!'
          );
        }
        break;

      default:
        throw new Error(`Invalid auth type: ${authType}`);
    }
  }
}

// Global session ID for telemetry and logging
import { sessionId } from '../utils/session.js';
export { sessionId };

// Logging function for user prompts
export function logUserPrompt(prompt: string): void {
  if (process.env.DEBUG) {
    console.log(`[${sessionId}] User prompt: ${prompt}`);
  }
}

// Error message utility
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}
