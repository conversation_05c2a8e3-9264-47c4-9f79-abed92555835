/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useCallback } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import { Config } from '@arien/arien-cli-core';

interface AppProps {
  config: Config;
  settings: Config; // Now using unified Config instead of LoadedSettings
  startupWarnings?: string[];
}

export const AppWrapper = (props: AppProps) => {
  return <App {...props} />;
};

const App = ({ config, settings, startupWarnings = [] }: AppProps) => {
  // Suppress unused parameter warnings
  void config;
  void settings;
  void startupWarnings;
  // State
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Array<{ type: string; text: string; id: string }>>([]);

  // Hooks
  const { exit } = useApp();

  // Input handling
  const handleInput = useCallback(async (inputText: string) => {
    if (!inputText.trim()) return;

    setInput('');
    setIsLoading(true);

    try {
      // Add user input to messages
      const userMessage = {
        type: 'user',
        text: inputText,
        id: Date.now().toString(),
      };
      setMessages(prev => [...prev, userMessage]);

      // Process input (placeholder for now)
      if (inputText.startsWith('/help')) {
        const helpMessage = {
          type: 'system',
          text: 'Available commands:\n/help - Show this help\n/clear - Clear history\n/exit - Exit application',
          id: (Date.now() + 1).toString(),
        };
        setMessages(prev => [...prev, helpMessage]);
      } else if (inputText.startsWith('/clear')) {
        setMessages([]);
      } else if (inputText.startsWith('/exit')) {
        exit();
      } else {
        // Echo response
        const echoMessage = {
          type: 'assistant',
          text: `Echo: ${inputText}`,
          id: (Date.now() + 1).toString(),
        };
        setMessages(prev => [...prev, echoMessage]);
      }
    } catch (error) {
      const errorMessage = {
        type: 'error',
        text: `Error: ${error}`,
        id: (Date.now() + 1).toString(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [exit]);

  // Handle keyboard input
  useInput((input, key) => {
    if (key.return) {
      handleInput(input);
      setInput('');
    } else if (key.ctrl && input === 'c') {
      exit();
    } else if (!key.ctrl && !key.meta && input.length === 1) {
      setInput(prev => prev + input);
    } else if (key.backspace || key.delete) {
      setInput(prev => prev.slice(0, -1));
    }
  });

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Box borderStyle="single" paddingX={1}>
        <Text color="cyan" bold>
          🤖 Arien CLI - AI Assistant for Coding
        </Text>
      </Box>

      {/* Messages area */}
      <Box flexDirection="column" flexGrow={1} paddingX={1}>
        {messages.map((message) => (
          <Box key={message.id} marginY={0}>
            <Text color={
              message.type === 'user' ? 'green' :
              message.type === 'error' ? 'red' :
              message.type === 'system' ? 'yellow' :
              'white'
            }>
              {message.type === 'user' ? '> ' : ''}
              {message.text}
            </Text>
          </Box>
        ))}

        {isLoading && (
          <Box>
            <Text color="gray">Processing...</Text>
          </Box>
        )}
      </Box>

      {/* Input area */}
      <Box borderStyle="single" paddingX={1}>
        <Text color="blue">{'> '}</Text>
        <Text>{input}</Text>
        <Text color="gray">█</Text>
      </Box>

      {/* Footer */}
      <Box paddingX={1}>
        <Text color="gray" dimColor>
          Type /help for commands, /exit to quit, Ctrl+C to exit
        </Text>
      </Box>
    </Box>
  );
};

export default App;
